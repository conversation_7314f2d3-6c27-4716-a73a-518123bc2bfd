# ---- Build Stage ----
# Use an official Node.js image, which is the standard environment for Next.js builds
FROM node:20-slim as builder

# Install Bun into the Node.js image
RUN npm install -g bun

# Set up the working directory
WORKDIR /app

# Copy all files
COPY . .

# Install dependencies using Bun
RUN bun install --frozen-lockfile

# Generate the Prisma client
RUN bun prisma generate

# Build the Next.js application
RUN bun next build

# ---- Runtime Stage ----
# Start from a fresh, slim Node.js image for the final container
FROM node:20-slim as runner

# Install Bun for running the start command
RUN npm install -g bun

WORKDIR /app

# Set the environment to production
ENV NODE_ENV=production
ENV PORT=3000

# Copy only the necessary artifacts from the builder stage for a small final image
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/public ./public
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/.env.production ./.env.production

# Expose the port
EXPOSE 3000

# Set the command to start the application
CMD ["bun", "next", "start"]