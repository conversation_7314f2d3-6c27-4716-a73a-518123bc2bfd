const http = require("http");
const { exec } = require("child_process");
const fs = require("fs");

const PORT = 4000;

const server = http.createServer((req, res) => {
  if (req.method === "POST" && req.url === "/webhook") {
    let body = "";
    console.log("Received GitHub webhook");

    req.on("data", (chunk) => {
      body += chunk.toString();
    });

    req.on("end", () => {
      const deployCommand =
        "bash /home/<USER>/kivapos-deploy/deploy.sh >> /home/<USER>/kivapos-deploy/deploy.log 2>&1";

      const child = exec(deployCommand, (err) => {
        if (err) {
          console.error("🚨 Deployment failed:", err);

          // Show last 20 lines of the deploy log
          try {
            const log = fs.readFileSync(
              "/home/<USER>/kivapos-deploy/deploy.log",
              "utf8"
            );
            console.error(
              "📝 Last lines from deploy.log:\n" +
                log.split("\n").slice(-20).join("\n")
            );
          } catch (readErr) {
            console.error("❌ Failed to read deploy.log:", readErr);
          }

          res.writeHead(500);
          return res.end("Deployment failed");
        }

        console.log("✅ Webhook-triggered deployment completed successfully.");
        res.writeHead(200);
        res.end("Deployment completed");
      });
    });
  } else {
    res.writeHead(404);
    res.end("Not Found");
  }
});

server.listen(PORT, () => {
  console.log(`Webhook listener running on port ${PORT}`);
});
