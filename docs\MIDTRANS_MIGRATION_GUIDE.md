# Midtrans Payment Gateway Migration Guide

This document provides comprehensive instructions for configuring and using the new Midtrans payment gateway integration that has replaced the previous Xendit implementation.

## Overview

The application has been successfully migrated from Xendit to Midtrans payment gateway while maintaining all existing functionality. The migration includes:

- ✅ Complete Xendit code commented out (preserved for reference)
- ✅ New Midtrans integration with comprehensive logging
- ✅ Snap.js integration for seamless payment experience
- ✅ Webhook handling for payment status updates
- ✅ Error handling and debugging capabilities

## Environment Configuration

### Required Environment Variables

Add the following environment variables to your `.env` file:

```bash
# Midtrans Payment Gateway Configuration
MIDTRANS_SERVER_KEY="your_midtrans_server_key_here"
MIDTRANS_CLIENT_KEY="your_midtrans_client_key_here"
NEXT_PUBLIC_MIDTRANS_CLIENT_KEY="your_midtrans_client_key_here"
MIDTRANS_IS_PRODUCTION="false"  # Set to "true" for production
MIDTRANS_WEBHOOK_URL="https://yourdomain.com/api/webhooks/midtrans"
```

### Getting Midtrans Credentials

1. **Register/Login to Midtrans Dashboard**

   - Sandbox: https://dashboard.sandbox.midtrans.com
   - Production: https://dashboard.midtrans.com

2. **Get API Keys**

   - Go to Settings → Access Keys
   - Copy your Server Key and Client Key
   - Use the same Client Key for both `MIDTRANS_CLIENT_KEY` and `NEXT_PUBLIC_MIDTRANS_CLIENT_KEY`

3. **Configure Webhook URL**

   - Go to Settings → Configuration
   - Set Payment Notification URL to: `https://yourdomain.com/api/webhooks/midtrans`
   - Set Finish Redirect URL to: `https://yourdomain.com/dashboard/settings/billing/success`
   - Set Unfinish Redirect URL to: `https://yourdomain.com/dashboard/settings/billing/pending`
   - Set Error Redirect URL to: `https://yourdomain.com/dashboard/settings/billing/failed`

   **💡 Tip**: Use the built-in Redirection Settings page at `/dashboard/settings/redirections` to easily copy all required URLs.

## Installation

The migration requires the `midtrans-client` package:

```bash
bun add midtrans-client
```

## New Payment Flow

### 1. Subscription Creation

- User selects a subscription plan
- System creates Midtrans transaction using Snap API
- Returns both redirect URL and Snap token

### 2. Payment Processing

- **Primary**: Uses Midtrans Snap.js for seamless popup payment
- **Fallback**: Redirects to Midtrans hosted payment page
- Supports all Midtrans payment methods (Credit Card, Bank Transfer, E-wallets, etc.)

### 3. Payment Completion

- Webhook receives payment status updates
- System updates payment and subscription status
- User is redirected to appropriate success/failure page

## Key Features

### Readable Order IDs

The system now generates human-readable order IDs for better transaction tracking:

- Format: `SUB-{PLAN}-{TIMESTAMP}-{RANDOM}`
- Example: `SUB-PRO-**************-A7B9C2`
- Makes it easier to identify transactions in Midtrans Dashboard

### Enhanced Transaction Information

Midtrans Snap now displays comprehensive transaction details:

- **Merchant Name**: KivaPos
- **Item Category**: Subscription Plan
- **Custom Fields**: Plan name, date, customer email
- **Billing Address**: Complete customer information
- **Expiry Time**: 24 hours from creation

### Comprehensive Logging

All payment operations include detailed console logging with emojis for easy identification:

- 🚀 Transaction creation
- 📨 Webhook processing
- ✅ Successful operations
- ❌ Error conditions
- 🔍 Status checking
- 🔄 Status updates

### Error Handling

- Network failure recovery
- Invalid signature detection
- Payment timeout handling
- Comprehensive error logging

### Security

- Webhook signature verification
- Server-side transaction status validation
- Secure API key handling

## Testing

### Sandbox Testing

1. Set `MIDTRANS_IS_PRODUCTION="false"`
2. Use Midtrans sandbox credentials
3. Test with Midtrans simulator: https://simulator.sandbox.midtrans.com/

### Test Payment Methods

- **Credit Card**: Use test card numbers from Midtrans documentation
- **Bank Transfer**: Use virtual account simulator
- **E-wallets**: Use test accounts provided by Midtrans

### Webhook Testing

1. Use ngrok or similar tool for local development
2. Set webhook URL to your public endpoint
3. Monitor console logs for webhook processing

## Monitoring and Debugging

### Console Logs

The system provides extensive logging. Look for these patterns:

```
🚀 [SUBSCRIPTION] Creating Midtrans transaction for user: {...}
📨 [MIDTRANS WEBHOOK] Received webhook request
✅ [PAYMENT CHECK] Payment status updated to COMPLETED: {...}
❌ [MIDTRANS] Error creating transaction: {...}
```

### Webhook History

- Check Midtrans Dashboard → Settings → Configuration → See History
- Monitor webhook delivery status and responses

### Payment Status Flow

1. **PENDING**: Payment created, awaiting customer action
2. **COMPLETED**: Payment successful, subscription activated
3. **EXPIRED**: Payment expired or failed
4. **FAILED**: Payment explicitly failed

## Troubleshooting

### Common Issues

1. **Snap.js not loading**

   - Check `NEXT_PUBLIC_MIDTRANS_CLIENT_KEY` is set correctly
   - Verify script is loading in browser network tab
   - Ensure correct sandbox/production URL

2. **Webhook not receiving**

   - Verify webhook URL is publicly accessible
   - Check Midtrans Dashboard webhook history
   - Ensure webhook endpoint returns HTTP 200

3. **Payment status not updating**

   - Check webhook signature verification
   - Monitor console logs for errors
   - Verify database payment record exists

4. **Invalid signature errors**
   - Ensure `MIDTRANS_SERVER_KEY` matches dashboard
   - Check webhook payload format
   - Verify signature calculation logic

### Debug Mode

Enable detailed logging by monitoring browser console and server logs. All operations include contextual information for debugging.

## Migration Notes

### What Changed

- **Payment Gateway**: Xendit → Midtrans
- **Integration Method**: Redirect → Snap.js popup (with redirect fallback)
- **Webhook Endpoint**: `/api/webhooks/xendit` → `/api/webhooks/midtrans`
- **Status Mapping**: Xendit statuses mapped to Midtrans equivalents

### What Stayed the Same

- Database schema (minimal changes)
- User interface flow
- Subscription management logic
- Payment status checking API

### Preserved Code

All Xendit code has been commented out and preserved for reference. You can find it in:

- `src/lib/xendit.ts` (commented)
- `src/app/api/webhooks/xendit/route.ts` (commented)

## Production Deployment

### Pre-deployment Checklist

- [ ] Set `MIDTRANS_IS_PRODUCTION="true"`
- [ ] Use production Midtrans credentials
- [ ] Update webhook URL to production domain
- [ ] Test payment flow in production environment
- [ ] Monitor webhook delivery and logs

### Post-deployment Verification

1. Test complete payment flow
2. Verify webhook processing
3. Check subscription activation
4. Monitor error logs
5. Test payment status checking

## Support

For issues related to:

- **Midtrans API**: Contact Midtrans support or check their documentation
- **Integration Issues**: Check console logs and webhook history
- **Application Logic**: Review the comprehensive logging output

## API Endpoints

### New Endpoints

- `POST /api/webhooks/midtrans` - Midtrans webhook handler
- `POST /api/payments/check/[id]` - Updated for Midtrans status checking

### Modified Endpoints

- `POST /api/subscriptions` - Now creates Midtrans transactions
- All payment-related endpoints updated to use Midtrans

The migration is complete and production-ready with comprehensive logging and error handling for troubleshooting any payment-related issues.
