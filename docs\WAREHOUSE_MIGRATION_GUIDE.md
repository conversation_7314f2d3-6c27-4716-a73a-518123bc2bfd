# Warehouse Management System Migration Guide

This guide will help you migrate your database to support the new warehouse management system.

## Overview

The warehouse management system adds the following new features:
- Multiple warehouse locations
- Per-warehouse stock tracking
- Stock transfers between warehouses
- Stock movement history
- Warehouse-specific purchase and sales transactions

## Database Changes

### New Tables Added:
1. **warehouses** - Store warehouse information
2. **warehouse_stocks** - Track stock levels per product per warehouse
3. **stock_movements** - Log all stock movements (purchases, sales, transfers, adjustments)

### Modified Tables:
1. **sales** - Added `warehouseId` field (replaces string `warehouse` field)
2. **purchases** - Added `warehouseId` field
3. **products** - Added relationships to warehouse stocks and movements

## Migration Steps

### Step 1: Backup Your Database
Before proceeding, create a backup of your current database:

```bash
# For PostgreSQL
pg_dump your_database_name > backup_before_warehouse_migration.sql

# For other databases, use appropriate backup commands
```

### Step 2: Generate and Apply Prisma Migration

1. Generate the migration:
```bash
bun prisma migrate dev --name add-warehouse-management
```

2. If you encounter any issues, you can reset the database (⚠️ **This will delete all data**):
```bash
bun prisma migrate reset
```

### Step 3: Create Default Warehouse (Optional)

After the migration, you may want to create a default warehouse for existing data:

```bash
bun src/scripts/create-default-warehouse.ts
```

### Step 4: Migrate Existing Stock Data (Optional)

If you have existing products with stock, you can migrate them to the default warehouse:

```bash
bun src/scripts/migrate-existing-stock.ts
```

## Manual Migration (Alternative)

If you prefer to run the migration manually, execute these SQL commands:

```sql
-- Add warehouse tables
CREATE TABLE "warehouses" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "address" TEXT,
    "phone" TEXT,
    "email" TEXT,
    "contactName" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "isDefault" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" TEXT NOT NULL,

    CONSTRAINT "warehouses_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "warehouse_stocks" (
    "id" TEXT NOT NULL,
    "quantity" INTEGER NOT NULL DEFAULT 0,
    "minLevel" INTEGER DEFAULT 0,
    "maxLevel" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "productId" TEXT NOT NULL,
    "warehouseId" TEXT NOT NULL,

    CONSTRAINT "warehouse_stocks_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "stock_movements" (
    "id" TEXT NOT NULL,
    "type" "StockMovementType" NOT NULL,
    "quantity" INTEGER NOT NULL,
    "previousStock" INTEGER NOT NULL,
    "newStock" INTEGER NOT NULL,
    "reference" TEXT,
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "productId" TEXT NOT NULL,
    "warehouseId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,

    CONSTRAINT "stock_movements_pkey" PRIMARY KEY ("id")
);

-- Create enum for stock movement types
CREATE TYPE "StockMovementType" AS ENUM ('PURCHASE', 'SALE', 'TRANSFER_IN', 'TRANSFER_OUT', 'ADJUSTMENT', 'RETURN', 'DAMAGE');

-- Add warehouse relationships to existing tables
ALTER TABLE "sales" ADD COLUMN "warehouseId" TEXT;
ALTER TABLE "purchases" ADD COLUMN "warehouseId" TEXT;

-- Create indexes
CREATE UNIQUE INDEX "warehouses_userId_name_key" ON "warehouses"("userId", "name");
CREATE INDEX "warehouses_userId_idx" ON "warehouses"("userId");
CREATE UNIQUE INDEX "warehouse_stocks_productId_warehouseId_key" ON "warehouse_stocks"("productId", "warehouseId");
CREATE INDEX "warehouse_stocks_productId_idx" ON "warehouse_stocks"("productId");
CREATE INDEX "warehouse_stocks_warehouseId_idx" ON "warehouse_stocks"("warehouseId");
CREATE INDEX "stock_movements_productId_idx" ON "stock_movements"("productId");
CREATE INDEX "stock_movements_warehouseId_idx" ON "stock_movements"("warehouseId");
CREATE INDEX "stock_movements_userId_idx" ON "stock_movements"("userId");
CREATE INDEX "stock_movements_createdAt_idx" ON "stock_movements"("createdAt");
CREATE INDEX "sales_warehouseId_idx" ON "sales"("warehouseId");
CREATE INDEX "purchases_warehouseId_idx" ON "purchases"("warehouseId");

-- Add foreign key constraints
ALTER TABLE "warehouses" ADD CONSTRAINT "warehouses_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "warehouse_stocks" ADD CONSTRAINT "warehouse_stocks_productId_fkey" FOREIGN KEY ("productId") REFERENCES "products"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "warehouse_stocks" ADD CONSTRAINT "warehouse_stocks_warehouseId_fkey" FOREIGN KEY ("warehouseId") REFERENCES "warehouses"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "stock_movements" ADD CONSTRAINT "stock_movements_productId_fkey" FOREIGN KEY ("productId") REFERENCES "products"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "stock_movements" ADD CONSTRAINT "stock_movements_warehouseId_fkey" FOREIGN KEY ("warehouseId") REFERENCES "warehouses"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "stock_movements" ADD CONSTRAINT "stock_movements_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "sales" ADD CONSTRAINT "sales_warehouseId_fkey" FOREIGN KEY ("warehouseId") REFERENCES "warehouses"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "purchases" ADD CONSTRAINT "purchases_warehouseId_fkey" FOREIGN KEY ("warehouseId") REFERENCES "warehouses"("id") ON DELETE SET NULL ON UPDATE CASCADE;
```

## Post-Migration Steps

### 1. Update Prisma Client
After the migration, regenerate the Prisma client:

```bash
bun prisma generate
```

### 2. Test the Application
1. Start your application: `bun dev`
2. Navigate to any of the warehouse tabs in Products, Purchases, or Sales pages
3. Try creating a new warehouse
4. Verify that the warehouse management features are working

### 3. Create Your First Warehouse
1. Go to Products > Gudang tab
2. Click "Tambah Gudang" to create your first warehouse
3. Set it as the default warehouse for transactions

## Troubleshooting

### Migration Fails
If the migration fails:
1. Check the error message carefully
2. Ensure your database is running and accessible
3. Make sure you have the latest Prisma version: `bun add prisma@latest`
4. Try resetting the migration: `bun prisma migrate reset` (⚠️ **This will delete all data**)

### Missing Warehouse Data
If you don't see warehouse options in forms:
1. Make sure you've created at least one warehouse
2. Check that the warehouse is marked as active
3. Verify the database migration completed successfully

### Stock Data Issues
If existing stock data is not showing:
1. Run the stock migration script: `bun src/scripts/migrate-existing-stock.ts`
2. Manually create warehouse stock entries for existing products

## Rollback (Emergency Only)

If you need to rollback the migration:

```sql
-- Remove new columns
ALTER TABLE "sales" DROP COLUMN "warehouseId";
ALTER TABLE "purchases" DROP COLUMN "warehouseId";

-- Drop new tables
DROP TABLE "stock_movements";
DROP TABLE "warehouse_stocks";
DROP TABLE "warehouses";

-- Drop enum
DROP TYPE "StockMovementType";
```

⚠️ **Warning**: Rolling back will permanently delete all warehouse data!

## Support

If you encounter any issues during migration:
1. Check the application logs for detailed error messages
2. Verify your database connection and permissions
3. Ensure all dependencies are up to date
4. Create an issue in the project repository with the error details
