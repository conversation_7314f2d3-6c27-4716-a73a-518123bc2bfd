# Excel Import Functionality for Products

## Overview

This document describes the Excel import functionality implemented for products in the Kasir Online application.

## Features Implemented

### 1. Import Dialog Enhancement

- **Location**: `src/components/pages/dashboard/reports/components/ExportImportTools.tsx`
- **Enhanced UI**: Modern dialog with clear instructions and progress indicators
- **Template Download**: One-click download of Excel template
- **File Validation**: Size (10MB max) and format (.xlsx, .xls) validation
- **Progress Tracking**: Real-time import progress with visual feedback

### 2. Excel Template Generation

- **Location**: `src/utils/importTemplate.ts`
- **Template Structure**: Matches export format but excludes ID and Total Stok columns
- **Sample Data**: Includes example rows to guide users
- **Instructions Sheet**: Comprehensive usage guide with format specifications
- **Styling**: Professional formatting with color-coded required/optional fields

### 3. Import Processing Logic

- **Location**: `src/actions/import/products.ts`
- **Database Safety**: Uses transactions for data consistency
- **Related Entities**: Auto-creates categories and units if they don't exist
- **Validation**: Comprehensive data validation with detailed error messages
- **Uniqueness**: Handles duplicate SKUs and barcodes automatically
- **User Association**: Links all imported products to current user account

## Template Structure

### Included Columns

1. **Nama Produk** (Required) - Product name
2. **Kode Produk (SKU)** (Optional) - Product code
3. **Barcode** (Optional) - Product barcode
4. **Kategori** (Optional) - Product category
5. **Satuan** (Optional) - Unit of measurement
6. **Harga Beli** (Optional) - Purchase price
7. **Harga Jual** (Required) - Selling price
8. **Harga Grosir** (Optional) - Wholesale price
9. **Harga Diskon** (Optional) - Discount price
10. **Tag Produk** (Optional) - Product tags (comma-separated)
11. **Varian Warna** (Optional) - Color variants (comma-separated)

### Excluded Columns (Auto-Generated)

- **ID Product**: Generated automatically by system
- **Total Stok**: Set to 0 initially (managed through purchases)

## Safety Measures

### Data Validation

- Required field validation (Nama Produk, Harga Jual)
- Data type validation (numbers for prices)
- String length limits (255 characters max)
- Array limits (10 tags max, 5 variants max)

### Database Safety

- Transaction-based processing for rollback capability
- Duplicate handling for SKU and barcode
- User isolation (products only visible to importing user)
- Error logging and detailed error messages

### System Protection

- File size limit (10MB)
- Row count limit (1000 products per import)
- Input sanitization to prevent injection attacks
- Proper error handling with graceful degradation

## User Experience

### Import Process

1. User clicks "Import Produk" button
2. Dialog opens with instructions and template download option
3. User downloads template and fills in product data
4. User uploads completed Excel file
5. System validates and processes data with progress indicator
6. Results summary shows created items and any errors
7. User can close dialog and see imported products in system

### Error Handling

- Clear validation messages for each row
- Summary of successful imports vs errors
- Detailed error descriptions for troubleshooting
- Graceful handling of partial failures

## Technical Implementation

### Key Files

- `src/actions/import/products.ts` - Main import logic
- `src/utils/imports/` - Template generation (organized by module)
  - `src/utils/imports/products.ts` - Product import templates
  - `src/utils/imports/sales.ts` - Sales import templates
  - `src/utils/imports/purchases.ts` - Purchase import templates
  - `src/utils/imports/services.ts` - Service import templates
  - `src/utils/imports/contacts.ts` - Customer/Supplier import templates
  - `src/utils/imports/shared.ts` - Shared utilities and styling
- `src/components/pages/dashboard/reports/components/ExportImportTools.tsx` - UI components
- `src/test/import-template-test.ts` - Testing utilities

### Dependencies

- `xlsx` - Excel file processing
- `xlsx-js-style` - Excel styling and formatting
- Prisma - Database operations with transactions
- React - UI components and state management

### Database Models Used

- `Product` - Main product data
- `Category` - Product categories (auto-created)
- `Unit` - Measurement units (auto-created)
- `ProductVariant` - Color variants (auto-created)

## Future Enhancements

### Planned Features

- Import for Sales data
- Import for Purchase data
- Bulk update functionality
- Import history and audit trail
- Advanced validation rules
- Custom field mapping

### Potential Improvements

- Drag & drop file upload
- Preview before import
- Undo import functionality
- Import scheduling
- Email notifications for large imports
- Multi-language template support

## Usage Guidelines

### For Users

1. Always download the latest template before importing
2. Fill required fields (marked in red in template)
3. Follow format examples provided in template
4. Remove example rows before importing
5. Keep file size under 10MB
6. Maximum 1000 products per import

### For Developers

1. All import functions use database transactions
2. Validate data before processing
3. Provide detailed error messages
4. Log import activities for debugging
5. Test with various data scenarios
6. Handle edge cases gracefully

## Testing

### Test Scenarios

- Valid data import
- Invalid data handling
- Large file processing
- Duplicate data handling
- Network interruption recovery
- Database constraint violations

### Test Files

- `src/test/import-template-test.ts` - Template generation tests
- Manual testing with sample Excel files
- Edge case testing with malformed data

## Support

### Common Issues

1. **File format errors**: Ensure .xlsx or .xls format
2. **Size limit exceeded**: Split large files into smaller batches
3. **Required field missing**: Check Nama Produk and Harga Jual
4. **Invalid price format**: Use numbers without formatting
5. **Category creation**: New categories are created automatically

### Troubleshooting

- Check browser console for detailed error messages
- Verify Excel file format and structure
- Ensure all required fields are filled
- Contact support for persistent issues
