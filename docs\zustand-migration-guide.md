# Zustand State Management Migration Guide

This guide explains how to migrate from local React state to Zustand global state management in the KivaPOS application.

## Overview

Zustand has been implemented to solve common state management issues:
- **Prop drilling** - Passing props through multiple component levels
- **State duplication** - Similar state logic repeated across components
- **Persistence** - Automatic localStorage integration
- **Performance** - Optimized re-renders with selectors
- **Developer Experience** - Better TypeScript support and debugging

## Store Structure

```
src/stores/
├── index.ts              # Central exports and utilities
├── layout-store.ts       # Sidebar, theme, and layout preferences
├── filter-store.ts       # Filter state for all modules
├── loading-store.ts      # Loading states and progress tracking
└── dashboard-store.ts    # Dashboard data and statistics
```

## Available Stores

### 1. Layout Store (`layout-store.ts`)
Manages sidebar state, theme preferences, and responsive behavior.

```tsx
import { useIsSidebarCollapsed, useToggleSidebar } from '@/stores';

const MyComponent = () => {
  const isCollapsed = useIsSidebarCollapsed();
  const toggleSidebar = useToggleSidebar();
  
  return (
    <button onClick={toggleSidebar}>
      {isCollapsed ? 'Expand' : 'Collapse'} Sidebar
    </button>
  );
};
```

### 2. Filter Store (`filter-store.ts`)
Centralized filter management for all modules (sales, purchases, products, services, reports).

```tsx
import { useSalesFilters, useSetSalesFilters } from '@/stores';

const SalesPage = () => {
  const filters = useSalesFilters();
  const setFilters = useSetSalesFilters();
  
  const handleDateChange = (dateRange: string) => {
    setFilters({ dateRange });
  };
  
  return <DateRangePicker value={filters.dateRange} onChange={handleDateChange} />;
};
```

### 3. Loading Store (`loading-store.ts`)
Manages loading states and progress tracking across the application.

```tsx
import { useIsModuleLoading, useSetModuleLoading, useImportProgress } from '@/stores';

const ImportComponent = () => {
  const isLoading = useIsModuleLoading('products');
  const setLoading = useSetModuleLoading();
  const progress = useImportProgress();
  
  const handleImport = async () => {
    setLoading('products', true);
    // ... import logic
    setLoading('products', false);
  };
  
  return (
    <div>
      {isLoading && <ProgressBar value={progress} />}
      <button onClick={handleImport}>Import Products</button>
    </div>
  );
};
```

### 4. Dashboard Store (`dashboard-store.ts`)
Manages dashboard data, statistics, and automatic refresh logic.

```tsx
import { useSummaryData, useRefreshDashboard, useDashboardLoading } from '@/stores';

const DashboardSummary = () => {
  const summaryData = useSummaryData();
  const refreshData = useRefreshDashboard();
  const isLoading = useDashboardLoading();
  
  useEffect(() => {
    if (!summaryData) {
      refreshData();
    }
  }, [summaryData, refreshData]);
  
  if (isLoading) return <Skeleton />;
  
  return (
    <div>
      <h2>Sales This Month: {summaryData?.salesThisMonth}</h2>
      <button onClick={refreshData}>Refresh</button>
    </div>
  );
};
```

## Migration Examples

### Before: Local State Management
```tsx
// ❌ Old approach - Local state with prop drilling
const ParentComponent = () => {
  const [filters, setFilters] = useState({ dateRange: 'last30days' });
  const [isLoading, setIsLoading] = useState(false);
  
  return (
    <div>
      <FilterComponent filters={filters} onFiltersChange={setFilters} />
      <DataComponent filters={filters} isLoading={isLoading} />
      <ExportComponent filters={filters} setLoading={setIsLoading} />
    </div>
  );
};
```

### After: Zustand Store
```tsx
// ✅ New approach - Zustand store
const ParentComponent = () => {
  return (
    <div>
      <FilterComponent />
      <DataComponent />
      <ExportComponent />
    </div>
  );
};

const FilterComponent = () => {
  const filters = useSalesFilters();
  const setFilters = useSetSalesFilters();
  // No props needed!
};

const DataComponent = () => {
  const filters = useSalesFilters();
  const isLoading = useIsModuleLoading('sales');
  // Direct access to store state
};
```

## Migration Steps

### Step 1: Install Dependencies
```bash
bun add zustand
```

### Step 2: Identify Components to Migrate
Look for components with:
- Complex useState logic
- Props being passed through multiple levels
- Similar state patterns across components
- localStorage usage
- Loading state management

### Step 3: Choose the Right Store
- **Layout Store**: Sidebar, theme, responsive behavior
- **Filter Store**: Search, date ranges, pagination, sorting
- **Loading Store**: Loading states, progress tracking
- **Dashboard Store**: Dashboard-specific data and statistics

### Step 4: Replace Local State
```tsx
// Before
const [isCollapsed, setIsCollapsed] = useState(false);

// After
const isCollapsed = useIsSidebarCollapsed();
const toggleSidebar = useToggleSidebar();
```

### Step 5: Remove Props
```tsx
// Before
<ChildComponent isLoading={isLoading} onToggle={handleToggle} />

// After
<ChildComponent /> // Child component uses store directly
```

### Step 6: Update Child Components
```tsx
// Child component before
const ChildComponent = ({ isLoading, onToggle }) => {
  // ...
};

// Child component after
const ChildComponent = () => {
  const isLoading = useIsModuleLoading('products');
  const toggleSidebar = useToggleSidebar();
  // ...
};
```

## Best Practices

### 1. Use Selectors for Performance
```tsx
// ✅ Good - Only re-renders when isCollapsed changes
const isCollapsed = useIsSidebarCollapsed();

// ❌ Bad - Re-renders on any layout store change
const { isCollapsed } = useLayoutStore();
```

### 2. Batch Related Updates
```tsx
// ✅ Good - Single store update
setFilters({ 
  dateRange: 'custom',
  startDate: new Date(),
  endDate: new Date(),
});

// ❌ Bad - Multiple store updates
setFilters({ dateRange: 'custom' });
setFilters({ startDate: new Date() });
setFilters({ endDate: new Date() });
```

### 3. Use Store Actions for Complex Logic
```tsx
// ✅ Good - Logic in store action
const refreshDashboard = useRefreshDashboard();

// ❌ Bad - Logic in component
const handleRefresh = async () => {
  setLoading(true);
  const data = await fetchData();
  setData(data);
  setLoading(false);
};
```

### 4. Persist Important State
```tsx
// Stores automatically persist to localStorage
// No manual localStorage.setItem() needed
```

## Testing with Zustand

```tsx
import { renderHook, act } from '@testing-library/react';
import { useLayoutStore } from '@/stores';

test('should toggle sidebar', () => {
  const { result } = renderHook(() => useLayoutStore());
  
  act(() => {
    result.current.toggleSidebar();
  });
  
  expect(result.current.isCollapsed).toBe(true);
});
```

## Debugging

1. **Zustand DevTools**: Install the browser extension for state inspection
2. **Console Logging**: Stores expose getState() for debugging
3. **React DevTools**: Zustand hooks show up in component tree

```tsx
// Debug current state
console.log('Current layout state:', useLayoutStore.getState());
```

## Common Pitfalls

1. **Don't overuse stores** - Keep local UI state local
2. **Use selectors** - Avoid subscribing to entire store
3. **Batch updates** - Combine related state changes
4. **Handle SSR** - Initialize stores properly on client-side

## Migration Checklist

- [ ] Identify components with shared state
- [ ] Choose appropriate store for each use case
- [ ] Replace useState with store selectors
- [ ] Remove prop drilling
- [ ] Update child components to use stores
- [ ] Test functionality parity
- [ ] Remove unused local state
- [ ] Update TypeScript types if needed
- [ ] Test persistence behavior
- [ ] Verify performance improvements

## Next Steps

After migration, consider:
1. Adding more specific stores for complex domains
2. Implementing optimistic updates
3. Adding middleware for logging/analytics
4. Creating custom hooks for common patterns
