{"name": "kivapos", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test:midtrans": "bun run scripts/test-midtrans.js", "check": "tsc --noEmit && next lint"}, "dependencies": {"@auth/core": "^0.38.0", "@auth/prisma-adapter": "^2.8.0", "@aws-sdk/client-s3": "^3.864.0", "@aws-sdk/s3-request-presigner": "^3.864.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^4.1.3", "@neondatabase/serverless": "^1.0.0", "@prisma/adapter-neon": "^6.5.0", "@prisma/client": "^6.5.0", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.6.5", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.488.0", "midtrans-client": "^1.4.3", "motion": "^12.23.6", "next": "^15.3.0", "next-auth": "^5.0.0-beta.25", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "react": "^19.1.0", "react-day-picker": "8.10.1", "react-dom": "^19.1.0", "react-hook-form": "^7.55.0", "react-icons": "^5.5.0", "recharts": "^2.15.2", "resend": "^4.2.0", "sonner": "^2.0.2", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "xendit-node": "^6.3.0", "xlsx": "^0.18.5", "xlsx-js-style": "^1.2.0", "zod": "^3.24.2", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "^15.3.0", "prisma": "^6.5.0", "tailwindcss": "^4", "typescript": "^5"}}