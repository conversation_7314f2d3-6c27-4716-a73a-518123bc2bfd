"use server";

import { db } from "@/lib/prisma";

export async function verifyEmailForDevelopment(email: string) {
  try {
    console.log("🔧 DEV: Verifying email for development:", email);

    const user = await db.user.findUnique({
      where: { email },
    });

    if (!user) {
      console.log("❌ DEV: User not found");
      return { success: false, message: "User not found" };
    }

    if (user.emailVerified) {
      console.log("✅ DEV: Email already verified");
      return { success: true, message: "<PERSON><PERSON> already verified" };
    }

    // Update the user to mark email as verified
    const updatedUser = await db.user.update({
      where: { email },
      data: {
        emailVerified: new Date(),
      },
    });

    console.log("✅ DEV: Email verified successfully");
    return {
      success: true,
      message: "Email verified successfully",
      user: {
        id: updatedUser.id,
        email: updatedUser.email,
        emailVerified: updatedUser.emailVerified,
      },
    };
  } catch (error) {
    console.error("❌ DEV: Error verifying email:", error);
    return { success: false, message: "Error verifying email", error };
  }
}

export async function listUnverifiedUsers() {
  try {
    console.log("📋 DEV: Listing unverified users");

    const users = await db.user.findMany({
      where: {
        emailVerified: null,
      },
      select: {
        id: true,
        name: true,
        email: true,
        createdAt: true,
      },
      take: 10,
    });

    console.log("📋 DEV: Found unverified users:", users.length);
    return { success: true, users, count: users.length };
  } catch (error) {
    console.error("❌ DEV: Error listing unverified users:", error);
    return { success: false, message: "Error listing users", error };
  }
}
