"use server";

import { db } from "@/lib/prisma";
import {
  startOfDay,
  endOfDay,
  subDays,
  startOfMonth,
  endOfMonth,
  subMonths,
  formatISO,
  eachMonthOfInterval,
  subYears,
  format,
  formatDistanceToNowStrict,
} from "date-fns";
import { id } from "date-fns/locale"; // For Indonesian locale formatting
import { Prisma } from "@prisma/client";

// Helper function to calculate percentage change
const calculatePercentageChange = (
  current: number,
  previous: number
): number => {
  if (previous === 0) {
    return current > 0 ? 100 : 0; // Avoid division by zero, return 100% if previous was 0 and current is positive
  }
  return Math.round(((current - previous) / previous) * 100);
};

// --- Type Definitions for New Data ---

// Type for Sales Chart Data Point
export type SalesChartDataPoint = {
  name: string; // Month abbreviation e.g., "Jan"
  total: number;
};

// Type for Product Distribution Data Point
export type ProductDistributionDataPoint = {
  name: string; // Category name
  value: number; // Count or value
};

// Type for Recent Transaction Item (legacy - keeping for compatibility)
export type RecentTransactionItem = {
  id: string; // Sale ID or Invoice Number
  time: string; // Formatted time ago (e.g., "10 menit lalu")
  amount: string; // Formatted currency string
  status: "success" | "pending" | "failed"; // Assuming Sale has a status or can be inferred
};

// Enhanced Activity Types
export type ActivityType = "product" | "purchase" | "sale";

export type RecentActivityItem = {
  id: string;
  type: ActivityType;
  title: string;
  description: string;
  time: string; // Formatted time ago
  amount?: string; // Optional formatted currency
  icon: string; // Icon identifier
  color: string; // Color theme
  href?: string; // Optional link
};

// Import the getEffectiveUserId function
import { getEffectiveUserId } from "@/lib/get-effective-user-id";

// --- Server Actions ---

export const getDashboardSummary = async () => {
  try {
    // Get the effective user ID (owner ID if employee, user's own ID otherwise)
    const userId = await getEffectiveUserId();

    // Return early if user is not authenticated
    if (!userId) {
      return {
        success: false,
        error: "Tidak terautentikasi!",
      };
    }

    const now = new Date();

    // --- Sales Data ---
    const todayStart = startOfDay(now);
    const todayEnd = endOfDay(now);
    const yesterdayStart = startOfDay(subDays(now, 1));
    const yesterdayEnd = endOfDay(subDays(now, 1));

    const salesTodayResult = await db.sale.aggregate({
      _sum: {
        totalAmount: true,
      },
      where: {
        userId: userId, // Filter by user ID
        saleDate: {
          gte: todayStart,
          lte: todayEnd,
        },
      },
    });
    const salesToday = salesTodayResult._sum.totalAmount ?? 0;

    const salesYesterdayResult = await db.sale.aggregate({
      _sum: {
        totalAmount: true,
      },
      where: {
        userId: userId, // Filter by user ID
        saleDate: {
          gte: yesterdayStart,
          lte: yesterdayEnd,
        },
      },
    });
    const salesYesterday = salesYesterdayResult._sum.totalAmount ?? 0;
    // Convert Decimal to number before calculating change
    const salesChange = calculatePercentageChange(
      Number(salesToday),
      Number(salesYesterday)
    );

    // --- Product Data ---
    // Assuming 'active' means all products for now
    const totalProducts = await db.product.count({
      where: {
        userId: userId, // Filter by user ID
      },
    });
    // New products in the last 7 days
    const sevenDaysAgo = subDays(now, 7);
    const newProductsCount = await db.product.count({
      where: {
        userId: userId, // Filter by user ID
        createdAt: {
          gte: sevenDaysAgo,
        },
      },
    });

    // --- Customer Data ---
    const totalCustomers = await db.customer.count({
      where: {
        userId: userId, // Filter by user ID
      },
    });
    // New customers in the last 7 days
    const newCustomersCount = await db.customer.count({
      where: {
        userId: userId, // Filter by user ID
        createdAt: {
          gte: sevenDaysAgo,
        },
      },
    });

    // --- Purchase Data ---
    const thisMonthStart = startOfMonth(now);
    const thisMonthEnd = endOfMonth(now);
    const lastMonthStart = startOfMonth(subMonths(now, 1));
    const lastMonthEnd = endOfMonth(subMonths(now, 1));

    const purchasesThisMonthResult = await db.purchase.aggregate({
      _sum: {
        totalAmount: true,
      },
      where: {
        userId: userId, // Filter by user ID
        purchaseDate: {
          gte: thisMonthStart,
          lte: thisMonthEnd,
        },
      },
    });
    const purchasesThisMonth = purchasesThisMonthResult._sum.totalAmount ?? 0;

    const purchasesLastMonthResult = await db.purchase.aggregate({
      _sum: {
        totalAmount: true,
      },
      where: {
        userId: userId, // Filter by user ID
        purchaseDate: {
          gte: lastMonthStart,
          lte: lastMonthEnd,
        },
      },
    });
    const purchasesLastMonth = purchasesLastMonthResult._sum.totalAmount ?? 0;
    // Convert Decimal to number before calculating change
    const purchasesChange = calculatePercentageChange(
      Number(purchasesThisMonth),
      Number(purchasesLastMonth)
    );

    return {
      success: true,
      // Ensure only plain objects/primitives are returned
      data: {
        salesToday: Number(salesToday), // Convert Decimal to number
        salesChange, // Already a number
        totalProducts, // Already a number
        newProductsCount, // Already a number
        totalCustomers, // Already a number
        newCustomersCount, // Already a number
        purchasesThisMonth: Number(purchasesThisMonth), // Convert Decimal to number
        purchasesChange, // Already a number
      },
    };
  } catch (error) {
    console.error("Error fetching dashboard summary:", error);
    return {
      success: false,
      error: "Gagal mengambil data ringkasan dashboard.",
    };
  }
};

// --- Action to get Sales Chart Data (Last 6 Months) ---
export const getSalesChartData = async (): Promise<{
  success: boolean;
  data?: SalesChartDataPoint[];
  error?: string;
}> => {
  try {
    // Get the effective user ID (owner ID if employee, user's own ID otherwise)
    const userId = await getEffectiveUserId();

    // Return early if user is not authenticated
    if (!userId) {
      return {
        success: false,
        error: "Tidak terautentikasi!",
      };
    }

    const now = new Date();
    // Go back 5 months to get a total of 6 months including the current one
    const sixMonthsAgo = startOfMonth(subMonths(now, 5));
    const currentMonthEnd = endOfMonth(now);

    const monthlySales = await db.sale.groupBy({
      by: ["saleDate"],
      _sum: {
        totalAmount: true,
      },
      where: {
        userId: userId, // Filter by user ID
        saleDate: {
          gte: sixMonthsAgo,
          lte: currentMonthEnd,
        },
      },
      orderBy: {
        saleDate: "asc",
      },
    });

    // Aggregate sales by month
    const salesByMonth: { [key: string]: number } = {};
    monthlySales.forEach((sale) => {
      const monthKey = format(sale.saleDate, "yyyy-MM");
      const amount = Number(sale._sum.totalAmount ?? 0);
      salesByMonth[monthKey] = (salesByMonth[monthKey] ?? 0) + amount;
    });

    // Create data points for the last 6 months, filling missing months with 0
    const monthsInterval = eachMonthOfInterval({
      start: sixMonthsAgo,
      end: now,
    });

    const chartData: SalesChartDataPoint[] = monthsInterval.map((monthDate) => {
      const monthKey = format(monthDate, "yyyy-MM");
      const monthName = format(monthDate, "MMM", { locale: id }); // e.g., "Jan"
      return {
        name: monthName,
        total: salesByMonth[monthKey] ?? 0,
      };
    });

    return { success: true, data: chartData };
  } catch (error) {
    console.error("Error fetching sales chart data:", error);
    return { success: false, error: "Gagal mengambil data grafik penjualan." };
  }
};

/**
 * Get Purchase Trend Chart Data for the last 6 months (same logic as sales)
 */
export const getPurchaseTrendData = async (): Promise<{
  success: boolean;
  data?: { name: string; total: number }[];
  error?: string;
}> => {
  try {
    // Get effective user ID (owner ID if employee, user's own ID otherwise)
    const userId = await getEffectiveUserId();

    if (!userId) {
      return { success: false, error: "Tidak terautentikasi!" };
    }

    const now = new Date();
    // Go back 5 months to get a total of 6 months including the current one
    const sixMonthsAgo = startOfMonth(subMonths(now, 5));
    const currentMonthEnd = endOfMonth(now);

    const monthlyPurchases = await db.purchase.groupBy({
      by: ["purchaseDate"],
      _sum: {
        totalAmount: true,
      },
      where: {
        userId: userId, // Filter by user ID
        purchaseDate: {
          gte: sixMonthsAgo,
          lte: currentMonthEnd,
        },
      },
      orderBy: {
        purchaseDate: "asc",
      },
    });

    // Aggregate purchases by month
    const purchasesByMonth: { [key: string]: number } = {};
    monthlyPurchases.forEach((purchase) => {
      const monthKey = format(purchase.purchaseDate, "yyyy-MM");
      const amount = Number(purchase._sum.totalAmount ?? 0);
      purchasesByMonth[monthKey] = (purchasesByMonth[monthKey] ?? 0) + amount;
    });

    // Create data points for the last 6 months, filling missing months with 0
    const monthsInterval = eachMonthOfInterval({
      start: sixMonthsAgo,
      end: now,
    });

    const chartData = monthsInterval.map((monthDate) => {
      const monthKey = format(monthDate, "yyyy-MM");
      const monthName = format(monthDate, "MMM", { locale: id }); // e.g., "Jan"
      return {
        name: monthName,
        total: purchasesByMonth[monthKey] ?? 0,
      };
    });

    return { success: true, data: chartData };
  } catch (error) {
    console.error("Error fetching purchase trend data:", error);
    return { success: false, error: "Gagal mengambil data tren pembelian." };
  }
};

// --- Action to get Product Distribution Data (By Category) ---
// NOTE: This assumes a 'category' field exists on the Product model.
// If not, this needs adjustment based on the actual schema.
export const getProductDistributionData = async (): Promise<{
  success: boolean;
  data?: ProductDistributionDataPoint[];
  error?: string;
}> => {
  try {
    // Get the effective user ID (owner ID if employee, user's own ID otherwise)
    const userId = await getEffectiveUserId();

    // Return early if user is not authenticated
    if (!userId) {
      return {
        success: false,
        error: "Tidak terautentikasi!",
      };
    }

    // Group products by categoryId and count them
    const distribution = await db.product.groupBy({
      by: ["categoryId"],
      _count: {
        _all: true, // Correct way to count items in the group
      },
      where: {
        userId: userId, // Filter by user ID
      },
      orderBy: {
        _count: {
          categoryId: "desc", // Order by count descending
        },
      },
    });

    // Get the distinct category IDs from the distribution result
    const categoryIds = distribution
      .map((item) => item.categoryId)
      .filter((id): id is string => id !== null); // Filter out null categoryIds

    // Fetch the corresponding category names
    const categories = await db.category.findMany({
      where: {
        id: {
          in: categoryIds,
        },
        userId: userId, // Filter by user ID
      },
      select: {
        id: true,
        name: true,
      },
    });

    // Create a map for quick lookup of category names by ID
    const categoryNameMap = new Map(
      categories.map((cat) => [cat.id, cat.name])
    );

    // Map the distribution data to the required chart format
    let chartData: ProductDistributionDataPoint[] = distribution.map(
      (item) => ({
        name: item.categoryId
          ? (categoryNameMap.get(item.categoryId) ?? "Kategori Tidak Dikenal")
          : "Tanpa Kategori", // Handle null categoryId or missing category name
        value: item._count._all, // Use the correct count
      })
    );

    // Optional: Limit to top N categories + 'Lainnya' if too many categories
    const MAX_CATEGORIES = 5;
    if (chartData.length > MAX_CATEGORIES) {
      // Sort by value descending to ensure top categories are selected
      chartData.sort((a, b) => b.value - a.value);
      const topCategories = chartData.slice(0, MAX_CATEGORIES - 1);
      const otherCount = chartData
        .slice(MAX_CATEGORIES - 1)
        .reduce((sum, cat) => sum + cat.value, 0);
      // Add "Lainnya" only if there's actually a count > 0
      if (otherCount > 0) {
        chartData = [...topCategories, { name: "Lainnya", value: otherCount }];
      } else {
        chartData = topCategories;
      }
    }

    return { success: true, data: chartData };
  } catch (error) {
    console.error("Error fetching product distribution data:", error);
    return {
      success: false,
      error: "Gagal mengambil data distribusi produk.",
    };
  }
};

// --- Action to get Recent Transactions (Last 5 Sales) ---
export const getRecentTransactions = async (): Promise<{
  success: boolean;
  data?: RecentTransactionItem[];
  error?: string;
}> => {
  try {
    // Get the effective user ID (owner ID if employee, user's own ID otherwise)
    const userId = await getEffectiveUserId();

    // Return early if user is not authenticated
    if (!userId) {
      return {
        success: false,
        error: "Tidak terautentikasi!",
      };
    }

    const recentSales = await db.sale.findMany({
      take: 5,
      where: {
        userId: userId, // Filter by user ID
      },
      orderBy: {
        saleDate: "desc",
      },
      select: {
        id: true, // Assuming 'id' can serve as the transaction ID/invoice
        saleDate: true,
        totalAmount: true,
        // Add status if available, e.g., status: true
      },
    });

    // Helper to format currency locally within the action
    const formatCurrencyLocal = (value: number | Prisma.Decimal) => {
      return new Intl.NumberFormat("id-ID", {
        style: "currency",
        currency: "IDR",
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(Number(value));
    };

    const transactions: RecentTransactionItem[] = recentSales.map((sale) => ({
      id: sale.id, // Use sale ID or a specific invoice number field if exists
      time: formatDistanceToNowStrict(sale.saleDate, {
        addSuffix: true,
        locale: id,
      }), // e.g., "10 menit yang lalu"
      amount: formatCurrencyLocal(sale.totalAmount),
      status: "success", // Assuming all fetched sales are 'success'. Adjust if status field exists.
    }));

    return { success: true, data: transactions };
  } catch (error) {
    console.error("Error fetching recent transactions:", error);
    return { success: false, error: "Gagal mengambil transaksi terkini." };
  }
};

// --- Enhanced Recent Activities (Products, Purchases, Sales) ---
export const getRecentActivities = async (
  page: number = 1,
  limit: number = 8
): Promise<{
  success: boolean;
  data?: RecentActivityItem[];
  pagination?: {
    currentPage: number;
    totalPages: number;
    totalCount: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
  error?: string;
}> => {
  try {
    // Get the effective user ID (owner ID if employee, user's own ID otherwise)
    const userId = await getEffectiveUserId();

    // Return early if user is not authenticated
    if (!userId) {
      return {
        success: false,
        error: "Tidak terautentikasi!",
      };
    }

    // Helper to format currency locally within the action
    const formatCurrencyLocal = (value: number | Prisma.Decimal) => {
      return new Intl.NumberFormat("id-ID", {
        style: "currency",
        currency: "IDR",
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(Number(value));
    };

    // Calculate how many items to fetch from each source for better distribution
    const itemsPerSource = Math.ceil(limit * 1.5); // Fetch more to ensure we have enough after sorting

    // Fetch recent activities from multiple sources
    const [recentProducts, recentPurchases, recentSales] = await Promise.all([
      // Recent Products
      db.product.findMany({
        take: itemsPerSource,
        where: { userId },
        orderBy: { createdAt: "desc" },
        select: {
          id: true,
          name: true,
          price: true,
          stock: true,
          createdAt: true,
        },
      }),

      // Recent Purchases
      db.purchase.findMany({
        take: itemsPerSource,
        where: { userId },
        orderBy: { createdAt: "desc" },
        select: {
          id: true,
          totalAmount: true,
          createdAt: true,
          supplier: {
            select: { name: true },
          },
          items: {
            select: {
              quantity: true,
              product: {
                select: { name: true },
              },
            },
          },
        },
      }),

      // Recent Sales
      db.sale.findMany({
        take: itemsPerSource,
        where: { userId },
        orderBy: { createdAt: "desc" },
        select: {
          id: true,
          totalAmount: true,
          createdAt: true,
          items: {
            select: {
              quantity: true,
              product: {
                select: { name: true },
              },
            },
          },
        },
      }),
    ]);

    // Convert to activity items
    const activities: RecentActivityItem[] = [];

    // Add product activities
    recentProducts.forEach((product) => {
      activities.push({
        id: `product-${product.id}`,
        type: "product",
        title: product.name,
        description: `Produk baru ditambahkan dengan stok ${product.stock} unit`,
        time: formatDistanceToNowStrict(product.createdAt, {
          addSuffix: true,
          locale: id,
        }),
        amount: formatCurrencyLocal(product.price),
        icon: "cube",
        color: "amber",
        href: `/dashboard/products/detail/${product.id}`,
      });
    });

    // Add purchase activities
    recentPurchases.forEach((purchase) => {
      const totalItems = purchase.items.reduce(
        (sum, item) => sum + item.quantity,
        0
      );
      const supplierName =
        purchase.supplier?.name || "Supplier tidak diketahui";

      // Get first few product names for display
      const productNames = purchase.items
        .slice(0, 2)
        .map((item) => item.product.name);
      const moreProducts =
        purchase.items.length > 2
          ? ` +${purchase.items.length - 2} lainnya`
          : "";
      const productList = productNames.join(", ") + moreProducts;

      activities.push({
        id: `purchase-${purchase.id}`,
        type: "purchase",
        title: `Pembelian dari ${supplierName}`,
        description: `${productList} (${totalItems} item)`,
        time: formatDistanceToNowStrict(purchase.createdAt, {
          addSuffix: true,
          locale: id,
        }),
        amount: formatCurrencyLocal(purchase.totalAmount),
        icon: "shopping-bag",
        color: "emerald",
        href: `/dashboard/purchases/detail/${purchase.id}`,
      });
    });

    // Add sales activities
    recentSales.forEach((sale) => {
      const totalItems = sale.items.reduce(
        (sum, item) => sum + item.quantity,
        0
      );

      // Get first few product names for display
      const productNames = sale.items
        .slice(0, 2)
        .map((item) => item.product.name);
      const moreProducts =
        sale.items.length > 2 ? ` +${sale.items.length - 2} lainnya` : "";
      const productList = productNames.join(", ") + moreProducts;

      activities.push({
        id: `sale-${sale.id}`,
        type: "sale",
        title: "Penjualan",
        description: `${productList} (${totalItems} item terjual)`,
        time: formatDistanceToNowStrict(sale.createdAt, {
          addSuffix: true,
          locale: id,
        }),
        amount: formatCurrencyLocal(sale.totalAmount),
        icon: "banknotes",
        color: "indigo",
        href: `/dashboard/sales/detail/${sale.id}`,
      });
    });

    // Sort all activities by creation time (most recent first)
    activities.sort((a, b) => {
      // Extract the creation time from the original data for proper sorting
      const getCreatedAt = (activity: RecentActivityItem) => {
        if (activity.type === "product") {
          const product = recentProducts.find(
            (p) => `product-${p.id}` === activity.id
          );
          return product?.createdAt || new Date(0);
        } else if (activity.type === "purchase") {
          const purchase = recentPurchases.find(
            (p) => `purchase-${p.id}` === activity.id
          );
          return purchase?.createdAt || new Date(0);
        } else if (activity.type === "sale") {
          const sale = recentSales.find((s) => `sale-${s.id}` === activity.id);
          return sale?.createdAt || new Date(0);
        }
        return new Date(0);
      };

      return getCreatedAt(b).getTime() - getCreatedAt(a).getTime();
    });

    // Implement pagination
    const totalCount = activities.length;
    const totalPages = Math.ceil(totalCount / limit);
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedActivities = activities.slice(startIndex, endIndex);

    const pagination = {
      currentPage: page,
      totalPages,
      totalCount,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1,
    };

    return {
      success: true,
      data: paginatedActivities,
      pagination,
    };
  } catch (error) {
    console.error("Error fetching recent activities:", error);
    return { success: false, error: "Gagal mengambil aktivitas terkini." };
  }
};
