"use server";

import { z } from "zod";
import { db } from "@/lib/prisma";
import { revalidatePath } from "next/cache";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";

// Schema for category validation
const CategorySchema = z.object({
  name: z.string().min(1, "Nama kategori harus diisi"),
});

/**
 * Add a new category
 */
export const addCategory = async (values: z.infer<typeof CategorySchema>) => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  // 1. Validate input server-side
  const validatedFields = CategorySchema.safeParse(values);

  if (!validatedFields.success) {
    console.error(
      "Validation Error:",
      validatedFields.error.flatten().fieldErrors
    );
    return { error: "Input tidak valid!" };
  }

  const { name } = validatedFields.data;

  try {
    // Check if category with the same name already exists for this user
    const existingCategory = await db.category.findFirst({
      where: {
        userId,
        name,
      },
    });

    if (existingCategory) {
      return { error: "Kategori dengan nama yang sama sudah ada!" };
    }

    // Create category in database
    const category = await db.category.create({
      data: {
        name,
        userId,
      },
    });

    // Revalidate the products page cache
    revalidatePath("/dashboard/products");
    revalidatePath("/dashboard/products/new");
    revalidatePath("/dashboard/products/edit");

    return {
      success: "Kategori berhasil ditambahkan!",
      category,
    };
  } catch (error) {
    console.error("Database Error:", error);
    return { error: "Gagal menambahkan kategori ke database." };
  }
};

/**
 * Get all categories for the current user
 */
export const getCategories = async () => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  try {
    // Fetch all categories for the current user or their owner
    const categories = await db.category.findMany({
      where: {
        userId,
      },
      orderBy: {
        createdAt: "desc", // Sort by creation date, newest first
      },
    });

    return { categories };
  } catch (error) {
    console.error("Error fetching categories:", error);
    return { error: "Gagal mengambil data kategori." };
  }
};

/**
 * Update an existing category
 */
export const updateCategory = async (
  id: string,
  values: z.infer<typeof CategorySchema>
) => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  // 1. Validate input server-side
  const validatedFields = CategorySchema.safeParse(values);

  if (!validatedFields.success) {
    console.error(
      "Validation Error:",
      validatedFields.error.flatten().fieldErrors
    );
    return { error: "Input tidak valid!" };
  }

  const { name } = validatedFields.data;

  try {
    // Check if category with the same name already exists for this user (excluding current category)
    const existingCategory = await db.category.findFirst({
      where: {
        userId,
        name,
        NOT: {
          id,
        },
      },
    });

    if (existingCategory) {
      return { error: "Kategori dengan nama yang sama sudah ada!" };
    }

    // Update category in database
    const category = await db.category.update({
      where: {
        id,
        userId, // Ensure the category belongs to the current user
      },
      data: {
        name,
      },
    });

    // Revalidate the products page cache
    revalidatePath("/dashboard/products");
    revalidatePath("/dashboard/products/new");
    revalidatePath("/dashboard/products/edit");

    return {
      success: "Kategori berhasil diperbarui!",
      category,
    };
  } catch (error) {
    console.error("Database Error:", error);
    return { error: "Gagal memperbarui kategori ke database." };
  }
};

/**
 * Delete a category
 */
export const deleteCategory = async (id: string) => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  try {
    // Check if the category is referenced in any products
    const products = await db.product.findMany({
      where: {
        categoryId: id,
      },
      take: 1, // We only need to know if there are any, not how many
    });

    if (products.length > 0) {
      return {
        error:
          "Kategori tidak dapat dihapus karena digunakan dalam produk. Hapus produk terkait terlebih dahulu atau edit produk untuk mengganti kategori.",
      };
    }

    // Delete the category from the database
    await db.category.delete({
      where: {
        id,
        userId, // Ensure the category belongs to the current user
      },
    });

    // Revalidate the products page cache
    revalidatePath("/dashboard/products");
    revalidatePath("/dashboard/products/new");
    revalidatePath("/dashboard/products/edit");

    return { success: "Kategori berhasil dihapus!" };
  } catch (error) {
    console.error("Database Error:", error);
    return { error: "Gagal menghapus kategori." };
  }
};
