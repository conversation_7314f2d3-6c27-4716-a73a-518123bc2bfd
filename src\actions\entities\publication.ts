"use server";

import { db } from "@/lib/prisma";
import { auth } from "@/lib/auth";
import { revalidatePath } from "next/cache";

/**
 * Toggle the public status of a purchase
 */
export const togglePurchasePublicStatus = async (purchaseId: string) => {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return { error: "Tidak terautentikasi!" };
    }

    // Check if purchase exists and belongs to the user
    const purchase = await db.purchase.findFirst({
      where: {
        id: purchaseId,
        userId: session.user.id,
      },
      select: {
        id: true,
        isPublic: true,
        transactionNumber: true,
      },
    });

    if (!purchase) {
      return { error: "Pembelian tidak ditemukan!" };
    }

    // Toggle the public status
    const updatedPurchase = await db.purchase.update({
      where: {
        id: purchaseId,
      },
      data: {
        isPublic: !purchase.isPublic,
      },
    });

    // Revalidate the purchase detail page
    revalidatePath(`/dashboard/purchases/detail/${purchaseId}`);

    const message = updatedPurchase.isPublic
      ? "Pembelian berhasil dipublikasikan!"
      : "Pembelian berhasil dibuat privat!";

    return {
      success: message,
      isPublic: updatedPurchase.isPublic,
      publicUrl: updatedPurchase.isPublic
        ? `/cek/purchases/${purchase.transactionNumber || purchase.id}`
        : null,
    };
  } catch (error) {
    console.error("Error toggling purchase public status:", error);
    return { error: "Gagal mengubah status publikasi pembelian" };
  }
};

/**
 * Toggle the public status of a sale
 */
export const toggleSalePublicStatus = async (saleId: string) => {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return { error: "Tidak terautentikasi!" };
    }

    // Check if sale exists and belongs to the user
    const sale = await db.sale.findFirst({
      where: {
        id: saleId,
        userId: session.user.id,
      },
      select: {
        id: true,
        isPublic: true,
        transactionNumber: true,
      },
    });

    if (!sale) {
      return { error: "Penjualan tidak ditemukan!" };
    }

    // Toggle the public status
    const updatedSale = await db.sale.update({
      where: {
        id: saleId,
      },
      data: {
        isPublic: !sale.isPublic,
      },
    });

    // Revalidate the sale detail page
    revalidatePath(`/dashboard/sales/detail/${saleId}`);

    const message = updatedSale.isPublic
      ? "Penjualan berhasil dipublikasikan!"
      : "Penjualan berhasil dibuat privat!";

    return {
      success: message,
      isPublic: updatedSale.isPublic,
      publicUrl: updatedSale.isPublic
        ? `/cek/sales/${sale.transactionNumber || sale.id}`
        : null,
    };
  } catch (error) {
    console.error("Error toggling sale public status:", error);
    return { error: "Gagal mengubah status publikasi penjualan" };
  }
};
