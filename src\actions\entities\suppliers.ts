"use server";

import { z } from "zod";
import { SupplierSchema } from "@/schemas/zod";
import { db } from "@/lib/prisma";
import { revalidatePath } from "next/cache";
import { auth } from "@/lib/auth"; // Import auth to get session
import { getEffectiveUserId } from "@/lib/get-effective-user-id";
import { createSupplierAddedNotification } from "@/lib/create-system-notification";
import { generateSupplierId } from "@/lib/generate-id";
import { canCreateContact } from "@/lib/subscription-limits";
import { getUserSubscription } from "@/lib/subscription";

export const addSupplier = async (values: z.infer<typeof SupplierSchema>) => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  // 1. Check subscription limits before creating supplier
  try {
    const userSubscription = await getUserSubscription(userId);
    const limitCheck = await canCreateContact(userId, userSubscription.plan);

    if (!limitCheck.allowed) {
      return {
        error: limitCheck.message || "Batas kontak tercapai untuk paket Anda.",
      };
    }
  } catch (error) {
    console.error("Error checking subscription limits:", error);
    return { error: "Gagal memeriksa batas langganan." };
  }

  // 2. Validate input server-side
  const validatedFields = SupplierSchema.safeParse(values);

  if (!validatedFields.success) {
    console.error(
      "Validation Error:",
      validatedFields.error.flatten().fieldErrors
    );
    return { error: "Input tidak valid!" };
  }

  const {
    name,
    firstName,
    middleName,
    lastName,
    contactName,
    phone,
    telephone,
    fax,
    email,
    identityType,
    identityNumber,
    NIK,
    NPWP,
    companyName,
    otherInfo,
    address,
    billingAddress,
    shippingAddress,
    sameAsShipping,
    bankName,
    bankBranch,
    accountHolder,
    accountNumber,
    notes,
  } = validatedFields.data;

  try {
    // 2. Generate custom ID for the supplier
    const customId = await generateSupplierId();

    // 3. Create the supplier in the database
    // If email is empty string, set it to null to avoid unique constraint issues
    const supplier = await db.supplier.create({
      data: {
        id: customId, // Use the generated custom ID as the primary key
        name,
        firstName,
        middleName,
        lastName,
        contactName,
        phone,
        telephone,
        fax,
        email: email === "" ? null : email, // Convert empty string to null
        identityType,
        identityNumber,
        NIK,
        NPWP,
        companyName,
        otherInfo,
        address,
        billingAddress,
        shippingAddress: sameAsShipping ? billingAddress : shippingAddress,
        sameAsShipping,
        bankName,
        bankBranch,
        accountHolder,
        accountNumber,
        notes,
        userId, // Associate with the current user
      },
    });

    // 3. Create a notification for the new supplier
    await createSupplierAddedNotification(
      name,
      contactName || email || phone || "Tidak ada kontak"
    );

    // 4. Revalidate the suppliers page to show the new supplier
    revalidatePath("/dashboard/suppliers");

    return { success: "Supplier berhasil ditambahkan!", supplier };
  } catch (error) {
    console.error("Error adding supplier:", error);

    // Handle specific Prisma errors
    if (error && typeof error === "object" && "code" in error) {
      if (error.code === "P2002") {
        // Unique constraint violation
        return {
          error:
            "Supplier dengan data yang sama sudah ada. Silakan periksa kembali data yang dimasukkan.",
        };
      }
    }

    return { error: "Gagal menambahkan supplier. Silakan coba lagi." };
  }
};

export const getSuppliers = async () => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  try {
    // Fetch all suppliers for the current user or their owner
    const suppliers = await db.supplier.findMany({
      where: {
        userId,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return { suppliers };
  } catch (error) {
    console.error("Error fetching suppliers:", error);
    return { error: "Gagal mengambil data supplier." };
  }
};

export const getSupplierById = async (id: string) => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  try {
    // Fetch the supplier by ID, ensuring it belongs to the current user or their owner
    const supplier = await db.supplier.findUnique({
      where: {
        id,
        userId,
      },
    });

    if (!supplier) {
      return { error: "Supplier tidak ditemukan." };
    }

    return { supplier };
  } catch (error) {
    console.error("Error fetching supplier:", error);
    return { error: "Gagal mengambil data supplier." };
  }
};

export const updateSupplier = async (
  id: string,
  values: z.infer<typeof SupplierSchema>
) => {
  // Get current session
  const session = await auth();
  const user = session?.user;

  if (!user || !user.id) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = user.id;

  // 1. Validate input server-side
  const validatedFields = SupplierSchema.safeParse(values);

  if (!validatedFields.success) {
    console.error(
      "Validation Error:",
      validatedFields.error.flatten().fieldErrors
    );
    return { error: "Input tidak valid!" };
  }

  const {
    name,
    firstName,
    middleName,
    lastName,
    contactName,
    phone,
    telephone,
    fax,
    email,
    identityType,
    identityNumber,
    NIK,
    NPWP,
    companyName,
    otherInfo,
    address,
    billingAddress,
    shippingAddress,
    sameAsShipping,
    bankName,
    bankBranch,
    accountHolder,
    accountNumber,
    notes,
  } = validatedFields.data;

  try {
    // 2. Update the supplier in the database
    const supplier = await db.supplier.update({
      where: {
        id,
        userId, // Ensure the supplier belongs to the current user
      },
      data: {
        name,
        firstName,
        middleName,
        lastName,
        contactName,
        phone,
        telephone,
        fax,
        email: email === "" ? null : email, // Convert empty string to null
        identityType,
        identityNumber,
        NIK,
        NPWP,
        companyName,
        otherInfo,
        address,
        billingAddress,
        shippingAddress: sameAsShipping ? billingAddress : shippingAddress,
        sameAsShipping,
        bankName,
        bankBranch,
        accountHolder,
        accountNumber,
        notes,
      },
    });

    // 3. Revalidate the suppliers page to show the updated supplier
    revalidatePath("/dashboard/suppliers");
    revalidatePath(`/dashboard/suppliers/detail/${id}`);

    return { success: "Supplier berhasil diperbarui!", supplier };
  } catch (error) {
    console.error("Error updating supplier:", error);
    return { error: "Gagal memperbarui supplier. Silakan coba lagi." };
  }
};

export const deleteSupplier = async (id: string) => {
  console.log(`[deleteSupplier] Action called with ID: ${id}`);

  // Get current session
  const session = await auth();
  const user = session?.user;

  if (!user || !user.id) {
    console.log(`[deleteSupplier] Authentication failed - no user or user ID`);
    return { error: "Tidak terautentikasi!" };
  }
  const userId = user.id;
  console.log(`[deleteSupplier] Authenticated user ID: ${userId}`);

  try {
    // Check if the supplier is referenced in any purchases
    const purchases = await db.purchase.findMany({
      where: {
        supplierId: id,
      },
      take: 1, // We only need to know if there are any, not how many
    });

    if (purchases.length > 0) {
      return {
        error:
          "Supplier tidak dapat dihapus karena digunakan dalam catatan pembelian. Hapus catatan pembelian terkait terlebih dahulu atau edit pembelian untuk mengganti supplier.",
      };
    }

    // Delete the supplier from the database
    await db.supplier.delete({
      where: {
        id,
        userId, // Ensure the supplier belongs to the current user
      },
    });

    // Revalidate the suppliers page and contacts page to remove the deleted supplier
    revalidatePath("/dashboard/suppliers");
    revalidatePath("/dashboard/contacts");

    return { success: "Supplier berhasil dihapus!" };
  } catch (error) {
    console.error("Error deleting supplier:", error);
    return { error: "Gagal menghapus supplier. Silakan coba lagi." };
  }
};
