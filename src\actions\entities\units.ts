"use server";

import { z } from "zod";
import { db } from "@/lib/prisma";
import { revalidatePath } from "next/cache";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";

// Define schema for unit creation
const UnitSchema = z.object({
  name: z.string().min(1, { message: "Nama satuan wajib diisi" }),
});

export const addUnit = async (values: z.infer<typeof UnitSchema>) => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  // 1. Validate input server-side
  const validatedFields = UnitSchema.safeParse(values);

  if (!validatedFields.success) {
    console.error(
      "Validation Error:",
      validatedFields.error.flatten().fieldErrors
    );
    return { error: "Input tidak valid!" };
  }

  const { name } = validatedFields.data;

  try {
    // Check if unit with the same name already exists for this user
    const existingUnit = await db.unit.findFirst({
      where: {
        userId,
        name,
      },
    });

    if (existingUnit) {
      return {
        error: "Satuan dengan nama yang sama sudah ada!",
        unit: existingUnit,
      };
    }

    // Create unit in database
    const unit = await db.unit.create({
      data: {
        name,
        userId,
      },
    });

    // Revalidate the products page cache
    revalidatePath("/dashboard/products");
    revalidatePath("/dashboard/products/new");
    revalidatePath("/dashboard/products/edit");

    return {
      success: "Satuan berhasil ditambahkan!",
      unit,
    };
  } catch (error) {
    console.error("Database Error:", error);
    return { error: "Gagal menambahkan satuan ke database." };
  }
};

export const getUnits = async () => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  try {
    // Fetch all units for the current user or their owner
    const units = await db.unit.findMany({
      where: {
        userId,
      },
      orderBy: {
        name: "asc",
      },
    });

    return { units };
  } catch (error) {
    console.error("Error fetching units:", error);
    return { error: "Gagal mengambil data satuan." };
  }
};

/**
 * Update an existing unit
 */
export const updateUnit = async (
  id: string,
  values: z.infer<typeof UnitSchema>
) => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  // 1. Validate input server-side
  const validatedFields = UnitSchema.safeParse(values);

  if (!validatedFields.success) {
    console.error(
      "Validation Error:",
      validatedFields.error.flatten().fieldErrors
    );
    return { error: "Input tidak valid!" };
  }

  const { name } = validatedFields.data;

  try {
    // Check if unit with the same name already exists for this user (excluding current unit)
    const existingUnit = await db.unit.findFirst({
      where: {
        userId,
        name,
        NOT: {
          id,
        },
      },
    });

    if (existingUnit) {
      return { error: "Satuan dengan nama yang sama sudah ada!" };
    }

    // Update unit in database
    const unit = await db.unit.update({
      where: {
        id,
        userId, // Ensure the unit belongs to the current user
      },
      data: {
        name,
      },
    });

    // Revalidate the products page cache
    revalidatePath("/dashboard/products");
    revalidatePath("/dashboard/products/new");
    revalidatePath("/dashboard/products/edit");

    return {
      success: "Satuan berhasil diperbarui!",
      unit,
    };
  } catch (error) {
    console.error("Database Error:", error);
    return { error: "Gagal memperbarui satuan ke database." };
  }
};

/**
 * Delete a unit
 */
export const deleteUnit = async (id: string) => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  try {
    // Check if the unit is referenced in any products
    const products = await db.product.findMany({
      where: {
        unitId: id,
      },
      take: 1, // We only need to know if there are any, not how many
    });

    if (products.length > 0) {
      return {
        error:
          "Satuan tidak dapat dihapus karena digunakan dalam produk. Hapus produk terkait terlebih dahulu atau edit produk untuk mengganti satuan.",
      };
    }

    // Delete the unit from the database
    await db.unit.delete({
      where: {
        id,
        userId, // Ensure the unit belongs to the current user
      },
    });

    // Revalidate the products page cache
    revalidatePath("/dashboard/products");
    revalidatePath("/dashboard/products/new");
    revalidatePath("/dashboard/products/edit");

    return { success: "Satuan berhasil dihapus!" };
  } catch (error) {
    console.error("Database Error:", error);
    return { error: "Gagal menghapus satuan." };
  }
};
