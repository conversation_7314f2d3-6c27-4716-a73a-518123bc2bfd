"use server";

import { db } from "@/lib/prisma";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";
import { revalidatePath } from "next/cache";
import {
  WarehouseStock,
  StockTransferData,
  StockAdjustmentData,
  StockMovement,
  StockMovementType,
} from "@/types/warehouse";

// Get warehouse stock for a specific warehouse
export async function getWarehouseStock(
  warehouseId: string
): Promise<WarehouseStock[]> {
  try {
    const userId = await getEffectiveUserId();
    if (!userId) {
      throw new Error("User not authenticated");
    }

    const stocks = await db.warehouseStock.findMany({
      where: {
        warehouseId,
        warehouse: { userId },
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            sku: true,
            image: true,
            price: true,
            unit: true,
          },
        },
        warehouse: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: { product: { name: "asc" } },
    });

    // Serialize the data to match WarehouseStock type
    return stocks.map((stock) => ({
      ...stock,
      product: stock.product
        ? {
            ...stock.product,
            price: stock.product.price.toNumber(),
          }
        : undefined,
    }));
  } catch (error) {
    console.error("Error fetching warehouse stock:", error);
    throw new Error("Failed to fetch warehouse stock");
  }
}

// Get stock for a specific product across all warehouses
export async function getProductStock(
  productId: string
): Promise<WarehouseStock[]> {
  try {
    const userId = await getEffectiveUserId();
    if (!userId) {
      throw new Error("User not authenticated");
    }

    const stocks = await db.warehouseStock.findMany({
      where: {
        productId,
        warehouse: { userId },
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            sku: true,
            image: true,
            price: true,
            unit: true,
          },
        },
        warehouse: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: { warehouse: { name: "asc" } },
    });

    // Serialize the data to match WarehouseStock type
    return stocks.map((stock) => ({
      ...stock,
      product: stock.product
        ? {
            ...stock.product,
            price: stock.product.price.toNumber(),
          }
        : undefined,
    }));
  } catch (error) {
    console.error("Error fetching product stock:", error);
    throw new Error("Failed to fetch product stock");
  }
}

// Transfer stock between warehouses
export async function transferStock(data: StockTransferData): Promise<void> {
  try {
    const userId = await getEffectiveUserId();
    if (!userId) {
      throw new Error("User not authenticated");
    }

    const { productId, fromWarehouseId, toWarehouseId, quantity, notes } = data;

    if (fromWarehouseId === toWarehouseId) {
      throw new Error("Cannot transfer stock to the same warehouse");
    }

    if (quantity <= 0) {
      throw new Error("Transfer quantity must be greater than 0");
    }

    await db.$transaction(async (tx) => {
      // Check source warehouse stock
      const sourceStock = await tx.warehouseStock.findUnique({
        where: {
          productId_warehouseId: { productId, warehouseId: fromWarehouseId },
        },
      });

      if (!sourceStock || sourceStock.quantity < quantity) {
        throw new Error("Insufficient stock in source warehouse");
      }

      // Update source warehouse stock
      const newSourceQuantity = sourceStock.quantity - quantity;
      await tx.warehouseStock.update({
        where: {
          productId_warehouseId: { productId, warehouseId: fromWarehouseId },
        },
        data: { quantity: newSourceQuantity },
      });

      // Update or create destination warehouse stock
      const destStock = await tx.warehouseStock.findUnique({
        where: {
          productId_warehouseId: { productId, warehouseId: toWarehouseId },
        },
      });

      if (destStock) {
        const newDestQuantity = destStock.quantity + quantity;
        await tx.warehouseStock.update({
          where: {
            productId_warehouseId: { productId, warehouseId: toWarehouseId },
          },
          data: { quantity: newDestQuantity },
        });
      } else {
        await tx.warehouseStock.create({
          data: {
            productId,
            warehouseId: toWarehouseId,
            quantity,
            minLevel: 0,
          },
        });
      }

      // Create stock movement records
      const transferId = `TRANSFER-${Date.now()}`;

      await tx.stockMovement.create({
        data: {
          type: StockMovementType.TRANSFER_OUT,
          quantity: -quantity,
          previousStock: sourceStock.quantity,
          newStock: newSourceQuantity,
          reference: transferId,
          notes: notes || `Transfer to ${toWarehouseId}`,
          productId,
          warehouseId: fromWarehouseId,
          userId,
        },
      });

      await tx.stockMovement.create({
        data: {
          type: StockMovementType.TRANSFER_IN,
          quantity: quantity,
          previousStock: destStock?.quantity || 0,
          newStock: (destStock?.quantity || 0) + quantity,
          reference: transferId,
          notes: notes || `Transfer from ${fromWarehouseId}`,
          productId,
          warehouseId: toWarehouseId,
          userId,
        },
      });
    });

    revalidatePath("/dashboard/products");
    revalidatePath("/dashboard/purchases");
    revalidatePath("/dashboard/sales");
  } catch (error) {
    console.error("Error transferring stock:", error);
    throw error;
  }
}

// Adjust stock quantity manually
export async function adjustStock(data: StockAdjustmentData): Promise<void> {
  try {
    const userId = await getEffectiveUserId();
    if (!userId) {
      throw new Error("User not authenticated");
    }

    const { productId, warehouseId, newQuantity, notes } = data;

    if (newQuantity < 0) {
      throw new Error("Stock quantity cannot be negative");
    }

    await db.$transaction(async (tx) => {
      // Get current stock
      const currentStock = await tx.warehouseStock.findUnique({
        where: {
          productId_warehouseId: { productId, warehouseId },
        },
      });

      const previousQuantity = currentStock?.quantity || 0;
      const quantityChange = newQuantity - previousQuantity;

      // Update or create warehouse stock
      if (currentStock) {
        await tx.warehouseStock.update({
          where: {
            productId_warehouseId: { productId, warehouseId },
          },
          data: { quantity: newQuantity },
        });
      } else {
        await tx.warehouseStock.create({
          data: {
            productId,
            warehouseId,
            quantity: newQuantity,
            minLevel: 0,
          },
        });
      }

      // Create stock movement record
      await tx.stockMovement.create({
        data: {
          type: StockMovementType.ADJUSTMENT,
          quantity: quantityChange,
          previousStock: previousQuantity,
          newStock: newQuantity,
          notes: notes || "Manual stock adjustment",
          productId,
          warehouseId,
          userId,
        },
      });
    });

    revalidatePath("/dashboard/products");
    revalidatePath("/dashboard/purchases");
    revalidatePath("/dashboard/sales");
  } catch (error) {
    console.error("Error adjusting stock:", error);
    throw error;
  }
}

// Get stock movements for a warehouse or product
export async function getStockMovements(
  warehouseId?: string,
  productId?: string,
  limit: number = 50
): Promise<StockMovement[]> {
  try {
    const userId = await getEffectiveUserId();
    if (!userId) {
      throw new Error("User not authenticated");
    }

    const movements = await db.stockMovement.findMany({
      where: {
        userId,
        ...(warehouseId && { warehouseId }),
        ...(productId && { productId }),
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            sku: true,
          },
        },
        warehouse: {
          select: {
            id: true,
            name: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            username: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
      take: limit,
    });

    // Convert Prisma enum to our custom enum
    return movements.map((movement) => ({
      ...movement,
      type: movement.type as StockMovementType,
    }));
  } catch (error) {
    console.error("Error fetching stock movements:", error);
    throw new Error("Failed to fetch stock movements");
  }
}
