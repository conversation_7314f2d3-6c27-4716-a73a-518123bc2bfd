"use server";

import { auth } from "@/lib/auth";
import { db } from "@/lib/prisma";
import { EventDiscountSchema } from "@/schemas/zod";
import { revalidatePath } from "next/cache";
import { z } from "zod";

function transformEventDiscount(eventDiscount: any) {
  // console.log("Original discountPercentage type:", typeof eventDiscount.discountPercentage, "value:", eventDiscount.discountPercentage);
  const transformedDiscount = {
    ...eventDiscount,
    discountPercentage: eventDiscount.discountPercentage.toNumber(),
    products: eventDiscount.products.map((ep: any) => ({
      ...ep,
      product: {
        ...ep.product,
        price: ep.product.price.toNumber(),
      },
    })),
  };
  // console.log("Transformed discountPercentage type:", typeof transformedDiscount.discountPercentage, "value:", transformedDiscount.discountPercentage);
  return transformedDiscount;
}

// Get all event discounts for the current user
export async function getEventDiscounts() {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return { error: "Unauthorized" };
    }

    const eventDiscounts = await db.eventDiscount.findMany({
      where: {
        userId: session.user.id,
      },
      include: {
        products: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                price: true,
              },
            },
          },
        },
        _count: {
          select: {
            products: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Calculate sales count for each event discount by counting SaleItems that used this discount
    const eventDiscountsWithSalesCount = await Promise.all(
      eventDiscounts.map(async (discount) => {
        const salesCount = await db.saleItem.count({
          where: {
            eventDiscountId: discount.id,
            sale: {
              userId: session.user.id,
              isDraft: false, // Only count non-draft sales
            },
          },
        });

        return {
          ...discount,
          _count: {
            ...discount._count,
            sales: salesCount,
          },
        };
      })
    );

    return {
      success: true,
      data: eventDiscountsWithSalesCount.map(transformEventDiscount),
    };
  } catch (error) {
    console.error("Error fetching event discounts:", error);
    return { error: "Failed to fetch event discounts" };
  }
}

// Get active event discounts (for sales form)
export async function getActiveEventDiscounts() {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return { error: "Unauthorized" };
    }

    const now = new Date();
    const eventDiscounts = await db.eventDiscount.findMany({
      where: {
        userId: session.user.id,
        isActive: true,
        startDate: {
          lte: now,
        },
        endDate: {
          gte: now,
        },
      },
      include: {
        products: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                price: true,
              },
            },
          },
        },
      },
      orderBy: {
        name: "asc",
      },
    });

    return { success: true, data: eventDiscounts.map(transformEventDiscount) };
  } catch (error) {
    console.error("Error fetching active event discounts:", error);
    return { error: "Failed to fetch active event discounts" };
  }
}

// Create a new event discount
export async function createEventDiscount(
  values: z.infer<typeof EventDiscountSchema>
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return { error: "Unauthorized" };
    }

    const validatedFields = EventDiscountSchema.safeParse(values);
    if (!validatedFields.success) {
      return { error: "Invalid fields" };
    }

    const { productIds, ...discountData } = validatedFields.data;

    // Create the event discount with product relations
    const eventDiscount = await db.eventDiscount.create({
      data: {
        ...discountData,
        userId: session.user.id,
        products: {
          create: productIds.map((productId) => ({
            productId,
          })),
        },
      },
      include: {
        products: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                price: true,
              },
            },
          },
        },
      },
    });

    revalidatePath("/dashboard/event-discounts");
    return {
      success: "Event discount created successfully",
      data: transformEventDiscount(eventDiscount),
    };
  } catch (error) {
    console.error("Error creating event discount:", error);
    return { error: "Failed to create event discount" };
  }
}

// Update an event discount
export async function updateEventDiscount(
  id: string,
  values: z.infer<typeof EventDiscountSchema>
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return { error: "Unauthorized" };
    }

    const validatedFields = EventDiscountSchema.safeParse(values);
    if (!validatedFields.success) {
      return { error: "Invalid fields" };
    }

    const { productIds, ...discountData } = validatedFields.data;

    // Check if the event discount exists and belongs to the user
    const existingDiscount = await db.eventDiscount.findFirst({
      where: {
        id,
        userId: session.user.id,
      },
    });

    if (!existingDiscount) {
      return { error: "Event discount not found" };
    }

    // Update the event discount and replace product relations
    const eventDiscount = await db.eventDiscount.update({
      where: { id },
      data: {
        ...discountData,
        products: {
          deleteMany: {}, // Remove all existing product relations
          create: productIds.map((productId) => ({
            productId,
          })),
        },
      },
      include: {
        products: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                price: true,
              },
            },
          },
        },
      },
    });

    revalidatePath("/dashboard/event-discounts");
    return {
      success: "Event discount updated successfully",
      data: transformEventDiscount(eventDiscount),
    };
  } catch (error) {
    console.error("Error updating event discount:", error);
    return { error: "Failed to update event discount" };
  }
}

// Delete an event discount
export async function deleteEventDiscount(id: string) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return { error: "Unauthorized" };
    }

    // Check if the event discount exists and belongs to the user
    const existingDiscount = await db.eventDiscount.findFirst({
      where: {
        id,
        userId: session.user.id,
      },
    });

    if (!existingDiscount) {
      return { error: "Event discount not found" };
    }

    // Delete the event discount (cascade will handle product relations)
    await db.eventDiscount.delete({
      where: { id },
    });

    revalidatePath("/dashboard/event-discounts");
    return { success: "Event discount deleted successfully" };
  } catch (error) {
    console.error("Error deleting event discount:", error);
    return { error: "Failed to delete event discount" };
  }
}

// Toggle event discount active status
export async function toggleEventDiscountStatus(id: string) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return { error: "Unauthorized" };
    }

    // Check if the event discount exists and belongs to the user
    const existingDiscount = await db.eventDiscount.findFirst({
      where: {
        id,
        userId: session.user.id,
      },
    });

    if (!existingDiscount) {
      return { error: "Event discount not found" };
    }

    // Toggle the active status and include products relation for transformation
    const eventDiscount = await db.eventDiscount.update({
      where: { id },
      data: {
        isActive: !existingDiscount.isActive,
      },
      include: {
        products: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                price: true,
              },
            },
          },
        },
      },
    });

    revalidatePath("/dashboard/event-discounts");
    return {
      success: `Event discount ${eventDiscount.isActive ? "activated" : "deactivated"} successfully`,
      data: transformEventDiscount(eventDiscount),
    };
  } catch (error) {
    console.error("Error toggling event discount status:", error);
    return { error: "Failed to toggle event discount status" };
  }
}

// Get event discount by ID
export async function getEventDiscountById(id: string) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return { error: "Unauthorized" };
    }

    const eventDiscount = await db.eventDiscount.findFirst({
      where: {
        id,
        userId: session.user.id,
      },
      include: {
        products: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                price: true,
              },
            },
          },
        },
      },
    });

    if (!eventDiscount) {
      return { error: "Event discount not found" };
    }

    return { success: true, data: transformEventDiscount(eventDiscount) };
  } catch (error) {
    console.error("Error fetching event discount:", error);
    return { error: "Failed to fetch event discount" };
  }
}
