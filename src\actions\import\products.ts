"use server";

import { db } from "@/lib/prisma";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";
import { generateConcurrentSafeProductIds } from "@/lib/generate-id";
import { createSystemNotification } from "@/lib/create-system-notification";
import * as XLSX from "xlsx";

interface ImportResult {
  success?: string;
  error?: string;
  summary?: {
    productsCreated: number;
    categoriesCreated: number;
    unitsCreated: number;
    variantsCreated: number;
    errors: string[];
  };
}

// Validate required fields
const validateProductData = (row: any, rowIndex: number): string[] => {
  const errors: string[] = [];

  // Check if row is empty
  if (!row || Object.keys(row).length === 0) {
    errors.push(`Baris ${rowIndex}: Baris kosong`);
    return errors;
  }

  // Validate required fields
  if (!row["Nama Produk"] || row["Nama Produk"].toString().trim() === "") {
    errors.push(`Baris ${rowIndex}: Nama Produk wajib diisi`);
  }

  if (
    !row["Harga Jual"] ||
    isNaN(Number(row["Harga Jual"])) ||
    Number(row["Harga Jual"]) <= 0
  ) {
    errors.push(`Baris ${rowIndex}: Harga Jual harus berupa angka positif`);
  }

  // Validate optional numeric fields
  const numericFields = ["Harga Beli", "Harga Grosir", "Harga Diskon"];
  numericFields.forEach((field) => {
    if (
      row[field] &&
      row[field] !== "" &&
      (isNaN(Number(row[field])) || Number(row[field]) < 0)
    ) {
      errors.push(
        `Baris ${rowIndex}: ${field} harus berupa angka positif atau kosong`
      );
    }
  });

  // Validate product name length
  if (row["Nama Produk"] && row["Nama Produk"].toString().length > 255) {
    errors.push(`Baris ${rowIndex}: Nama Produk maksimal 255 karakter`);
  }

  // Validate description length
  if (row["Deskripsi"] && row["Deskripsi"].toString().length > 1000) {
    errors.push(`Baris ${rowIndex}: Deskripsi maksimal 1000 karakter`);
  }

  return errors;
};

// Sanitize input data
const sanitizeString = (value: any): string => {
  if (value === null || value === undefined) return "";
  return String(value).trim().slice(0, 255); // Limit length to prevent overflow
};

const sanitizeNumber = (value: any): number => {
  if (value === null || value === undefined || value === "") return 0;

  // Handle string values that might have currency formatting
  let cleanValue = value;
  if (typeof value === "string") {
    // Remove common currency symbols and separators
    cleanValue = value.replace(/[Rp\s,\.]/g, "");
  }

  const num = Number(cleanValue);
  return isNaN(num) ? 0 : Math.max(0, Math.round(num * 100) / 100); // Ensure non-negative and round to 2 decimal places
};

// Sanitize description text with proper length limits and formatting
const sanitizeDescription = (value: any): string => {
  if (value === null || value === undefined) return "";

  let description = String(value).trim();

  // Remove excessive whitespace and normalize line breaks
  description = description.replace(/\s+/g, " ").replace(/\n+/g, "\n");

  // Limit length to 1000 characters
  if (description.length > 1000) {
    description = description.slice(0, 1000).trim();
  }

  return description;
};

// Find or create category
const findOrCreateCategory = async (
  categoryName: string,
  userId: string,
  tx: Parameters<Parameters<typeof db.$transaction>[0]>[0]
) => {
  if (!categoryName || categoryName.trim() === "") return null;

  const sanitizedName = sanitizeString(categoryName);

  // Check if category exists
  const existingCategory = await tx.category.findFirst({
    where: {
      name: sanitizedName,
      userId: userId,
    },
  });

  if (existingCategory) {
    return existingCategory.id;
  }

  // Create new category
  const newCategory = await tx.category.create({
    data: {
      name: sanitizedName,
      userId: userId,
    },
  });

  return newCategory.id;
};

// Find or create unit
const findOrCreateUnit = async (
  unitName: string,
  userId: string,
  tx: Parameters<Parameters<typeof db.$transaction>[0]>[0]
) => {
  if (!unitName || unitName.trim() === "") return null;

  const sanitizedName = sanitizeString(unitName);

  // Check if unit exists
  const existingUnit = await tx.unit.findFirst({
    where: {
      name: sanitizedName,
      userId: userId,
    },
  });

  if (existingUnit) {
    return existingUnit.id;
  }

  // Create new unit
  const newUnit = await tx.unit.create({
    data: {
      name: sanitizedName,
      userId: userId,
    },
  });

  return newUnit.id;
};

// Process tags
const processTags = (tagsString: string): string[] => {
  if (!tagsString || tagsString.trim() === "") return [];

  return tagsString
    .split(",")
    .map((tag) => sanitizeString(tag))
    .filter((tag) => tag.length > 0)
    .slice(0, 3); // Limit to 3 tags
};

// Process variants
const processVariants = (variantsString: string): any[] => {
  if (!variantsString || variantsString.trim() === "") return [];

  return variantsString
    .split(",")
    .map((variant) => {
      const trimmed = sanitizeString(variant);
      if (trimmed.length === 0) return null;

      return {
        colorName: trimmed,
        colorCode: "#000000", // Default color
        sku: null,
        price: null,
        stock: 0,
        image: null,
      };
    })
    .filter((variant) => variant !== null)
    .slice(0, 5); // Limit to 5 variants
};

export const importProducts = async (
  fileBuffer: ArrayBuffer
): Promise<ImportResult> => {
  try {
    // Get effective user ID
    const effectiveUserId = await getEffectiveUserId();
    if (!effectiveUserId) {
      return { error: "Tidak terautentikasi!" };
    }

    // Parse Excel file
    let workbook;
    try {
      workbook = XLSX.read(fileBuffer, { type: "array" });
    } catch (error) {
      return { error: "File Excel tidak valid atau rusak" };
    }

    const sheetName = workbook.SheetNames[0];
    if (!sheetName) {
      return { error: "File Excel tidak memiliki sheet yang valid" };
    }

    const worksheet = workbook.Sheets[sheetName];
    if (!worksheet) {
      return { error: "Sheet Excel tidak dapat dibaca" };
    }

    let jsonData;
    try {
      jsonData = XLSX.utils.sheet_to_json(worksheet);
    } catch (error) {
      return { error: "Gagal membaca data dari Excel" };
    }

    if (jsonData.length === 0) {
      return { error: "File Excel kosong atau tidak memiliki data" };
    }

    // Filter out completely empty rows
    const filteredData = jsonData.filter((row: any) => {
      return (
        row &&
        Object.keys(row).length > 0 &&
        Object.values(row).some(
          (value) => value !== null && value !== undefined && value !== ""
        )
      );
    });

    if (filteredData.length === 0) {
      return { error: "Tidak ada data valid yang ditemukan dalam file Excel" };
    }

    // Limit number of rows to prevent system overload
    if (filteredData.length > 1000) {
      return { error: "Maksimal 1000 produk dapat diimpor sekaligus" };
    }

    // Validate all data first
    const validationErrors: string[] = [];
    filteredData.forEach((row: any, index) => {
      const rowErrors = validateProductData(row, index + 2); // +2 because Excel rows start at 1 and we have header
      validationErrors.push(...rowErrors);
    });

    if (validationErrors.length > 0) {
      return {
        error: "Validasi gagal",
        summary: {
          productsCreated: 0,
          categoriesCreated: 0,
          unitsCreated: 0,
          variantsCreated: 0,
          errors: validationErrors,
        },
      };
    }

    // Pre-generate all product IDs outside of transaction to avoid timeout
    let productIds: string[] = [];
    try {
      console.log(
        `[IMPORT] Generating ${filteredData.length} product IDs for user ${effectiveUserId}`
      );

      // Use a separate, shorter transaction just for ID generation
      productIds = await db.$transaction(
        async (tx) => {
          return await generateConcurrentSafeProductIds(
            effectiveUserId,
            filteredData.length,
            tx
          );
        },
        { timeout: 30000 } // 30 seconds for ID generation only
      );

      console.log(
        `[IMPORT] Successfully generated ${productIds.length} product IDs`
      );
    } catch (error) {
      console.error("[IMPORT] Error generating product IDs:", error);
      return {
        error: `Gagal membuat ID produk - ${error instanceof Error ? error.message : "Unknown error"}`,
        summary: {
          productsCreated: 0,
          categoriesCreated: 0,
          unitsCreated: 0,
          variantsCreated: 0,
          errors: [error instanceof Error ? error.message : "Unknown error"],
        },
      };
    }

    // Process import in smaller batches to avoid transaction timeout
    const BATCH_SIZE = 10; // Process 10 products at a time
    let totalProductsCreated = 0;
    let totalCategoriesCreated = 0;
    let totalUnitsCreated = 0;
    let totalVariantsCreated = 0;
    const allErrors: string[] = [];

    // Process data in batches
    for (
      let batchStart = 0;
      batchStart < filteredData.length;
      batchStart += BATCH_SIZE
    ) {
      const batchEnd = Math.min(batchStart + BATCH_SIZE, filteredData.length);
      const batch = filteredData.slice(batchStart, batchEnd);
      const batchProductIds = productIds.slice(batchStart, batchEnd);

      console.log(
        `[IMPORT] Processing batch ${Math.floor(batchStart / BATCH_SIZE) + 1}/${Math.ceil(filteredData.length / BATCH_SIZE)} (${batch.length} products)`
      );

      try {
        const batchResult = await db.$transaction(
          async (tx) => {
            let productsCreated = 0;
            let categoriesCreated = 0;
            let unitsCreated = 0;
            let variantsCreated = 0;
            const errors: string[] = [];

            // Process each product in the batch
            for (let i = 0; i < batch.length; i++) {
              const row = batch[i] as any;
              const rowIndex = batchStart + i + 2; // +2 for Excel header row
              const customProductId = batchProductIds[i];

              try {
                // Process category
                let categoryId = null;
                if (row["Kategori"]) {
                  const existingCategoryCount = await tx.category.count({
                    where: { userId: effectiveUserId },
                  });
                  categoryId = await findOrCreateCategory(
                    row["Kategori"],
                    effectiveUserId,
                    tx
                  );
                  const newCategoryCount = await tx.category.count({
                    where: { userId: effectiveUserId },
                  });
                  if (newCategoryCount > existingCategoryCount) {
                    categoriesCreated++;
                  }
                }

                // Process unit
                let unitId = null;
                if (row["Satuan"]) {
                  const existingUnitCount = await tx.unit.count({
                    where: { userId: effectiveUserId },
                  });
                  unitId = await findOrCreateUnit(
                    row["Satuan"],
                    effectiveUserId,
                    tx
                  );
                  const newUnitCount = await tx.unit.count({
                    where: { userId: effectiveUserId },
                  });
                  if (newUnitCount > existingUnitCount) {
                    unitsCreated++;
                  }
                }

                // Process SKU - ensure uniqueness (use exact header name from template)
                let sku: string | null = sanitizeString(
                  row["Kode Produk (SKU)"]
                );
                if (sku && sku.trim() !== "") {
                  const existingSku = await tx.product.findFirst({
                    where: { sku: sku, userId: effectiveUserId },
                  });
                  if (existingSku) {
                    sku = `${sku}-${Date.now()}`; // Make unique
                  }
                } else {
                  sku = null;
                }

                // Process barcode
                let barcode: string | null = sanitizeString(row["Barcode"]);
                if (barcode && barcode.trim() !== "") {
                  const existingBarcode = await tx.product.findFirst({
                    where: { barcode: barcode, userId: effectiveUserId },
                  });
                  if (existingBarcode) {
                    barcode = `${barcode}-${Date.now()}`; // Make unique
                  }
                } else {
                  barcode = null;
                }

                // Process tags and variants
                const tags = processTags(row["Tag Produk"]);
                const variants = processVariants(row["Varian Warna"]);

                // Create product
                const newProduct = await tx.product.create({
                  data: {
                    id: customProductId,
                    name: sanitizeString(row["Nama Produk"]),
                    description: sanitizeDescription(row["Deskripsi"]),
                    sku: sku,
                    barcode: barcode,
                    price: sanitizeNumber(row["Harga Jual"]),
                    wholesalePrice: sanitizeNumber(row["Harga Grosir"]) || null,
                    cost: sanitizeNumber(row["Harga Beli"]) || null,
                    stock: 0, // Initial stock is 0 as per requirements
                    image: "",
                    salePriceTaxRate: 0,
                    wholesalePriceTaxRate: 0,
                    costPriceTaxRate: 0,
                    weight: 0,
                    length: 0,
                    width: 0,
                    height: 0,
                    unit: sanitizeString(row["Satuan"]) || "Pcs",
                    unitId: unitId,
                    tags: tags,
                    userId: effectiveUserId,
                    categoryId: categoryId,
                    isDraft: false,
                    hasVariants: variants.length > 0,
                  },
                });

                // Create variants if any
                if (variants.length > 0) {
                  for (const variant of variants) {
                    await tx.productVariant.create({
                      data: {
                        ...variant,
                        productId: newProduct.id,
                      },
                    });
                    variantsCreated++;
                  }
                }

                productsCreated++;
              } catch (error) {
                console.error(`Error processing row ${rowIndex}:`, error);

                // Handle specific error types
                let errorMessage = "Unknown error";
                if (error instanceof Error) {
                  if (
                    error.message.includes(
                      "Unique constraint failed on the fields: (`id`)"
                    )
                  ) {
                    errorMessage = "ID produk duplikat - silakan coba lagi";
                  } else if (
                    error.message.includes("Unique constraint failed")
                  ) {
                    errorMessage = "Data duplikat terdeteksi";
                  } else {
                    errorMessage = error.message;
                  }
                }

                errors.push(
                  `Baris ${rowIndex}: Gagal memproses data - ${errorMessage}`
                );
              }
            }

            return {
              productsCreated,
              categoriesCreated,
              unitsCreated,
              variantsCreated,
              errors,
            };
          },
          { timeout: 30000 } // 30 seconds per batch
        );

        // Accumulate results from this batch
        totalProductsCreated += batchResult.productsCreated;
        totalCategoriesCreated += batchResult.categoriesCreated;
        totalUnitsCreated += batchResult.unitsCreated;
        totalVariantsCreated += batchResult.variantsCreated;
        allErrors.push(...batchResult.errors);

        console.log(
          `[IMPORT] Batch completed: ${batchResult.productsCreated} products created`
        );
      } catch (error) {
        console.error(`[IMPORT] Batch failed:`, error);
        allErrors.push(
          `Batch ${Math.floor(batchStart / BATCH_SIZE) + 1} gagal: ${error instanceof Error ? error.message : "Unknown error"}`
        );
      }
    }

    // Return final results
    const finalResult = {
      productsCreated: totalProductsCreated,
      categoriesCreated: totalCategoriesCreated,
      unitsCreated: totalUnitsCreated,
      variantsCreated: totalVariantsCreated,
      errors: allErrors,
    };

    // Create notifications based on import results
    try {
      if (allErrors.length > 0 && totalProductsCreated === 0) {
        // Complete failure notification
        await createSystemNotification(
          "error",
          "Import Produk Gagal",
          `Import produk gagal sepenuhnya. ${allErrors.length} error ditemukan. Silakan periksa file dan coba lagi.`,
          false
        );

        return {
          error: "Import gagal",
          summary: finalResult,
        };
      } else if (allErrors.length > 0 && totalProductsCreated > 0) {
        // Partial success notification
        await createSystemNotification(
          "warning",
          "Import Produk Sebagian Berhasil",
          `Import produk selesai dengan ${totalProductsCreated} produk berhasil diimpor, namun ${allErrors.length} baris gagal diproses. ${totalCategoriesCreated} kategori baru, ${totalUnitsCreated} unit baru, dan ${totalVariantsCreated} varian telah dibuat.`,
          false
        );
      } else {
        // Complete success notification
        await createSystemNotification(
          "success",
          "Import Produk Berhasil",
          `Import produk berhasil! ${totalProductsCreated} produk berhasil diimpor. ${totalCategoriesCreated} kategori baru, ${totalUnitsCreated} unit baru, dan ${totalVariantsCreated} varian telah dibuat.`,
          false
        );
      }
    } catch (notificationError) {
      console.error("Failed to create import notification:", notificationError);
    }

    return {
      success: `Import berhasil! ${totalProductsCreated} produk berhasil diimpor.`,
      summary: finalResult,
    };
  } catch (error) {
    console.error("Import error:", error);

    // Create error notification
    try {
      await createSystemNotification(
        "error",
        "Import Produk Error",
        `Terjadi kesalahan sistem saat mengimpor produk: ${error instanceof Error ? error.message : "Unknown error"}`,
        false
      );
    } catch (notificationError) {
      console.error("Failed to create error notification:", notificationError);
    }

    return {
      error: "Gagal memproses file import",
      summary: {
        productsCreated: 0,
        categoriesCreated: 0,
        unitsCreated: 0,
        variantsCreated: 0,
        errors: [error instanceof Error ? error.message : "Unknown error"],
      },
    };
  }
};
