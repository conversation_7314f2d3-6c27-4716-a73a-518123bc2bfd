"use server";

import { db } from "@/lib/prisma";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";
import { generateCustomerId, generateSalesId } from "@/lib/generate-id";
import { createSystemNotification } from "@/lib/create-system-notification";
import * as XLSX from "xlsx";

interface ImportSummary {
  salesCreated: number;
  customersCreated: number;
  errors: string[];
}

interface ImportResult {
  success?: string;
  error?: string;
  summary: ImportSummary;
}

// Utility function to sanitize string inputs
const sanitizeString = (value: any): string => {
  if (value === null || value === undefined || value === "") return "";
  return String(value).trim().substring(0, 255);
};

// Utility function to sanitize number inputs
const sanitizeNumber = (value: any): number => {
  if (value === null || value === undefined || value === "") return 0;
  const num = Number(value);
  return isNaN(num) ? 0 : Math.max(0, num);
};

// Utility function to sanitize date inputs
const sanitizeDate = (value: any): Date => {
  if (value === null || value === undefined || value === "") {
    return new Date();
  }

  // Handle Excel date serial numbers
  if (typeof value === "number") {
    // Excel date serial number (days since 1900-01-01)
    const excelEpoch = new Date(1900, 0, 1);
    const date = new Date(
      excelEpoch.getTime() + (value - 1) * 24 * 60 * 60 * 1000
    );
    return isNaN(date.getTime()) ? new Date() : date;
  }

  // Handle string dates
  if (typeof value === "string") {
    const date = new Date(value);
    return isNaN(date.getTime()) ? new Date() : date;
  }

  // Handle Date objects
  if (value instanceof Date) {
    return isNaN(value.getTime()) ? new Date() : value;
  }

  return new Date();
};

// Utility function to parse boolean values
const sanitizeBoolean = (value: any): boolean => {
  if (value === null || value === undefined || value === "") return false;
  const str = String(value).toLowerCase().trim();
  return str === "ya" || str === "yes" || str === "true" || str === "1";
};

// Main import function for sales
export const importSales = async (
  arrayBuffer: ArrayBuffer
): Promise<ImportResult> => {
  try {
    console.log("[IMPORT] Starting sales import process");

    // Get effective user ID
    const effectiveUserId = await getEffectiveUserId();
    if (!effectiveUserId) {
      return {
        error: "Tidak terautentikasi",
        summary: {
          salesCreated: 0,
          customersCreated: 0,
          errors: ["User tidak terautentikasi"],
        },
      };
    }

    // Parse Excel file
    const workbook = XLSX.read(arrayBuffer, { type: "array" });
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

    console.log(`[IMPORT] Parsed ${data.length} rows from Excel`);

    if (data.length < 2) {
      return {
        error: "File Excel kosong atau tidak memiliki data",
        summary: {
          salesCreated: 0,
          customersCreated: 0,
          errors: ["File tidak memiliki data yang valid"],
        },
      };
    }

    // Get headers from first row
    const headers = data[0] as string[];
    const dataRows = data.slice(1);

    // Convert to objects
    const salesData = dataRows
      .map((row: unknown, index: number) => {
        const rowArray = row as any[];
        const obj: any = {};
        headers.forEach((header, i) => {
          obj[header] = rowArray[i];
        });
        obj._rowIndex = index + 2; // +2 because we start from row 2 (after header)
        return obj;
      })
      .filter((row) => {
        // Filter out empty rows
        return (
          sanitizeString(row["Tanggal Penjualan"]) &&
          sanitizeString(row["Nama Produk"]) &&
          sanitizeNumber(row["Quantity"]) > 0 &&
          sanitizeNumber(row["Harga Jual"]) > 0
        );
      });

    console.log(`[IMPORT] Filtered to ${salesData.length} valid sales rows`);

    if (salesData.length === 0) {
      return {
        error: "Tidak ada data penjualan yang valid ditemukan",
        summary: {
          salesCreated: 0,
          customersCreated: 0,
          errors: ["Tidak ada baris data yang memenuhi kriteria minimum"],
        },
      };
    }

    // Pre-validate all products before processing any rows
    console.log("[IMPORT] Validating product availability...");
    const uniqueProductNames = new Set<string>();

    // Collect all unique product names from the import data
    salesData.forEach((row) => {
      const productName = sanitizeString(row["Nama Produk"]);
      if (productName) {
        uniqueProductNames.add(productName);
      }
    });

    // Check if all products exist in the database
    const existingProducts = await db.product.findMany({
      where: {
        name: {
          in: Array.from(uniqueProductNames),
        },
        userId: effectiveUserId,
      },
      select: {
        name: true,
      },
    });

    const existingProductNames = new Set(existingProducts.map((p) => p.name));
    const missingProducts: string[] = [];

    uniqueProductNames.forEach((productName) => {
      if (!existingProductNames.has(productName)) {
        missingProducts.push(productName);
      }
    });

    if (missingProducts.length > 0) {
      const errorMessage = `Produk berikut tidak ditemukan di sistem: ${missingProducts.join(", ")}. Silakan buat produk tersebut terlebih dahulu atau hapus dari file import.`;

      // Create notification for missing products
      try {
        await createSystemNotification(
          "error",
          "Import Penjualan Gagal - Produk Tidak Ditemukan",
          `Import penjualan dibatalkan karena ${missingProducts.length} produk tidak ditemukan di sistem. ${errorMessage}`,
          false
        );
      } catch (notificationError) {
        console.error(
          "Failed to create validation notification:",
          notificationError
        );
      }

      return {
        error: errorMessage,
        summary: {
          salesCreated: 0,
          customersCreated: 0,
          errors: [errorMessage],
        },
      };
    }

    console.log(
      `[IMPORT] Product validation passed - all ${uniqueProductNames.size} products found`
    );

    if (salesData.length > 500) {
      return {
        error: "Terlalu banyak data. Maksimal 500 transaksi per import",
        summary: {
          salesCreated: 0,
          customersCreated: 0,
          errors: ["Jumlah data melebihi batas maksimal 500 transaksi"],
        },
      };
    }

    // Process import in smaller batches
    const BATCH_SIZE = 10;
    let totalSalesCreated = 0;
    let totalCustomersCreated = 0;
    const allErrors: string[] = [];

    // Process data in batches
    for (
      let batchStart = 0;
      batchStart < salesData.length;
      batchStart += BATCH_SIZE
    ) {
      const batchEnd = Math.min(batchStart + BATCH_SIZE, salesData.length);
      const batch = salesData.slice(batchStart, batchEnd);

      console.log(
        `[IMPORT] Processing batch ${Math.floor(batchStart / BATCH_SIZE) + 1}/${Math.ceil(salesData.length / BATCH_SIZE)} (${batch.length} sales)`
      );

      // Process each row individually to avoid transaction rollback issues
      let batchSalesCreated = 0;
      let batchCustomersCreated = 0;
      const batchErrors: string[] = [];

      for (const row of batch) {
        try {
          const rowResult = await db.$transaction(
            async (tx) => {
              // Validate and sanitize required fields
              const saleDate = sanitizeDate(row["Tanggal Penjualan"]);
              const productName = sanitizeString(row["Nama Produk"]);
              const quantity = sanitizeNumber(row["Quantity"]);
              const salePrice = sanitizeNumber(row["Harga Jual"]);
              const isWholesale = sanitizeBoolean(row["Harga Grosir"]);

              if (!productName) {
                throw new Error("Nama Produk tidak boleh kosong");
              }

              if (quantity <= 0) {
                throw new Error("Quantity harus lebih dari 0");
              }

              if (salePrice <= 0) {
                throw new Error("Harga Jual harus lebih dari 0");
              }

              // Find existing product
              const existingProduct = await tx.product.findFirst({
                where: {
                  name: productName,
                  userId: effectiveUserId,
                },
              });

              if (!existingProduct) {
                throw new Error(
                  `Produk "${productName}" tidak ditemukan di sistem`
                );
              }

              // Check stock availability
              if (existingProduct.stock < quantity) {
                throw new Error(
                  `Stok tidak mencukupi. Tersedia: ${existingProduct.stock}, Dibutuhkan: ${quantity}`
                );
              }

              // Handle customer
              let customerId: string | null = null;
              let customersCreated = 0;
              const customerName = sanitizeString(row["Pelanggan"]);
              if (customerName) {
                let customer = await tx.customer.findFirst({
                  where: {
                    name: customerName,
                    userId: effectiveUserId,
                  },
                });

                if (!customer) {
                  try {
                    const newCustomerId = await generateCustomerId();
                    customer = await tx.customer.create({
                      data: {
                        id: newCustomerId,
                        name: customerName,
                        userId: effectiveUserId,
                      },
                    });
                    customersCreated++;
                  } catch (customerError) {
                    // If customer creation fails, try to find if it was created by another concurrent process
                    customer = await tx.customer.findFirst({
                      where: {
                        name: customerName,
                        userId: effectiveUserId,
                      },
                    });

                    if (!customer) {
                      // If still not found, throw the original error
                      throw customerError;
                    }
                  }
                }
                customerId = customer.id;
              }

              // Handle warehouse
              let warehouseId: string | null = null;
              const warehouseName = sanitizeString(row["Gudang"]);
              if (warehouseName) {
                const warehouse = await tx.warehouse.findFirst({
                  where: {
                    name: warehouseName,
                    userId: effectiveUserId,
                  },
                });

                if (!warehouse) {
                  throw new Error(
                    `Gudang "${warehouseName}" tidak ditemukan di sistem`
                  );
                }
                warehouseId = warehouse.id;
              }

              // Calculate discounts and totals
              const discountPercentage = sanitizeNumber(row["Diskon (%)"]);
              const discountAmount = sanitizeNumber(row["Diskon (Rp)"]);
              const taxPercentage = sanitizeNumber(row["PPN (%)"]);

              const subtotal = quantity * salePrice;
              const finalDiscountAmount =
                discountAmount > 0
                  ? discountAmount
                  : (subtotal * discountPercentage) / 100;
              const afterDiscount = subtotal - finalDiscountAmount;
              const taxAmount = (afterDiscount * taxPercentage) / 100;
              const totalAmount = afterDiscount + taxAmount;

              // Generate transaction number
              const currentYear = new Date().getFullYear().toString().slice(-2);
              const lastSale = await tx.sale.findFirst({
                where: { userId: effectiveUserId },
                orderBy: { createdAt: "desc" },
              });

              let nextNumber = 1;
              if (lastSale?.transactionNumber) {
                const match =
                  lastSale.transactionNumber.match(/JUAL-\d{2}J(\d{6})/);
                if (match) {
                  nextNumber = parseInt(match[1]) + 1;
                }
              }

              const transactionNumber = `JUAL-${currentYear}J${nextNumber.toString().padStart(6, "0")}`;

              // Parse status field
              const statusValue = sanitizeString(row["Status Pembayaran"]);
              const status = statusValue === "Lunas" ? "LUNAS" : "BELUM_LUNAS";

              // Create sale
              const saleId = await generateSalesId(effectiveUserId, tx);
              const newSale = await tx.sale.create({
                data: {
                  id: saleId,
                  saleDate,
                  totalAmount,
                  transactionNumber,
                  invoiceRef: sanitizeString(row["No. Faktur"]) || null,
                  memo: sanitizeString(row["Memo"]) || null,
                  status: status as any, // Cast to match Prisma enum
                  userId: effectiveUserId,
                  customerId,
                  warehouseId,
                  isDraft: false,
                },
              });

              // Create sale item
              await tx.saleItem.create({
                data: {
                  quantity,
                  priceAtSale: salePrice,
                  unit: sanitizeString(row["Satuan"]) || "Pcs",
                  discountPercentage:
                    discountPercentage > 0 ? discountPercentage : null,
                  discountAmount:
                    finalDiscountAmount > 0 ? finalDiscountAmount : null,
                  tax: taxPercentage > 0 ? `${taxPercentage}%` : null,
                  isWholesale,
                  saleId: newSale.id,
                  productId: existingProduct.id,
                },
              });

              // Update product stock
              await tx.product.update({
                where: { id: existingProduct.id },
                data: {
                  stock: {
                    decrement: quantity,
                  },
                },
              });

              return {
                salesCreated: 1,
                customersCreated,
              };
            },
            { timeout: 30000 }
          );

          // Accumulate successful results
          batchSalesCreated += rowResult.salesCreated;
          batchCustomersCreated += rowResult.customersCreated;
        } catch (error) {
          console.error(
            `[IMPORT] Error processing row ${row._rowIndex}:`,
            error
          );

          // Provide more specific error messages
          let errorMessage = "Error tidak diketahui";
          if (error instanceof Error) {
            if (error.message.includes("Unique constraint failed")) {
              if (error.message.includes("customer")) {
                errorMessage = "Customer dengan data yang sama sudah ada";
              } else if (error.message.includes("transactionNumber")) {
                errorMessage = "Nomor transaksi sudah ada";
              } else {
                errorMessage = "Data duplikat ditemukan";
              }
            } else if (
              error.message.includes("Produk") &&
              error.message.includes("tidak ditemukan")
            ) {
              errorMessage = error.message;
            } else if (error.message.includes("Gagal menghasilkan")) {
              errorMessage = error.message;
            } else {
              errorMessage = error.message;
            }
          }

          batchErrors.push(`Baris ${row._rowIndex}: ${errorMessage}`);
        }
      }

      // Accumulate batch results
      totalSalesCreated += batchSalesCreated;
      totalCustomersCreated += batchCustomersCreated;
      allErrors.push(...batchErrors);

      console.log(
        `[IMPORT] Batch completed: ${batchSalesCreated} sales created`
      );
    }

    // Return final results
    const finalResult = {
      salesCreated: totalSalesCreated,
      customersCreated: totalCustomersCreated,
      errors: allErrors,
    };

    // Create notifications based on import results
    try {
      if (allErrors.length > 0 && totalSalesCreated === 0) {
        // Complete failure notification
        await createSystemNotification(
          "error",
          "Import Penjualan Gagal",
          `Import penjualan gagal sepenuhnya. ${allErrors.length} error ditemukan. Silakan periksa file dan coba lagi.`,
          false
        );

        return {
          error: "Import gagal",
          summary: finalResult,
        };
      } else if (allErrors.length > 0 && totalSalesCreated > 0) {
        // Partial success notification
        await createSystemNotification(
          "warning",
          "Import Penjualan Sebagian Berhasil",
          `Import penjualan selesai dengan ${totalSalesCreated} penjualan berhasil diimpor, namun ${allErrors.length} baris gagal diproses. ${totalCustomersCreated} customer baru telah dibuat.`,
          false
        );
      } else {
        // Complete success notification
        await createSystemNotification(
          "success",
          "Import Penjualan Berhasil",
          `Import penjualan berhasil! ${totalSalesCreated} penjualan berhasil diimpor. ${totalCustomersCreated} customer baru telah dibuat.`,
          false
        );
      }
    } catch (notificationError) {
      console.error("Failed to create import notification:", notificationError);
    }

    return {
      success: `Import berhasil! ${totalSalesCreated} penjualan berhasil diimpor.`,
      summary: finalResult,
    };
  } catch (error) {
    console.error("Import error:", error);

    // Create error notification
    try {
      await createSystemNotification(
        "error",
        "Import Penjualan Error",
        `Terjadi kesalahan sistem saat mengimpor penjualan: ${error instanceof Error ? error.message : "Unknown error"}`,
        false
      );
    } catch (notificationError) {
      console.error("Failed to create error notification:", notificationError);
    }

    return {
      error: "Gagal memproses file import",
      summary: {
        salesCreated: 0,
        customersCreated: 0,
        errors: [error instanceof Error ? error.message : "Unknown error"],
      },
    };
  }
};
