"use server";

import { db } from "@/lib/prisma";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";
import { generateCustomerId, generateServiceId } from "@/lib/generate-id";
import { createSystemNotification } from "@/lib/create-system-notification";
import * as XLSX from "xlsx";
import { DeviceType, ServiceStatus } from "@prisma/client";

interface ImportSummary {
  servicesCreated: number;
  customersCreated: number;
  errors: string[];
}

interface ImportResult {
  success?: string;
  error?: string;
  summary: ImportSummary;
}

// Utility function to sanitize string inputs
const sanitizeString = (value: any): string => {
  if (value === null || value === undefined || value === "") return "";
  return String(value).trim().substring(0, 255);
};

// Utility function to sanitize number inputs
const sanitizeNumber = (value: any): number => {
  if (value === null || value === undefined || value === "") return 0;
  const num = Number(value);
  return isNaN(num) ? 0 : Math.max(0, num);
};

// Utility function to sanitize date inputs
const sanitizeDate = (value: any): Date => {
  if (value === null || value === undefined || value === "") {
    return new Date();
  }

  // Handle Excel date serial numbers
  if (typeof value === "number") {
    // Excel date serial number (days since 1900-01-01)
    const excelEpoch = new Date(1900, 0, 1);
    const date = new Date(
      excelEpoch.getTime() + (value - 1) * 24 * 60 * 60 * 1000
    );
    return isNaN(date.getTime()) ? new Date() : date;
  }

  // Handle string dates
  if (typeof value === "string") {
    const date = new Date(value);
    return isNaN(date.getTime()) ? new Date() : date;
  }

  // Handle Date objects
  if (value instanceof Date) {
    return isNaN(value.getTime()) ? new Date() : value;
  }

  return new Date();
};

// Utility function to sanitize service status
const sanitizeStatus = (value: any): ServiceStatus => {
  const status = String(value).trim().toUpperCase();

  switch (status) {
    case 'DITERIMA':
      return 'DITERIMA';
    case 'PROSES_MENUNGGU_SPAREPART':
      return 'PROSES_MENUNGGU_SPAREPART';
    case 'SELESAI_BELUM_DIAMBIL':
      return 'SELESAI_BELUM_DIAMBIL';
    case 'SELESAI_SUDAH_DIAMBIL':
      return 'SELESAI_SUDAH_DIAMBIL';
    // Mappings from old function for flexibility
    case 'DALAM PROSES':
      return 'PROSES_MENUNGGU_SPAREPART';
    case 'SELESAI':
      return 'SELESAI_BELUM_DIAMBIL';
    case 'DIAMBIL':
      return 'SELESAI_SUDAH_DIAMBIL';
    case 'RECEIVED':
        return 'DITERIMA';
    case 'IN_PROGRESS':
        return 'PROSES_MENUNGGU_SPAREPART';
    case 'COMPLETED':
        return 'SELESAI_BELUM_DIAMBIL';
    case 'DELIVERED':
        return 'SELESAI_SUDAH_DIAMBIL';
    default:
      return 'DITERIMA';
  }
};

const sanitizeDeviceType = (value: any): DeviceType => {
  const strValue = String(value).toUpperCase().trim();
  switch (strValue) {
    case 'LAPTOP':
      return 'LAPTOP';
    case 'DESKTOP':
      return 'DESKTOP';
    case 'PHONE':
      return 'PHONE';
    case 'TABLET':
      return 'TABLET';
    case 'PRINTER':
      return 'PRINTER';
    default:
      return 'OTHER';
  }
};

// Main import function for services
export const importServices = async (
  arrayBuffer: ArrayBuffer
): Promise<ImportResult> => {
  try {
    console.log("[IMPORT] Starting services import process");

    // Get effective user ID
    const effectiveUserId = await getEffectiveUserId();
    if (!effectiveUserId) {
      return {
        error: "Tidak terautentikasi",
        summary: {
          servicesCreated: 0,
          customersCreated: 0,
          errors: ["User tidak terautentikasi"],
        },
      };
    }

    // Parse Excel file
    const workbook = XLSX.read(arrayBuffer, { type: "array" });
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

    console.log(`[IMPORT] Parsed ${data.length} rows from Excel`);

    if (data.length < 2) {
      return {
        error: "File Excel kosong atau tidak memiliki data",
        summary: {
          servicesCreated: 0,
          customersCreated: 0,
          errors: ["File tidak memiliki data yang valid"],
        },
      };
    }

    // Skip header rows (first 4 rows based on template)
    const dataRows = data.slice(4);
    console.log(`[IMPORT] Processing ${dataRows.length} data rows`);

    if (dataRows.length === 0) {
      return {
        error: "Tidak ada data untuk diimpor",
        summary: {
          servicesCreated: 0,
          customersCreated: 0,
          errors: ["Tidak ada data setelah header"],
        },
      };
    }

    // Process data in transaction
    const result = await db.$transaction(async (tx) => {
      let servicesCreated = 0;
      let customersCreated = 0;
      const errors: string[] = [];

      for (let i = 0; i < dataRows.length; i++) {
        const row = dataRows[i] as any[];
        const rowNumber = i + 5; // Adjust for header rows

        try {
          // Skip empty rows
          if (!row || row.length === 0 || !row[0]) {
            continue;
          }

          // Extract and sanitize data
          const receivedDate = sanitizeDate(row[0]);
          const customerName = sanitizeString(row[1]);
          const customerPhone = sanitizeString(row[2]);
          const customerEmail = sanitizeString(row[3]);
          const deviceType = sanitizeDeviceType(row[4]);
          const deviceBrand = sanitizeString(row[5]);
          const deviceModel = sanitizeString(row[6]);
          const complaint = sanitizeString(row[7]);
          const estimatedCost = sanitizeNumber(row[8]);
          const warrantyPeriod = sanitizeNumber(row[9]);
          const status = sanitizeStatus(row[10]);
          const notes = sanitizeString(row[11]);

          // Validate required fields
          if (!customerName) {
            errors.push(`Baris ${rowNumber}: Nama pelanggan wajib diisi`);
            continue;
          }

          if (!deviceType) {
            errors.push(`Baris ${rowNumber}: Jenis perangkat wajib diisi`);
            continue;
          }

          if (!complaint) {
            errors.push(`Baris ${rowNumber}: Keluhan wajib diisi`);
            continue;
          }

          // Find or create customer
          let customer = null;
          if (customerEmail) {
            customer = await tx.customer.findFirst({
              where: {
                email: customerEmail,
                userId: effectiveUserId,
              },
            });
          }

          if (!customer && customerPhone) {
            customer = await tx.customer.findFirst({
              where: {
                phone: customerPhone,
                userId: effectiveUserId,
              },
            });
          }

          if (!customer) {
            // Create new customer
            const customerId = await generateCustomerId();
            customer = await tx.customer.create({
              data: {
                id: customerId,
                name: customerName,
                phone: customerPhone || null,
                email: customerEmail || null,
                userId: effectiveUserId,
              },
            });
            customersCreated++;
          }

          // Generate service number
          const currentYear = new Date().getFullYear().toString().slice(-2);
          const lastService = await tx.service.findFirst({
            where: {
              userId: effectiveUserId,
              serviceNumber: {
                startsWith: `SERV-${currentYear}S`,
              },
            },
            orderBy: {
              serviceNumber: "desc",
            },
          });

          let nextNumber = 1;
          if (lastService?.serviceNumber) {
            const match = lastService.serviceNumber.match(/SERV-\d{2}S(\d{6})/);
            if (match) {
              nextNumber = parseInt(match[1]) + 1;
            }
          }

          const serviceNumber = `SERV-${currentYear}S${nextNumber.toString().padStart(6, "0")}`;

          // Create service
          const serviceId = await generateServiceId(effectiveUserId, tx);
          await tx.service.create({
            data: {
              id: serviceId,
              serviceNumber,
              customerId: customer.id,
              customerName,
              customerPhone,
              customerEmail: customerEmail || null,
              deviceType,
              deviceBrand,
              deviceModel,
              problemDescription: complaint,
              estimatedCost: estimatedCost > 0 ? estimatedCost : null,
              warrantyPeriod: warrantyPeriod > 0 ? warrantyPeriod : null,
              status,
              diagnosisNotes: notes || null,
              receivedDate,
              userId: effectiveUserId,
            },
          });

          servicesCreated++;
        } catch (error) {
          console.error(`[IMPORT] Error processing row ${rowNumber}:`, error);
          errors.push(
            `Baris ${rowNumber}: ${error instanceof Error ? error.message : "Error tidak diketahui"}`
          );
        }
      }

      return { servicesCreated, customersCreated, errors };
    });

    // Create system notification
    await createSystemNotification(
      "success",
      "Import Servis Selesai",
      `Berhasil mengimpor ${result.servicesCreated} servis dan membuat ${result.customersCreated} pelanggan baru.`
    );

    console.log(`[IMPORT] Import completed: ${result.servicesCreated} services created`);

    return {
      success: `Berhasil mengimpor ${result.servicesCreated} servis`,
      summary: result,
    };
  } catch (error) {
    console.error("[IMPORT] Import failed:", error);
    return {
      error: `Gagal mengimpor data: ${error instanceof Error ? error.message : "Error tidak diketahui"}`,
      summary: {
        servicesCreated: 0,
        customersCreated: 0,
        errors: [error instanceof Error ? error.message : "Error tidak diketahui"],
      },
    };
  }
};
