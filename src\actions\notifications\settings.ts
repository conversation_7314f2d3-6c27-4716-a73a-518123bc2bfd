"use server";

import { db } from "@/lib/prisma";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";
import { z } from "zod";

// Define schema for notification settings
const NotificationSettingsSchema = z.object({
  emailEnabled: z.boolean(),
  emailInfoEnabled: z.boolean(),
  emailWarningEnabled: z.boolean(),
  emailSuccessEnabled: z.boolean(),
  emailErrorEnabled: z.boolean(),
  emailPurchaseEnabled: z.boolean(),
  emailProductAdditionEnabled: z.boolean(),
  emailServiceAdditionEnabled: z.boolean(),
  dailySummary: z.boolean(),
  weeklySummary: z.boolean(),
  dailyReports: z.boolean(),
  dailyReportsTime: z.string().optional().default("17:00"), // 5 PM UTC+7
  operatingHoursStart: z.string().optional().default("08:00"),
  operatingHoursEnd: z.string().optional().default("17:00"),
  notificationHours: z
    .array(z.string())
    .optional()
    .default(["09:00", "12:00", "17:00"])
    .transform((val) => val.join(",")), // Transform array to string for storage
});

const NotificationSettingsResponseSchema = NotificationSettingsSchema.extend({
  notificationHours: z.string().transform((val) => val.split(",")), // Transform string to array for response
});

export type NotificationSettingsType = z.infer<
  typeof NotificationSettingsResponseSchema
>;

// Get notification settings for the current user
export async function getNotificationSettings(): Promise<{
  success: boolean;
  data?: NotificationSettingsType;
  error?: string;
}> {
  try {
    const userId = await getEffectiveUserId();

    if (!userId) {
      return { success: false, error: "Tidak terautentikasi!" };
    }

    // Get notification settings from database
    let settings = await db.notificationSettings.findUnique({
      where: { userId },
    });

    // If settings don't exist, create default settings
    if (!settings) {
      settings = await db.notificationSettings.create({
        data: {
          userId,
          emailEnabled: true,
          emailInfoEnabled: true,
          emailWarningEnabled: true,
          emailSuccessEnabled: true,
          emailErrorEnabled: true,
          emailPurchaseEnabled: true,
          emailProductAdditionEnabled: true,
          emailServiceAdditionEnabled: true,
          dailySummary: false,
          weeklySummary: true,
          dailyReports: false,
          dailyReportsTime: "17:00",
          operatingHoursStart: "08:00",
          operatingHoursEnd: "17:00",
          notificationHours: ["09:00", "12:00", "17:00"].join(","),
        },
      });
    }

    const parsedSettings =
      NotificationSettingsResponseSchema.safeParse(settings);

    if (!parsedSettings.success) {
      console.error(
        "Error parsing notification settings from DB:",
        parsedSettings.error
      );
      return {
        success: false,
        error: "Gagal memuat pengaturan notifikasi: Data tidak valid.",
      };
    }

    return {
      success: true,
      data: parsedSettings.data,
    };
  } catch (error) {
    console.error("Error getting notification settings:", error);
    return {
      success: false,
      error: "Gagal mendapatkan pengaturan notifikasi.",
    };
  }
}

// Update notification settings for the current user
export async function updateNotificationSettings(
  settings: NotificationSettingsType
): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    const userId = await getEffectiveUserId();

    if (!userId) {
      return { success: false, error: "Tidak terautentikasi!" };
    }

    // Validate settings
    const validatedSettings = NotificationSettingsSchema.safeParse(settings);
    if (!validatedSettings.success) {
      return {
        success: false,
        error: "Pengaturan notifikasi tidak valid.",
      };
    }

    // Convert notificationHours array to string for database storage
    const dataToUpdate = {
      ...validatedSettings.data,
      notificationHours: validatedSettings.data.notificationHours,
    };

    // Update or create notification settings
    await db.notificationSettings.upsert({
      where: { userId },
      update: dataToUpdate,
      create: {
        userId,
        ...dataToUpdate,
      },
    });

    return { success: true };
  } catch (error) {
    console.error("Error updating notification settings:", error);
    return {
      success: false,
      error: "Gagal memperbarui pengaturan notifikasi.",
    };
  }
}

// Test notification email
export async function sendTestNotificationEmail(): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    const userId = await getEffectiveUserId();

    if (!userId) {
      return { success: false, error: "Tidak terautentikasi!" };
    }

    // Import dynamically to avoid circular dependencies
    const { sendNotificationEmail } = await import(
      "@/lib/send-notification-email"
    );

    // Send test notification email
    const result = await sendNotificationEmail({
      userId,
      title: "Email Notifikasi Uji Coba",
      message:
        "Ini adalah email uji coba untuk memastikan pengaturan notifikasi email Anda berfungsi dengan baik.",
      type: "info",
    });

    if (!result.success) {
      return {
        success: false,
        error: result.error || "Gagal mengirim email uji coba.",
      };
    }

    return { success: true };
  } catch (error) {
    console.error("Error sending test notification email:", error);
    return {
      success: false,
      error: "Gagal mengirim email uji coba.",
    };
  }
}

// Send test daily report email
export async function sendTestDailyReportEmail(): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    const userId = await getEffectiveUserId();

    if (!userId) {
      return { success: false, error: "Tidak terautentikasi!" };
    }

    // Get user information
    const user = await db.user.findUnique({
      where: { id: userId },
      select: { email: true, name: true, username: true },
    });

    if (!user || !user.email) {
      return {
        success: false,
        error: "User tidak ditemukan atau tidak memiliki email",
      };
    }

    // Import dynamically to avoid circular dependencies
    const { sendDailyReportEmail } = await import("@/lib/daily-reports");

    // Send test daily report email
    const result = await sendDailyReportEmail(
      userId,
      user.email,
      user.name || user.username || "Pengguna",
      new Date()
    );

    if (!result.success) {
      return {
        success: false,
        error: result.error || "Gagal mengirim email laporan harian uji coba.",
      };
    }

    return { success: true };
  } catch (error) {
    console.error("Error sending test daily report email:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Gagal mengirim email laporan harian uji coba.",
    };
  }
}
