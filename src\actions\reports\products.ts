"use server";

import { db } from "@/lib/prisma";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";
import { AdvancedFilters, getDateRange } from "./_utils";
import { startOfDay, endOfDay } from "date-fns";

// Enhanced function to get product report data with advanced filters
export const getProductReportData = async (filters: AdvancedFilters) => {
  try {
    const effectiveUserId = await getEffectiveUserId();
    if (!effectiveUserId) {
      return { error: "Tidak terautentikasi!" };
    }

    // Use custom date range if provided, otherwise use dateRange
    let startDate: Date, endDate: Date;
    if (filters.startDate && filters.endDate) {
      startDate = startOfDay(filters.startDate);
      endDate = endOfDay(filters.endDate);
    } else {
      const dateRange = getDateRange(filters.dateRange);
      startDate = dateRange.startDate;
      endDate = dateRange.endDate;
    }

    // Build where clause for products
    const whereClause: any = {
      userId: effectiveUserId,
    };

    // Add category filter if provided
    if (filters.category) {
      whereClause.category = {
        name: {
          contains: filters.category,
          mode: "insensitive",
        },
      };
    }

    const products = await db.product.findMany({
      where: whereClause,
      include: {
        category: true,
        variants: true, // Fetch all variant fields
        unitModel: true,
      },
      orderBy: {
        id: "asc",
      },
    });

    // The data is returned directly, to be processed by the export utility
    const productData = products.map((product) => {
      return {
        ...product,
        price: product.price.toNumber(),
        cost: product.cost?.toNumber() ?? null,
        wholesalePrice: product.wholesalePrice?.toNumber() ?? null,
        length: product.length?.toNumber() ?? null,
        width: product.width?.toNumber() ?? null,
        height: product.height?.toNumber() ?? null,
        variants: product.variants.map((variant) => ({
          ...variant,
          price: variant.price?.toNumber() ?? null,
        })),
      };
    });

    return {
      success: true,
      data: productData,
    };
  } catch (error) {
    console.error("Error fetching product report data with filters:", error);
    return {
      error: "Gagal mengambil data laporan produk.",
    };
  }
};
