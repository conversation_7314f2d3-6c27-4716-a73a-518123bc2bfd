"use server";

import { db } from "@/lib/prisma";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";

// Function to get supplier report data
export const getSupplierReportData = async () => {
  try {
    const effectiveUserId = await getEffectiveUserId();
    if (!effectiveUserId) {
      return { error: "Tidak terautentikasi!" };
    }

    const suppliers = await db.supplier.findMany({
      where: {
        userId: effectiveUserId,
      },
      include: {
        purchases: true, // Include purchases to calculate stats
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    const supplierData = suppliers.map((supplier) => ({
      id: supplier.id,
      name: supplier.name,
      contactName: supplier.contactName || "-",
      email: supplier.email || "-",
      phone: supplier.phone || "-",
      address: supplier.address || "-",
      notes: supplier.notes || "-",
      createdAt: supplier.createdAt.toISOString(),
      updatedAt: supplier.updatedAt.toISOString(),
      totalTransactions: supplier.purchases.length,
      totalPurchases: supplier.purchases.reduce(
        (sum, purchase) => sum + purchase.totalAmount.toNumber(),
        0
      ),
    }));

    return {
      success: true,
      data: supplierData,
    };
  } catch (error) {
    console.error("Error fetching supplier report data:", error);
    return {
      error: "Gagal mengambil data laporan supplier.",
    };
  }
};
