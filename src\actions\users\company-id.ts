"use server";

import { db } from "@/lib/prisma";

/**
 * Generates the next available company ID in the format IP000001, IP000002, etc.
 * This function is used during user registration and for updating existing users.
 * It uses a transaction to prevent race conditions.
 */
export async function generateNextCompanyId(): Promise<string> {
  try {
    // Use a transaction to prevent race conditions
    return await db.$transaction(async (prisma) => {
      // Find the highest existing company ID
      const highestAdditionalInfo = await prisma.businessInfo.findFirst({
        where: {
          companyId: {
            not: null,
            startsWith: "IP",
          },
        },
        orderBy: {
          companyId: "desc",
        },
        select: {
          companyId: true,
        },
      });

      let nextNumber = 1;

      if (highestAdditionalInfo?.companyId) {
        // Extract the number part from the highest ID
        const match = highestAdditionalInfo.companyId.match(/IP(\d+)/);
        if (match && match[1]) {
          nextNumber = parseInt(match[1], 10) + 1;
        }
      }

      // Format the new company ID with leading zeros
      return `IP${nextNumber.toString().padStart(6, "0")}`;
    });
  } catch (error) {
    console.error("Error generating company ID:", error);
    // Fallback to a timestamp-based ID if transaction fails
    const timestamp = Date.now().toString().slice(-6);
    return `IP${timestamp}`;
  }
}

/**
 * Assigns company IDs to all users who don't have one yet.
 * This is used for migrations to update existing users.
 */
export async function assignCompanyIdsToExistingUsers(): Promise<{
  success: boolean;
  count: number;
  error?: string;
}> {
  try {
    // Find all users without a company ID (users who don't have BusinessInfo or have null companyId)
    const usersWithoutCompanyId = await db.user.findMany({
      where: {
        role: "OWNER", // Only assign to owners, not employees
        OR: [
          {
            businessInfo: null, // Users without BusinessInfo
          },
          {
            businessInfo: {
              companyId: null, // Users with BusinessInfo but no companyId
            },
          },
        ],
      },
      select: {
        id: true,
      },
    });

    if (usersWithoutCompanyId.length === 0) {
      return {
        success: true,
        count: 0,
        error: "No users found without company IDs",
      };
    }

    // Process users in batches to avoid long-running transactions
    const batchSize = 10;
    let updatedCount = 0;

    for (let i = 0; i < usersWithoutCompanyId.length; i += batchSize) {
      const batch = usersWithoutCompanyId.slice(i, i + batchSize);

      // Use a transaction for each batch
      await db.$transaction(async (prisma) => {
        for (const user of batch) {
          const newCompanyId = await generateNextCompanyId();

          // Create or update BusinessInfo with the company ID
          await prisma.businessInfo.upsert({
            where: {
              userId: user.id,
            },
            create: {
              userId: user.id,
              companyId: newCompanyId,
            },
            update: {
              companyId: newCompanyId,
            },
          });

          updatedCount++;
        }
      });
    }

    return {
      success: true,
      count: updatedCount,
    };
  } catch (error) {
    console.error("Error assigning company IDs:", error);
    return {
      success: false,
      count: 0,
      error: `Failed to assign company IDs to existing users: ${error instanceof Error ? error.message : String(error)}`,
    };
  }
}

/**
 * Gets a user's company ID by their user ID.
 * Returns null if the user doesn't have a company ID or doesn't exist.
 */
export async function getUserCompanyId(userId: string): Promise<string | null> {
  try {
    const businessInfo = await db.businessInfo.findUnique({
      where: {
        userId: userId,
      },
      select: {
        companyId: true,
      },
    });

    return businessInfo?.companyId || null;
  } catch (error) {
    console.error("Error getting user company ID:", error);
    return null;
  }
}

/**
 * Manually assigns a company ID to a specific user.
 * This is useful for administrative purposes.
 */
export async function assignCompanyIdToUser(userId: string): Promise<{
  success: boolean;
  companyId?: string;
  error?: string;
}> {
  try {
    // Check if user exists and get their role
    const existingUser = await db.user.findUnique({
      where: {
        id: userId,
      },
      select: {
        role: true,
        businessInfo: {
          select: {
            companyId: true,
          },
        },
      },
    });

    if (!existingUser) {
      return {
        success: false,
        error: "User not found",
      };
    }

    if (existingUser.businessInfo?.companyId) {
      return {
        success: true,
        companyId: existingUser.businessInfo.companyId,
        error: "User already has a company ID",
      };
    }

    if (existingUser.role !== "OWNER") {
      return {
        success: false,
        error: "Only owner accounts can have company IDs",
      };
    }

    // Generate a new company ID
    const newCompanyId = await generateNextCompanyId();

    // Create or update BusinessInfo with the new company ID
    await db.businessInfo.upsert({
      where: {
        userId: userId,
      },
      create: {
        userId: userId,
        companyId: newCompanyId,
      },
      update: {
        companyId: newCompanyId,
      },
    });

    return {
      success: true,
      companyId: newCompanyId,
    };
  } catch (error) {
    console.error("Error assigning company ID to user:", error);
    return {
      success: false,
      error: `Failed to assign company ID: ${error instanceof Error ? error.message : String(error)}`,
    };
  }
}
