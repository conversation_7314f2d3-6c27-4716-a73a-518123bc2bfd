"use server";

import { db } from "@/lib/prisma";
import { auth } from "@/lib/auth";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";
import { SubscriptionPlan } from "@prisma/client";
import {
  SUBSCRIPTION_PLANS,
  getPlanDetails,
  isUserInTrial,
} from "@/lib/subscription";

export interface SubscriptionInfo {
  currentPlan: SubscriptionPlan;
  planName: string;
  isActive: boolean;
  expiryDate: Date | null;
  trialStartDate: Date | null;
  trialEndDate: Date | null;
  isTrialActive: boolean;
  limits: {
    maxProducts: number | null;
    maxTransactionsPerMonth: number | null;
    maxUsers: number | null;
    maxSuppliers: number | null;
    maxCustomers: number | null;
  };
  usage: {
    currentProducts: number;
    currentTransactions: number;
    currentUsers: number;
    currentSuppliers: number;
    currentCustomers: number;
  };
  features: string[];
  limitations: string[];
}

/**
 * Get comprehensive subscription information for the current user
 */
export async function getSubscriptionInfo(): Promise<{
  success: boolean;
  data?: SubscriptionInfo;
  error?: string;
}> {
  try {
    const session = await auth();
    const effectiveUserId = await getEffectiveUserId();

    if (!session?.user?.id || !effectiveUserId) {
      return { success: false, error: "Tidak terautentikasi" };
    }

    // Get user data with subscription info
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      select: {
        currentPlan: true,
        subscriptionExpiry: true,
        trialStartDate: true,
        trialEndDate: true,
        isTrialActive: true,
      },
    });

    if (!user) {
      return { success: false, error: "User tidak ditemukan" };
    }

    // Get plan details from subscription configuration
    const planDetails = getPlanDetails(user.currentPlan);

    // Get current usage statistics
    const [
      currentProducts,
      currentTransactions,
      employeeCount,
      currentSuppliers,
      currentCustomers,
    ] = await Promise.all([
      db.product.count({ where: { userId: effectiveUserId } }),
      db.sale.count({
        where: {
          userId: effectiveUserId,
          saleDate: {
            gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1), // This month
          },
        },
      }),
      db.employee.count({ where: { ownerId: effectiveUserId } }), // Use ownerId
      db.supplier.count({ where: { userId: effectiveUserId } }),
      db.customer.count({ where: { userId: effectiveUserId } }),
    ]);

    const currentUsers = employeeCount + 1; // +1 for owner

    // Check if subscription is active
    const isActive = user.subscriptionExpiry
      ? new Date() < user.subscriptionExpiry
      : true; // Free plan is always active

    // Check if user is in trial
    const inTrial = isUserInTrial({
      trialStartDate: user.trialStartDate,
      trialEndDate: user.trialEndDate,
      isTrialActive: user.isTrialActive,
    });

    const subscriptionInfo: SubscriptionInfo = {
      currentPlan: user.currentPlan,
      planName: planDetails.name,
      isActive,
      expiryDate: user.subscriptionExpiry,
      trialStartDate: user.trialStartDate,
      trialEndDate: user.trialEndDate,
      isTrialActive: inTrial,
      limits: {
        maxProducts: planDetails.limits.maxProducts,
        maxTransactionsPerMonth: planDetails.limits.maxTransactionsPerMonth,
        maxUsers: planDetails.limits.maxUsers,
        maxSuppliers: planDetails.limits.maxSuppliers,
        maxCustomers: planDetails.limits.maxCustomers,
      },
      usage: {
        currentProducts,
        currentTransactions,
        currentUsers,
        currentSuppliers,
        currentCustomers,
      },
      features: planDetails.features,
      limitations: planDetails.limitations,
    };

    return { success: true, data: subscriptionInfo };
  } catch (error) {
    console.error("Error fetching subscription info:", error);
    return { success: false, error: "Gagal mengambil informasi langganan" };
  }
}
