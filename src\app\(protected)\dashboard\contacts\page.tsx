import React from "react";
import { Metadata } from "next";
import DashboardLayout from "@/components/layout/dashboardlayout";
import ContactsPage from "@/components/pages/dashboard/contacts/contacts";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { getCustomers } from "@/actions/entities/customers";
import { getSuppliers } from "@/actions/entities/suppliers";

export const metadata: Metadata = {
  title: "Kontak | KivaPOS",
  description: "Kelola data pelanggan dan supplier Anda",
};

const Contacts = async () => {
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/login");
  }

  // Fetch contact data
  const [customersResult, suppliersResult] = await Promise.all([
    getCustomers(),
    getSuppliers(),
  ]);

  if (customersResult.error) {
    console.error("Error fetching customers:", customersResult.error);
  }

  if (suppliersResult.error) {
    console.error("Error fetching suppliers:", suppliersResult.error);
  }

  return (
    <DashboardLayout>
      <div className="py-6 px-1.5 sm:px-2 md:px-4">
        <ContactsPage
          customers={customersResult.customers || []}
          suppliers={suppliersResult.suppliers || []}
        />
      </div>
    </DashboardLayout>
  );
};

export default Contacts;
