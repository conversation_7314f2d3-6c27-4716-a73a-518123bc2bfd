import React from "react";
import { Metadata } from "next";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { Role } from "@prisma/client";
import EventDiscountManagement from "@/components/pages/dashboard/event-discounts/event-discounts";
import { OnboardingGuard } from "@/components/auth/onboarding-guard";
import { DatabaseErrorWrapper } from "@/components/ui/database-error-wrapper";

export const metadata: Metadata = {
  title: "Diskon Event | KivaPOS",
  description: "Kelola diskon event untuk produk Anda",
};

export default async function EventDiscountsPage() {
  const session = await auth();

  if (!session?.user) {
    redirect("/login");
  }

  // Check if user has permission to access event discounts
  const userRole = session.user.role as Role;
  if (userRole !== Role.OWNER && userRole !== Role.ADMIN) {
    redirect("/dashboard");
  }

  return (
    <OnboardingGuard requireOnboarding={true}>
      <DatabaseErrorWrapper
        hasError={false}
        errorMessage=""
        title="Gagal Memuat Data Diskon Event"
        description="Terjadi masalah saat mengambil data diskon event dari database. <PERSON><PERSON>an refresh halaman untuk mencoba lagi."
      >
        <EventDiscountManagement />
      </DatabaseErrorWrapper>
    </OnboardingGuard>
  );
}
