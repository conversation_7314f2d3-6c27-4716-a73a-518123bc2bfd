import { redirect } from "next/navigation";
import { checkOnboardingStatus } from "@/actions/onboarding/onboarding";
import { auth } from "@/lib/auth";

// Redirect to the appropriate page based on onboarding status
// This page should only handle initial dashboard access, not interfere with direct navigation
export default async function DashboardPage() {
  const session = await auth();

  // Only redirect if user is accessing /dashboard directly (not a subpage)
  // This prevents interference with direct navigation to /dashboard/sales, etc.

  // If user is an employee, always redirect to summaries (skip onboarding)
  if (session?.user?.isEmployee) {
    redirect("/dashboard/summaries");
  }

  const { hasCompleted } = await checkOnboardingStatus();

  if (!hasCompleted) {
    redirect("/dashboard/welcome");
  } else {
    redirect("/dashboard/summaries");
  }
}
