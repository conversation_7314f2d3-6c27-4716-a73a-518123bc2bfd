import React from "react";
import { DatabaseErrorWrapper } from "@/components/ui/database-error-wrapper";
import { OnboardingGuard } from "@/components/auth/onboarding-guard";
import { Metadata } from "next";
import { WarehouseTabContent } from "@/components/pages/dashboard/products/components/WarehouseTabContent";
import Link from "next/link";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import DashboardLayout from "@/components/layout/dashboardlayout";

export const metadata: Metadata = {
  title: "Gudang - KivaPOS",
  description: "Kelola gudang dan stok inventaris Anda",
};

// This is an async Server Component
const WarehousePage = async () => {
  return (
    <OnboardingGuard requireOnboarding={true}>
      <DatabaseErrorWrapper
        hasError={false}
        errorMessage=""
        title="Gagal Memuat Data Gudang"
        description="Terjadi masalah saat mengambil data gudang dari database. <PERSON>lakan refresh halaman untuk mencoba lagi."
      >
        <DashboardLayout>
          <div className="space-y-6">
            {/* Main Tabs with URL routing */}
            <Tabs defaultValue="warehouse" value="warehouse" className="w-full">
              <TabsList className="mb-4 w-full md:w-fit">
                <TabsTrigger value="products" asChild>
                  <Link href="/dashboard/products">Produk / Jasa</Link>
                </TabsTrigger>
                <TabsTrigger value="warehouse" asChild>
                  <Link href="/dashboard/products/warehouse">Gudang</Link>
                </TabsTrigger>
              </TabsList>

              <TabsContent value="warehouse" className="space-y-6">
                {/* Page Header */}
                <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                  <div>
                    <h1 className="text-2xl font-bold tracking-tight text-gray-900 dark:text-gray-100">
                      Produk & Gudang
                    </h1>
                    <p className="text-gray-600 dark:text-gray-400">
                      Kelola produk, jasa, dan inventaris gudang Anda
                    </p>
                  </div>
                </div>
                <WarehouseTabContent />
              </TabsContent>
            </Tabs>
          </div>
        </DashboardLayout>
      </DatabaseErrorWrapper>
    </OnboardingGuard>
  );
};

export default WarehousePage;
