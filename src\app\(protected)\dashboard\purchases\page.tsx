import React from "react";
import Head from "next/head";
import DashboardLayout from "@/components/layout/dashboardlayout";
import PurchasesPage from "@/components/pages/dashboard/purchases/purchases";
import { DatabaseErrorWrapper } from "@/components/ui/database-error-wrapper";
import { getPurchases } from "@/actions/entities/purchases";
import { ProtectedRoute } from "@/components/auth/protected-route";
import { Role } from "@prisma/client";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Pembelian - KivaPOS",
  description: "Kelola pembelian dan inventaris Anda",
};

// This is now an async Server Component
const Purchases = async () => {
  // Fetch purchases for the current user
  const { purchases, error } = await getPurchases();

  return (
    <ProtectedRoute allowedRoles={[Role.OWNER, Role.ADMIN]}>
      <DashboardLayout>
        <div className="py-6 px-1.5 sm:px-2 md:px-4">
          <Head>
            <title>Pembelian - KivaPOS</title>
          </Head>

          <DatabaseErrorWrapper
            hasError={!!error}
            errorMessage={error}
            title="Gagal Memuat Data Pembelian"
            description="Terjadi masalah saat mengambil data pembelian dari database. Silakan refresh halaman untuk mencoba lagi."
          >
            {/* Pass the fetched purchases to the client component */}
            <PurchasesPage purchases={purchases || []} />
          </DatabaseErrorWrapper>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
};

export default Purchases;
