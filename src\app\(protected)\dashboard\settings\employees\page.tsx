import React from "react";
import DashboardLayout from "@/components/layout/dashboardlayout";
import SettingsLayout from "@/components/pages/dashboard/settings/settings-layout";
import { Role } from "@prisma/client";
import { ProtectedRoute } from "@/components/auth/protected-route";
import EmployeeManagementSettings from "@/components/pages/dashboard/settings/employees/employee-management-settings";

const EmployeesSettingsPage = async () => {
  return (
    <ProtectedRoute allowedRoles={[Role.OWNER]}>
      <DashboardLayout>
        <SettingsLayout>
          <EmployeeManagementSettings />
        </SettingsLayout>
      </DashboardLayout>
    </ProtectedRoute>
  );
};

export default EmployeesSettingsPage;
