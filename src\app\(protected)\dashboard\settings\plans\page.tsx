import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import React from "react";
import DashboardLayout from "@/components/layout/dashboardlayout";
import SettingsLayout from "@/components/pages/dashboard/settings/settings-layout";
import PlansSettings from "@/components/pages/dashboard/settings/plans/plans-settings";
import { getUserSubscription } from "@/lib/subscription";

const PlansSettingsPage = async () => {
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/login");
  }

  // Get user's subscription
  const subscription = await getUserSubscription(session.user.id);

  // Extract only the properties needed by the component
  const subscriptionData = {
    plan: subscription.plan,
    expiryDate: subscription.expiryDate
      ? subscription.expiryDate.toISOString()
      : null,
    isActive: subscription.isActive,
  };

  return (
    <DashboardLayout>
      <SettingsLayout>
        <PlansSettings initialData={subscriptionData} />
      </SettingsLayout>
    </DashboardLayout>
  );
};

export default PlansSettingsPage;