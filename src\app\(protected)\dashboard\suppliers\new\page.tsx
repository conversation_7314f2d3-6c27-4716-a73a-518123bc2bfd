import React from "react";
import DashboardLayout from "@/components/layout/dashboardlayout";
import EnhancedSupplierPage from "@/components/pages/dashboard/suppliers/new";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Tambah Supplier | KivaPOS",
  description: "Tambahkan supplier baru ke sistem Anda",
};

const NewSupplier = async () => {
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/login");
  }

  return (
    <DashboardLayout>
      <EnhancedSupplierPage />
    </DashboardLayout>
  );
};

export default NewSupplier;
