import React from "react";
import { notFound } from "next/navigation";
import { db } from "@/lib/prisma";
import { renderHorizontalTemplate1 } from "@/components/pages/dashboard/sales/templates/HorizontalTemplate1";
import type { Sale } from "@/components/pages/dashboard/sales/types";

type PageProps = {
  params: Promise<{ id: string }>;
};

// This is an async Server Component for public sales invoice
export default async function PublicSalesInvoice(props: PageProps) {
  // Get the id from params
  const params = await props.params;
  const id = params.id as string;

  // First, check if the sale exists (regardless of public status)
  const saleExists = await db.sale.findFirst({
    where: {
      OR: [{ id: id }, { transactionNumber: id }],
    },
    select: {
      id: true,
      isPublic: true,
      transactionNumber: true,
    },
  });

  // If sale doesn't exist at all, return 404
  if (!saleExists) {
    notFound();
  }

  // If sale exists but is not public, show access denied message
  if (!saleExists.isPublic) {
    return (
      <div
        style={{
          fontFamily: "Arial, sans-serif",
          margin: 0,
          padding: "40px 20px",
          backgroundColor: "#f8f9fa",
          minHeight: "100vh",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <div
          style={{
            backgroundColor: "white",
            padding: "40px",
            borderRadius: "8px",
            boxShadow: "0 2px 10px rgba(0,0,0,0.1)",
            textAlign: "center",
            maxWidth: "500px",
            width: "100%",
          }}
        >
          <div
            style={{
              fontSize: "48px",
              marginBottom: "20px",
            }}
          >
            🔒
          </div>
          <h1
            style={{
              color: "#dc3545",
              fontSize: "24px",
              marginBottom: "16px",
              fontWeight: "bold",
            }}
          >
            Akses Terbatas
          </h1>
          <p
            style={{
              color: "#6c757d",
              fontSize: "14px",
              lineHeight: "1.5",
              margin: "0",
              fontWeight: "bold",
            }}
          >
            Transaksi belum diijinkan di share oleh pemilik, harap hubungi
            pemilik akun.
          </p>
        </div>
      </div>
    );
  }

  // Fetch the complete sale data with all includes
  const sale = await db.sale.findFirst({
    where: {
      AND: [
        {
          OR: [{ id: id }, { transactionNumber: id }],
        },
        { isPublic: true },
      ],
    },
    include: {
      items: {
        include: {
          product: true,
        },
      },
      customer: true,
      user: {
        include: {
          businessInfo: true, // Include company info
        },
      },
      warehouse: true,
      Employee: true,
      EventDiscount: true,
    },
  });

  // This should not happen since we already checked, but just in case
  if (!sale) {
    notFound();
  }

  // Transform the sale data to include companyInfo
  const transformedSale = {
    ...sale,
    totalAmount: Number(sale.totalAmount), // Convert Decimal to number
    companyInfo: sale.user?.businessInfo
      ? {
          companyName: sale.user.businessInfo.companyName,
          companyAddress: sale.user.businessInfo.companyAddress,
          companyPhone: sale.user.businessInfo.companyPhone,
          companyEmail: sale.user.businessInfo.companyEmail,
        }
      : null,
    items: sale.items.map((item) => ({
      ...item,
      priceAtSale: Number(item.priceAtSale), // Convert Decimal to number
      discountPercentage: item.discountPercentage
        ? Number(item.discountPercentage)
        : undefined,
      discountAmount: item.discountAmount
        ? Number(item.discountAmount)
        : undefined,
    })),
  } as unknown as Sale;

  // Generate the HTML content using the horizontal template
  const htmlContent = renderHorizontalTemplate1(transformedSale);

  // Return the HTML content directly
  return (
    <div
      dangerouslySetInnerHTML={{ __html: htmlContent }}
      style={{
        fontFamily: "Arial, sans-serif",
        margin: 0,
        padding: 0,
        backgroundColor: "white",
      }}
    />
  );
}

// Generate metadata for the page
export async function generateMetadata(props: {
  params: Promise<{ id: string }>;
}) {
  const params = await props.params;
  const id = params.id;

  const sale = await db.sale.findFirst({
    where: {
      AND: [
        {
          OR: [{ id: id }, { transactionNumber: id }],
        },
        { isPublic: true },
      ],
    },
    select: {
      transactionNumber: true,
      id: true,
    },
  });

  if (!sale) {
    return {
      title: "Faktur Tidak Ditemukan",
      description:
        "Faktur penjualan tidak ditemukan atau tidak tersedia untuk umum",
    };
  }

  return {
    title: `Faktur Penjualan - ${sale.transactionNumber || sale.id.substring(0, 8)}`,
    description: `Faktur penjualan ${sale.transactionNumber || sale.id.substring(0, 8)}`,
  };
}
