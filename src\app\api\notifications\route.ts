import { NextRequest, NextResponse } from "next/server";
import { withSubscriptionLimit } from "@/lib/api-subscription-middleware";
import { getFilteredNotifications } from "@/actions/notifications/notifications";

// GET /api/notifications - Get user's notifications (with subscription check)
async function getNotificationsHandler(request: NextRequest) {
  try {
    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const type = searchParams.get("type") || undefined;
    const isRead = searchParams.get("isRead");

    // Convert isRead to boolean if provided
    const isReadFilter =
      isRead === "true" ? true : isRead === "false" ? false : undefined;

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Build filters object
    const filters: any = {};
    if (type && type !== "all") {
      filters.type = type;
    }
    if (isReadFilter !== undefined) {
      filters.readStatus = isReadFilter ? "read" : "unread";
    }

    const result = await getFilteredNotifications({
      limit,
      offset,
      filters: Object.keys(filters).length > 0 ? filters : undefined,
    });

    if (result.error) {
      return NextResponse.json({ error: result.error }, { status: 400 });
    }

    // Calculate pagination info
    const totalCount = result.totalCount || 0;
    const totalPages = Math.ceil(totalCount / limit);

    return NextResponse.json({
      success: true,
      notifications: result.data,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    });
  } catch (error) {
    console.error("Error getting notifications:", error);
    return NextResponse.json(
      { error: "Failed to get notifications" },
      { status: 500 }
    );
  }
}

// Export the wrapped handler with subscription limit check
export const GET = withSubscriptionLimit("notification")(
  getNotificationsHandler
);
