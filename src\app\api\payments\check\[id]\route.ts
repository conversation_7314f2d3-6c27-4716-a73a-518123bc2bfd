import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
// COMMENTED OUT - XENDIT IMPLEMENTATION (KEPT FOR REFERENCE)
// import { getInvoiceStatus } from "@/lib/xendit";
import { getTransactionStatus, mapMidtransStatus } from "@/lib/midtrans";
import { db } from "@/lib/prisma";
import { PaymentStatus } from "@prisma/client";

// POST /api/payments/check/:id - Check payment status
export async function POST(
  _request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  console.log("🔍 [PAYMENT CHECK] Starting payment status check");

  try {
    const session = await auth();

    if (!session?.user?.id) {
      console.log("❌ [PAYMENT CHECK] Unauthorized - no session");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const resolvedParams = await context.params;
    const paymentId = resolvedParams.id;

    console.log("🔍 [PAYMENT CHECK] Checking payment:", {
      paymentId,
      userId: session.user.id,
    });

    // Get payment from database
    const payment = await db.payment.findUnique({
      where: { id: paymentId },
    });

    if (!payment) {
      console.log("❌ [PAYMENT CHECK] Payment not found:", { paymentId });
      return NextResponse.json({ error: "Payment not found" }, { status: 404 });
    }

    // Check if payment belongs to user
    if (payment.userId !== session.user.id) {
      console.log(
        "❌ [PAYMENT CHECK] Unauthorized - payment belongs to different user:",
        {
          paymentId,
          paymentUserId: payment.userId,
          sessionUserId: session.user.id,
        }
      );
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    console.log("✅ [PAYMENT CHECK] Payment found:", {
      paymentId,
      currentStatus: payment.status,
      externalId: payment.externalId,
      invoiceId: payment.invoiceId,
    });

    // If payment is already completed, return success
    if (payment.status === PaymentStatus.COMPLETED) {
      console.log("✅ [PAYMENT CHECK] Payment already completed");
      return NextResponse.json({ status: PaymentStatus.COMPLETED });
    }

    console.log("🔍 [PAYMENT CHECK] Checking payment for Midtrans order ID:", {
      invoiceId: payment.invoiceId,
      externalId: payment.externalId,
    });

    // If payment has Midtrans order ID, check status
    if (payment.invoiceId || payment.externalId) {
      const orderId = payment.invoiceId || payment.externalId;

      console.log(
        "🔍 [PAYMENT CHECK] Attempting to get Midtrans transaction status for orderId:",
        {
          orderId,
          paymentId,
        }
      );

      const transactionResult = await getTransactionStatus(orderId!);

      let newStatus: PaymentStatus | undefined; // Declare newStatus outside the if block

      if (transactionResult.success && transactionResult.data) {
        const midtransStatus = transactionResult.data.transaction_status;
        const fraudStatus = transactionResult.data.fraud_status;

        console.log("📊 [PAYMENT CHECK] Midtrans status retrieved:", {
          orderId,
          transaction_status: midtransStatus,
          fraud_status: fraudStatus,
          payment_type: transactionResult.data.payment_type,
        });

        // Map Midtrans status to our PaymentStatus
        newStatus = mapMidtransStatus(
          midtransStatus,
          fraudStatus
        ) as PaymentStatus;

        console.log("🔄 [PAYMENT CHECK] Status mapping:", {
          orderId,
          midtransStatus,
          fraudStatus,
          mappedStatus: newStatus,
          currentStatus: payment.status,
        });

        // Update payment status if changed
        if (newStatus !== payment.status) {
          console.log("🔄 [PAYMENT CHECK] Updating payment status:", {
            paymentId,
            from: payment.status,
            to: newStatus,
          });

          await db.payment.update({
            where: { id: payment.id },
            data: {
              status: newStatus as PaymentStatus,
              paymentDate:
                newStatus === PaymentStatus.COMPLETED ? new Date() : null,
              metadata: {
                ...((payment.metadata as any) || {}),
                lastStatusCheck: new Date().toISOString(),
                midtransTransactionData: transactionResult.data,
              },
            },
          });

          // If payment is completed, update user's subscription
          if (newStatus === PaymentStatus.COMPLETED && payment.subscriptionId) {
            console.log(
              "🔄 [PAYMENT CHECK] Updating subscription for completed payment:",
              {
                paymentId,
                subscriptionId: payment.subscriptionId,
              }
            );

            const subscription = await db.subscription.findUnique({
              where: { id: payment.subscriptionId },
            });

            if (subscription) {
              await db.user.update({
                where: { id: payment.userId },
                data: {
                  currentPlan: subscription.plan,
                  subscriptionExpiry: subscription.endDate,
                },
              });

              // Update subscription status to active
              await db.subscription.update({
                where: { id: subscription.id },
                data: { status: "active" },
              });
            }
          }
        }

        console.log(
          "✅ [PAYMENT CHECK] Payment status updated successfully to:",
          {
            paymentId,
            newStatus,
          }
        );
        return NextResponse.json({ status: newStatus });
      } else {
        console.log("ℹ️ [PAYMENT CHECK] Payment status remains unchanged:", {
          paymentId,
          currentStatus: payment.status,
          newStatus,
        });
        return NextResponse.json({ status: payment.status });
      }
    } else {
      console.log(
        "⚠️ [PAYMENT CHECK] No Midtrans order ID found or transaction check failed for payment:",
        {
          paymentId,
          invoiceId: payment.invoiceId,
          externalId: payment.externalId,
        }
      );
    }

    // If no invoice ID or failed to check, return current status
    return NextResponse.json({ status: payment.status });
  } catch (error) {
    console.error("Error checking payment:", error);
    return NextResponse.json(
      { error: "Failed to check payment" },
      { status: 500 }
    );
  }
}
