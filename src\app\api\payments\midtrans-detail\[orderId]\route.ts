import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { getTransactionStatus } from "@/lib/midtrans";
import { db } from "@/lib/prisma";

// GET /api/payments/midtrans-detail/[orderId] - Get Midtrans transaction details
export async function GET(
  _request: NextRequest,
  context: { params: Promise<{ orderId: string }> }
) {
  try {
    console.log("🔍 [MIDTRANS DETAIL API] Fetching transaction details");
    const session = await auth();

    if (!session?.user?.id) {
      console.log("❌ [MIDTRANS DETAIL API] Unauthorized - no session");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { orderId } = await context.params;

    if (!orderId) {
      console.log("❌ [MIDTRANS DETAIL API] Missing order ID");
      return NextResponse.json(
        { error: "Order ID is required" },
        { status: 400 }
      );
    }

    console.log("🔍 [MIDTRANS DETAIL API] Looking for payment:", {
      orderId,
      userId: session.user.id,
    });

    // Verify that the payment belongs to the current user
    const payment = await db.payment.findFirst({
      where: {
        invoiceId: orderId,
        userId: session.user.id, // Ensure user owns this payment
      },
    });

    if (!payment) {
      console.log(
        "❌ [MIDTRANS DETAIL API] Payment not found or unauthorized:",
        {
          orderId,
          userId: session.user.id,
        }
      );
      return NextResponse.json(
        { error: "Payment not found or unauthorized" },
        { status: 404 }
      );
    }

    console.log("✅ [MIDTRANS DETAIL API] Payment found:", {
      paymentId: payment.id,
      orderId,
      status: payment.status,
    });

    // Get transaction details from Midtrans
    console.log("🚀 [MIDTRANS DETAIL API] Fetching from Midtrans:", {
      orderId,
    });
    const midtransResult = await getTransactionStatus(orderId);

    if (!midtransResult.success) {
      console.error("❌ [MIDTRANS DETAIL API] Failed to fetch from Midtrans:", {
        orderId,
        error: midtransResult.error,
      });
      return NextResponse.json(
        {
          success: false,
          error:
            midtransResult.error ||
            "Failed to fetch transaction details from Midtrans",
        },
        { status: 500 }
      );
    }

    console.log("✅ [MIDTRANS DETAIL API] Transaction details retrieved:", {
      orderId,
      transactionStatus: midtransResult.data.transaction_status,
    });

    return NextResponse.json({
      success: true,
      data: midtransResult.data,
    });
  } catch (error) {
    console.error("❌ [MIDTRANS DETAIL API] Error:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch transaction details",
      },
      { status: 500 }
    );
  }
}
