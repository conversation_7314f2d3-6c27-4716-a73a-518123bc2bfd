import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/prisma";
import { auth } from "@/lib/auth";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ productId: string }> }
) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: "Unauthorized", purchases: [] },
        { status: 401 }
      );
    }

    const { productId } = await params;

    // Get pagination parameters from query string
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "10");
    const skip = (page - 1) * limit;

    // TODO: Replace this with your actual database query
    // Example using Prisma:
    /*
    const purchases = await db.purchase.findMany({
      where: {
        items: {
          some: {
            productId: productId
          }
        }
      },
      include: {
        supplier: true,
        items: {
          where: {
            productId: productId
          }
        }
      },
      orderBy: {
        date: 'desc'
      }
    });

    // Transform the data to match the expected format
    const transformedPurchases = purchases.map(purchase => ({
      id: purchase.id,
      date: purchase.date.toISOString().split('T')[0], // Format as YYYY-MM-DD
      supplier: purchase.supplier.name,
      quantity: purchase.items[0]?.quantity || 0,
      unitPrice: purchase.items[0]?.unitPrice || 0,
      totalAmount: purchase.items[0]?.totalAmount || 0,
      invoiceNumber: purchase.invoiceNumber
    }));
    */

    // Get total count for pagination
    const totalCount = await db.purchaseItem.count({
      where: {
        productId: productId,
        purchase: {
          userId: session.user.id,
        },
      },
    });

    // Fetch purchase items for the specific product (only for the authenticated user)
    const purchaseItems = await db.purchaseItem.findMany({
      where: {
        productId: productId,
        purchase: {
          userId: session.user.id, // Only get purchases for the authenticated user
        },
      },
      include: {
        purchase: {
          include: {
            supplier: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      skip: skip,
      take: limit,
    });

    // Transform the data to match the expected format
    const transformedPurchases = purchaseItems.map((item) => ({
      id: item.id,
      date: item.purchase.purchaseDate.toISOString().split("T")[0], // Format as YYYY-MM-DD
      supplier: item.purchase.supplier?.name || "Supplier Tidak Diketahui",
      quantity: item.quantity,
      unitPrice: Number(item.costAtPurchase), // Convert Decimal to number
      totalAmount: Number(item.costAtPurchase) * item.quantity, // Calculate total
      invoiceNumber:
        item.purchase.invoiceRef ||
        item.purchase.transactionNumber ||
        item.purchase.id,
    }));

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return NextResponse.json({
      success: true,
      purchases: transformedPurchases,
      pagination: {
        currentPage: page,
        totalPages: totalPages,
        totalCount: totalCount,
        limit: limit,
        hasNextPage: hasNextPage,
        hasPrevPage: hasPrevPage,
      },
    });
  } catch (error) {
    console.error("Error fetching purchase history:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch purchase history",
        purchases: [],
      },
      { status: 500 }
    );
  }
}

/*
Example database schema for reference:

Table: purchases
- id (string/uuid)
- date (datetime)
- supplierId (string)
- invoiceNumber (string)
- totalAmount (decimal)
- createdAt (datetime)
- updatedAt (datetime)

Table: purchase_items
- id (string/uuid)
- purchaseId (string)
- productId (string)
- quantity (integer)
- unitPrice (decimal)
- totalAmount (decimal)

Table: suppliers
- id (string/uuid)
- name (string)
- email (string)
- phone (string)
- address (text)

To implement with your database:
1. Replace the mock data with actual database queries
2. Adjust the field names to match your schema
3. Add proper error handling and validation
4. Consider adding pagination for large datasets
5. Add authentication/authorization if needed
*/
