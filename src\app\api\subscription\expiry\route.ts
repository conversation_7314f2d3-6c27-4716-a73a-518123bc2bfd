import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { isSubscriptionExpired } from "@/lib/subscription-limits";

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const expired = await isSubscriptionExpired(session.user.id);

    return NextResponse.json({ expired });
  } catch (error) {
    console.error("Error checking subscription expiry:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
