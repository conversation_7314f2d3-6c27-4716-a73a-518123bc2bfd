import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { getUsageSummaryForAPI } from "@/lib/api-subscription-middleware";

// GET /api/subscription/usage - Get user's subscription usage summary
export async function GET(_request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const usageSummary = await getUsageSummaryForAPI(session.user.id);
    
    if (!usageSummary) {
      return NextResponse.json(
        { error: "Failed to get usage summary" },
        { status: 500 }
      );
    }
    
    return NextResponse.json({
      success: true,
      usage: usageSummary,
    });
  } catch (error) {
    console.error("Error getting usage summary:", error);
    return NextResponse.json(
      { error: "Failed to get usage summary" },
      { status: 500 }
    );
  }
}
