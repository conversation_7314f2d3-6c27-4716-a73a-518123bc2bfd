"use client";

import React, { useEffect } from "react";
import { HeroSection } from "./sections/hero-section";
import { FeaturesSection } from "./sections/features-section";
import { SocialProofSection } from "./sections/social-proof-section";
import { HowItWorksSection } from "./sections/how-it-works-section";
import { PricingSection } from "./sections/pricing-section";
import { CTASection } from "./sections/cta-section";
import { FooterSection } from "./sections/footer-section";
import { LandingNavbar } from "./components/landing-navbar";
import { ScrollToTopButton } from "./components/scroll-to-top-button";

export const ModernLandingPage: React.FC = () => {
  useEffect(() => {
    // Add smooth scrolling to the entire page
    document.documentElement.style.scrollBehavior = "smooth";

    // Cleanup
    return () => {
      document.documentElement.style.scrollBehavior = "auto";
    };
  }, []);

  return (
    <div className="min-h-screen bg-white">
      <LandingNavbar />
      <main>
        <HeroSection />
        <FeaturesSection />
        {/* <SocialProofSection /> */}
        <HowItWorksSection />
        <PricingSection />
        <CTASection />
      </main>
      <FooterSection />
      <ScrollToTopButton />
    </div>
  );
};
