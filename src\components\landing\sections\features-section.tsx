"use client";

import React from "react";
import { motion } from "framer-motion";
import {
  BarChart3,
  CreditCard,
  Package,
  FileText,
  Cloud,
  Shield,
  Smartphone,
  Users,
  TrendingUp,
  Zap,
  XCircle, // Added for problem points
} from "lucide-react";

// Interface for feature props remains the same
interface Feature {
  icon: React.ReactNode;
  title: string;
  description: string;
  highlight?: boolean;
}

export const FeaturesSection: React.FC = () => {
  // Problems faced by businesses, now more visually distinct
  const problemPoints = [
    "Kesulitan melacak pembelian dan penjualan secara akurat",
    "Pencatatan keuangan manual yang memakan waktu dan rentan error",
    "Laporan keuangan yang tidak real-time dan sulit dianalisis",
    "Manajemen produk dan stok yang tidak efisien dan terintegrasi",
  ];

  // Core features of the application
  const mainFeatures: Feature[] = [
    {
      icon: <Package className="w-8 h-8" />,
      title: "Manajemen Pembelian",
      description:
        "Kelola pembelian dari supplier, catat purchase order, dan pantau status pengadaan barang dengan sistem yang terorganisir dan mudah digunakan.",
      highlight: true,
    },
    {
      icon: <CreditCard className="w-8 h-8" />,
      title: "Manajemen Penjualan",
      description:
        "Catat transaksi penjualan, kelola data pelanggan, dan pantau performa penjualan dengan sistem pencatatan yang akurat dan efisien.",
      highlight: true,
    },
    {
      icon: <BarChart3 className="w-8 h-8" />,
      title: "Produk & Layanan",
      description:
        "Kelola katalog produk dan layanan, atur kategori, harga, dan informasi detail untuk memudahkan proses penjualan dan pembelian.",
    },
    {
      icon: <FileText className="w-8 h-8" />,
      title: "Laporan Keuangan Terperinci",
      description:
        "Generate laporan keuangan lengkap termasuk laba rugi, neraca, dan arus kas dengan format yang mudah dipahami dan dapat diekspor.",
    },
  ];

  // Additional supporting features
  const additionalFeatures: Feature[] = [
    {
      icon: <Cloud className="w-6 h-6" />,
      title: "Berbasis Cloud",
      description:
        "Akses data dari mana saja dengan backup otomatis dan keamanan terjamin.",
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: "Keamanan Data Terenkripsi",
      description:
        "Data keuangan Anda dilindungi dengan standar enkripsi modern.",
    },
    {
      icon: <Smartphone className="w-6 h-6" />,
      title: "Desain Mobile-First",
      description: "Interface yang optimal di desktop, tablet, dan smartphone.",
    },
    {
      icon: <Users className="w-6 h-6" />,
      title: "Manajemen Kontak Terpusat",
      description: "Kelola semua data supplier dan pelanggan di satu tempat.",
    },
    {
      icon: <TrendingUp className="w-6 h-6" />,
      title: "Analisis Bisnis Cerdas",
      description: "Dapatkan insight keuangan dan analisis performa finansial.",
    },
    {
      icon: <Zap className="w-6 h-6" />,
      title: "Performa Cepat & Responsif",
      description: "Loading cepat untuk produktivitas dan efisiensi maksimal.",
    },
  ];

  // Animation variants for Framer Motion
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 10,
      },
    },
  };

  return (
    <section id="features" className="py-24 sm:py-32 bg-slate-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Problem Statement Section */}
        <motion.div
          className="text-center mb-20"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          variants={containerVariants}
        >
          <motion.h2
            className="text-3xl md:text-4xl font-bold text-slate-900 mb-6"
            variants={itemVariants}
          >
            Operasional Bisnis Anda Terasa Rumit?
          </motion.h2>
          <motion.p
            className="text-lg text-slate-600 mb-10 max-w-3xl mx-auto"
            variants={itemVariants}
          >
            Banyak bisnis hebat terhambat oleh proses manual dan sistem yang
            terfragmentasi. Ini adalah beberapa tantangan yang kami selesaikan.
          </motion.p>

          <motion.div
            className="grid md:grid-cols-2 gap-4 max-w-4xl mx-auto"
            variants={containerVariants}
          >
            {problemPoints.map((problem, index) => (
              <motion.div
                key={index}
                className="flex items-center text-left bg-red-50 p-4 rounded-lg border border-red-200"
                variants={itemVariants}
              >
                <XCircle className="w-5 h-5 text-red-500 mr-3 flex-shrink-0" />
                <span className="text-slate-700">{problem}</span>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>

        {/* Solution Overview Section */}
        <motion.div
          className="text-center mb-20"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          variants={itemVariants}
        >
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-indigo-100 text-indigo-800 text-sm font-semibold mb-6">
            Solusi dari KivaPOS
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-6">
            Platform Tunggal untuk Semua Kebutuhan Bisnis
          </h2>
          <p className="text-lg text-slate-600 max-w-3xl mx-auto">
            KivaPOS menyatukan semua alat yang Anda butuhkan ke dalam satu
            platform yang intuitif, andal, dan dapat diakses dari mana saja.
          </p>
        </motion.div>

        {/* Main Features Section */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-20"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
        >
          {mainFeatures.map((feature, index) => (
            <motion.div
              key={index}
              className={`relative p-8 rounded-2xl transition-all duration-300 ${
                feature.highlight
                  ? "bg-white border border-indigo-200 shadow-lg"
                  : "bg-white border border-slate-200 shadow-md"
              }`}
              variants={itemVariants}
              whileHover={{
                transform: "translateY(-5px)",
                boxShadow: "0px 15px 25px -5px rgba(0, 0, 0, 0.1)",
              }}
            >
              {feature.highlight && (
                <div className="absolute -top-3 left-6 bg-indigo-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                  Fitur Utama
                </div>
              )}

              <div
                className={`inline-flex p-3 rounded-xl mb-5 ${
                  feature.highlight
                    ? "bg-indigo-100 text-indigo-600"
                    : "bg-slate-100 text-slate-600"
                }`}
              >
                {feature.icon}
              </div>

              <h3 className="text-xl font-semibold text-slate-800 mb-3">
                {feature.title}
              </h3>
              <p className="text-slate-600 leading-relaxed">
                {feature.description}
              </p>
            </motion.div>
          ))}
        </motion.div>

        {/* Additional Features Section */}
        <motion.div
          className="bg-white rounded-2xl p-8 sm:p-12 shadow-xl border border-slate-200"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
          variants={itemVariants}
        >
          <h3 className="text-2xl font-bold text-slate-900 text-center mb-10">
            Dan Semua yang Anda Butuhkan untuk Berkembang
          </h3>

          <motion.div
            className="grid md:grid-cols-2 lg:grid-cols-3 gap-x-8 gap-y-10"
            variants={containerVariants}
          >
            {additionalFeatures.map((feature, index) => (
              <motion.div
                key={index}
                className="flex items-start space-x-4"
                variants={itemVariants}
              >
                <div className="flex-shrink-0 p-3 bg-indigo-100 text-indigo-600 rounded-lg">
                  {feature.icon}
                </div>
                <div>
                  <h4 className="font-semibold text-slate-800 mb-1">
                    {feature.title}
                  </h4>
                  <p className="text-sm text-slate-600">
                    {feature.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};
