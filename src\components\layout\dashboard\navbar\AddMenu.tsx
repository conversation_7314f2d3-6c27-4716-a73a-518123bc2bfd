"use client";

import React from "react";
import Link from "next/link";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import {
  CubeIcon,
  ShoppingBagIcon,
  CurrencyDollarIcon,
} from "@heroicons/react/24/outline";
import { Plus, Lock } from "lucide-react";
import { useSubscriptionLimits } from "@/hooks/useSubscriptionLimits";
import { toast } from "sonner";

const AddMenu = () => {
  const { products, transactions, isLoading } = useSubscriptionLimits();

  const handleDisabledClick = (type: string, message?: string) => {
    toast.error(message || `Batas ${type} tercapai untuk paket Anda.`);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="relative h-8 w-8 md:h-11 md:w-11 rounded-md cursor-pointer"
          aria-label="Tambah Baru"
        >
          <Plus className="!h-4 !w-4 md:!h-6 md:!w-6" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        {/* Add Product */}
        {products.allowed ? (
          <Link href="/dashboard/products/new" passHref>
            <DropdownMenuItem className="cursor-pointer">
              <CubeIcon className="mr-2 h-4 w-4" />
              <span>Tambah Produk</span>
            </DropdownMenuItem>
          </Link>
        ) : (
          <DropdownMenuItem
            className="cursor-pointer opacity-50"
            onClick={() => handleDisabledClick("produk", products.message)}
          >
            <Lock className="mr-2 h-4 w-4" />
            <span>Tambah Produk</span>
          </DropdownMenuItem>
        )}

        {/* Add Purchase */}
        {transactions.allowed ? (
          <Link href="/dashboard/purchases/new" passHref>
            <DropdownMenuItem className="cursor-pointer">
              <ShoppingBagIcon className="mr-2 h-4 w-4" />
              <span>Tambah Pembelian</span>
            </DropdownMenuItem>
          </Link>
        ) : (
          <DropdownMenuItem
            className="cursor-pointer opacity-50"
            onClick={() =>
              handleDisabledClick("transaksi", transactions.message)
            }
          >
            <Lock className="mr-2 h-4 w-4" />
            <span>Tambah Pembelian</span>
          </DropdownMenuItem>
        )}

        {/* Add Sale */}
        {transactions.allowed ? (
          <Link href="/dashboard/sales/new" passHref>
            <DropdownMenuItem className="cursor-pointer">
              <CurrencyDollarIcon className="mr-2 h-4 w-4" />
              <span>Tambah Penjualan</span>
            </DropdownMenuItem>
          </Link>
        ) : (
          <DropdownMenuItem
            className="cursor-pointer opacity-50"
            onClick={() =>
              handleDisabledClick("transaksi", transactions.message)
            }
          >
            <Lock className="mr-2 h-4 w-4" />
            <span>Tambah Penjualan</span>
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default AddMenu;
