"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Plus,
  Edit,
  Trash2,
  Eye,
  LogOut,
  FileText,
  Users,
  TrendingUp,
  Calendar,
  Save,
  X,
} from "lucide-react";
import { useToast } from "@/components/hooks/use-toast";

interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  author: string;
  publishedAt: string;
  category: string;
  tags: string[];
  status: "draft" | "published";
  featured: boolean;
}

export const BlogAdminDashboard: React.FC = () => {
  const router = useRouter();
  const { toast } = useToast();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingPost, setEditingPost] = useState<BlogPost | null>(null);
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [newPost, setNewPost] = useState<Partial<BlogPost>>({
    title: "",
    excerpt: "",
    content: "",
    category: "",
    tags: [],
    status: "draft",
    featured: false,
  });

  // Check authentication on mount
  useEffect(() => {
    const session = localStorage.getItem("blog_admin_session");
    if (!session) {
      router.push("/blog/admin-login");
      return;
    }

    try {
      const sessionData = JSON.parse(session);
      if (sessionData.email === "<EMAIL>") {
        setIsAuthenticated(true);
        loadPosts();
      } else {
        router.push("/blog/admin-login");
      }
    } catch (error) {
      router.push("/blog/admin-login");
    }
  }, [router]);

  const loadPosts = () => {
    // Load posts from localStorage or initialize with dummy data
    const savedPosts = localStorage.getItem("blog_posts");
    if (savedPosts) {
      setPosts(JSON.parse(savedPosts));
    } else {
      const dummyPosts: BlogPost[] = [
        {
          id: "1",
          title: "10 Strategi Meningkatkan Penjualan Retail di Era Digital",
          excerpt:
            "Pelajari strategi terbukti untuk meningkatkan penjualan toko retail Anda dengan memanfaatkan teknologi digital dan sistem POS modern.",
          content: "Content here...",
          author: "Tim KivaPOS",
          publishedAt: "2024-01-15",
          category: "tips",
          tags: ["retail", "penjualan", "digital", "strategi"],
          status: "published",
          featured: true,
        },
        {
          id: "2",
          title: "Cara Memilih Sistem POS yang Tepat untuk Bisnis Anda",
          excerpt:
            "Panduan lengkap memilih sistem Point of Sale yang sesuai dengan kebutuhan dan budget bisnis Anda.",
          content: "Content here...",
          author: "Ahmad Rizki",
          publishedAt: "2024-01-12",
          category: "guides",
          tags: ["pos", "bisnis", "teknologi", "panduan"],
          status: "published",
          featured: false,
        },
      ];
      setPosts(dummyPosts);
      localStorage.setItem("blog_posts", JSON.stringify(dummyPosts));
    }
  };

  const handleLogout = () => {
    localStorage.removeItem("blog_admin_session");
    toast({
      title: "Logout Berhasil",
      description: "Anda telah keluar dari dashboard admin.",
    });
    router.push("/blog");
  };

  const handleCreatePost = () => {
    if (!newPost.title || !newPost.excerpt || !newPost.content) {
      toast({
        title: "Data Tidak Lengkap",
        description: "Mohon isi semua field yang diperlukan.",
        variant: "destructive",
      });
      return;
    }

    const post: BlogPost = {
      id: Date.now().toString(),
      title: newPost.title!,
      excerpt: newPost.excerpt!,
      content: newPost.content!,
      author: "Admin",
      publishedAt: new Date().toISOString().split("T")[0],
      category: newPost.category || "tips",
      tags: newPost.tags || [],
      status: (newPost.status as "draft" | "published") || "draft",
      featured: newPost.featured || false,
    };

    const updatedPosts = [...posts, post];
    setPosts(updatedPosts);
    localStorage.setItem("blog_posts", JSON.stringify(updatedPosts));

    setNewPost({
      title: "",
      excerpt: "",
      content: "",
      category: "",
      tags: [],
      status: "draft",
      featured: false,
    });
    setIsCreateDialogOpen(false);

    toast({
      title: "Post Dibuat",
      description: "Post blog berhasil dibuat.",
    });
  };

  const handleDeletePost = (postId: string) => {
    const updatedPosts = posts.filter((post) => post.id !== postId);
    setPosts(updatedPosts);
    localStorage.setItem("blog_posts", JSON.stringify(updatedPosts));

    toast({
      title: "Post Dihapus",
      description: "Post blog berhasil dihapus.",
    });
  };

  const handleToggleStatus = (postId: string) => {
    const updatedPosts = posts.map((post) =>
      post.id === postId
        ? {
            ...post,
            status:
              post.status === "published"
                ? "draft"
                : ("published" as "draft" | "published"),
          }
        : post
    );
    setPosts(updatedPosts);
    localStorage.setItem("blog_posts", JSON.stringify(updatedPosts));

    toast({
      title: "Status Diubah",
      description: "Status post berhasil diubah.",
    });
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">
            Memverifikasi akses...
          </p>
        </div>
      </div>
    );
  }

  const stats = {
    totalPosts: posts.length,
    publishedPosts: posts.filter((p) => p.status === "published").length,
    draftPosts: posts.filter((p) => p.status === "draft").length,
    featuredPosts: posts.filter((p) => p.featured).length,
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                Blog Admin Dashboard
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                Kelola konten blog KivaPOS
              </p>
            </div>
            <div className="flex items-center gap-4">
              <Button variant="outline" asChild>
                <a href="/blog" target="_blank">
                  <Eye className="h-4 w-4 mr-2" />
                  Lihat Blog
                </a>
              </Button>
              <Button variant="outline" onClick={handleLogout}>
                <LogOut className="h-4 w-4 mr-2" />
                Logout
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Total Posts
                  </p>
                  <p className="text-2xl font-bold">{stats.totalPosts}</p>
                </div>
                <FileText className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Published
                  </p>
                  <p className="text-2xl font-bold">{stats.publishedPosts}</p>
                </div>
                <TrendingUp className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Drafts
                  </p>
                  <p className="text-2xl font-bold">{stats.draftPosts}</p>
                </div>
                <Calendar className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Featured
                  </p>
                  <p className="text-2xl font-bold">{stats.featuredPosts}</p>
                </div>
                <Users className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Posts Management */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>Kelola Posts</CardTitle>
              <Dialog
                open={isCreateDialogOpen}
                onOpenChange={setIsCreateDialogOpen}
              >
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Buat Post Baru
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle>Buat Post Baru</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="title">Judul</Label>
                      <Input
                        id="title"
                        value={newPost.title || ""}
                        onChange={(e) =>
                          setNewPost((prev) => ({
                            ...prev,
                            title: e.target.value,
                          }))
                        }
                        placeholder="Masukkan judul post"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="excerpt">Excerpt</Label>
                      <Textarea
                        id="excerpt"
                        value={newPost.excerpt || ""}
                        onChange={(e) =>
                          setNewPost((prev) => ({
                            ...prev,
                            excerpt: e.target.value,
                          }))
                        }
                        placeholder="Ringkasan singkat post"
                        rows={3}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="content">Konten</Label>
                      <Textarea
                        id="content"
                        value={newPost.content || ""}
                        onChange={(e) =>
                          setNewPost((prev) => ({
                            ...prev,
                            content: e.target.value,
                          }))
                        }
                        placeholder="Isi konten post"
                        rows={8}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="category">Kategori</Label>
                        <Select
                          onValueChange={(value) =>
                            setNewPost((prev) => ({ ...prev, category: value }))
                          }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Pilih kategori" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="tips">Tips Bisnis</SelectItem>
                            <SelectItem value="technology">
                              Teknologi
                            </SelectItem>
                            <SelectItem value="guides">Panduan</SelectItem>
                            <SelectItem value="news">Berita</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="status">Status</Label>
                        <Select
                          onValueChange={(value) =>
                            setNewPost((prev) => ({
                              ...prev,
                              status: value as "draft" | "published",
                            }))
                          }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Pilih status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="draft">Draft</SelectItem>
                            <SelectItem value="published">Published</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="flex justify-end gap-2">
                      <Button
                        variant="outline"
                        onClick={() => setIsCreateDialogOpen(false)}
                      >
                        <X className="h-4 w-4 mr-2" />
                        Batal
                      </Button>
                      <Button onClick={handleCreatePost}>
                        <Save className="h-4 w-4 mr-2" />
                        Simpan Post
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {posts.map((post) => (
                <div key={post.id} className="border rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h3 className="font-semibold text-lg mb-2">
                        {post.title}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-300 mb-3">
                        {post.excerpt}
                      </p>
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <span>By {post.author}</span>
                        <span>{post.publishedAt}</span>
                        <Badge
                          variant={
                            post.status === "published"
                              ? "default"
                              : "secondary"
                          }
                        >
                          {post.status}
                        </Badge>
                        {post.featured && (
                          <Badge variant="outline">Featured</Badge>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2 ml-4">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleToggleStatus(post.id)}
                      >
                        {post.status === "published" ? "Unpublish" : "Publish"}
                      </Button>
                      <Button size="sm" variant="outline">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDeletePost(post.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
