"use client";

import React, { useState } from "react";
import { PublicPageLayout } from "@/components/layout/public-page-layout";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Search,
  Calendar,
  User,
  Clock,
  ArrowRight,
  Tag,
  TrendingUp,
  BookOpen,
  Lightbulb,
} from "lucide-react";

interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  author: string;
  publishedAt: string;
  readTime: string;
  category: string;
  tags: string[];
  featured: boolean;
}

interface Category {
  id: string;
  name: string;
  count: number;
  icon: React.ElementType;
}

export const BlogPage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");

  const categories: Category[] = [
    { id: "tips", name: "<PERSON>ips B<PERSON>", count: 12, icon: Lightbulb },
    { id: "technology", name: "Teknologi", count: 8, icon: TrendingUp },
    { id: "guides", name: "Panduan", count: 15, icon: BookOpen },
    { id: "news", name: "Berita", count: 6, icon: Tag },
  ];

  const blogPosts: BlogPost[] = [
    {
      id: "1",
      title: "10 Strategi Meningkatkan Penjualan Retail di Era Digital",
      excerpt: "Pelajari strategi terbukti untuk meningkatkan penjualan toko retail Anda dengan memanfaatkan teknologi digital dan sistem POS modern.",
      content: "Content here...",
      author: "Tim KivaPOS",
      publishedAt: "2024-01-15",
      readTime: "5 menit",
      category: "tips",
      tags: ["retail", "penjualan", "digital", "strategi"],
      featured: true,
    },
    {
      id: "2",
      title: "Cara Memilih Sistem POS yang Tepat untuk Bisnis Anda",
      excerpt: "Panduan lengkap memilih sistem Point of Sale yang sesuai dengan kebutuhan dan budget bisnis Anda.",
      content: "Content here...",
      author: "Ahmad Rizki",
      publishedAt: "2024-01-12",
      readTime: "7 menit",
      category: "guides",
      tags: ["pos", "bisnis", "teknologi", "panduan"],
      featured: true,
    },
    {
      id: "3",
      title: "Tren Pembayaran Digital di Indonesia 2024",
      excerpt: "Analisis mendalam tentang perkembangan metode pembayaran digital dan dampaknya terhadap bisnis retail di Indonesia.",
      content: "Content here...",
      author: "Sari Dewi",
      publishedAt: "2024-01-10",
      readTime: "6 menit",
      category: "technology",
      tags: ["pembayaran", "digital", "fintech", "indonesia"],
      featured: false,
    },
    {
      id: "4",
      title: "Mengelola Inventori dengan Efisien: Tips dan Trik",
      excerpt: "Pelajari cara mengelola stok barang dengan efisien untuk mengurangi waste dan meningkatkan profitabilitas.",
      content: "Content here...",
      author: "Budi Santoso",
      publishedAt: "2024-01-08",
      readTime: "4 menit",
      category: "tips",
      tags: ["inventori", "manajemen", "efisiensi", "stok"],
      featured: false,
    },
    {
      id: "5",
      title: "KivaPOS Meluncurkan Fitur Analitik Lanjutan",
      excerpt: "Fitur baru yang memungkinkan pemilik bisnis mendapatkan insight mendalam tentang performa penjualan dan perilaku pelanggan.",
      content: "Content here...",
      author: "Tim KivaPOS",
      publishedAt: "2024-01-05",
      readTime: "3 menit",
      category: "news",
      tags: ["kivapos", "fitur", "analitik", "update"],
      featured: false,
    },
    {
      id: "6",
      title: "Membangun Loyalitas Pelanggan di Era Kompetitif",
      excerpt: "Strategi praktis untuk membangun dan mempertahankan loyalitas pelanggan dalam persaingan bisnis yang ketat.",
      content: "Content here...",
      author: "Maya Putri",
      publishedAt: "2024-01-03",
      readTime: "8 menit",
      category: "tips",
      tags: ["loyalitas", "pelanggan", "crm", "retention"],
      featured: false,
    },
  ];

  const filteredPosts = blogPosts.filter((post) => {
    const matchesSearch = post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         post.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         post.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesCategory = selectedCategory === "all" || post.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const featuredPosts = filteredPosts.filter(post => post.featured);
  const regularPosts = filteredPosts.filter(post => !post.featured);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <PublicPageLayout
      title="Blog KivaPOS"
      description="Tips, panduan, dan insight terbaru tentang bisnis retail dan teknologi POS"
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Search and Filter */}
        <div className="mb-12">
          <div className="max-w-2xl mx-auto mb-8">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <Input
                type="text"
                placeholder="Cari artikel, tips, atau panduan..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 py-3 text-lg"
              />
            </div>
          </div>

          {/* Categories */}
          <div className="flex flex-wrap gap-2 justify-center">
            <Button
              variant={selectedCategory === "all" ? "default" : "outline"}
              onClick={() => setSelectedCategory("all")}
            >
              Semua ({blogPosts.length})
            </Button>
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? "default" : "outline"}
                onClick={() => setSelectedCategory(category.id)}
                className="flex items-center gap-2"
              >
                <category.icon className="h-4 w-4" />
                {category.name} ({category.count})
              </Button>
            ))}
          </div>
        </div>

        {/* Featured Posts */}
        {featuredPosts.length > 0 && (
          <section className="mb-16">
            <h2 className="text-2xl font-bold mb-8 text-center">Artikel Pilihan</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {featuredPosts.map((post) => (
                <Card key={post.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="h-48 bg-gradient-to-r from-blue-500 to-purple-600"></div>
                  <CardHeader>
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant="secondary">
                        {categories.find(c => c.id === post.category)?.name}
                      </Badge>
                      <Badge variant="outline">Featured</Badge>
                    </div>
                    <CardTitle className="text-xl leading-tight">
                      {post.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 dark:text-gray-300 mb-4">
                      {post.excerpt}
                    </p>
                    <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-1">
                          <User className="h-4 w-4" />
                          {post.author}
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          {formatDate(post.publishedAt)}
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          {post.readTime}
                        </div>
                      </div>
                    </div>
                    <div className="flex flex-wrap gap-1 mb-4">
                      {post.tags.slice(0, 3).map((tag, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                    <Button variant="outline" className="w-full">
                      Baca Selengkapnya
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>
        )}

        {/* Regular Posts */}
        <section>
          <h2 className="text-2xl font-bold mb-8 text-center">
            {selectedCategory === "all" ? "Semua Artikel" : `Artikel ${categories.find(c => c.id === selectedCategory)?.name}`}
          </h2>
          
          {regularPosts.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {regularPosts.map((post) => (
                <Card key={post.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="h-32 bg-gradient-to-r from-gray-400 to-gray-600"></div>
                  <CardHeader>
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant="secondary">
                        {categories.find(c => c.id === post.category)?.name}
                      </Badge>
                    </div>
                    <CardTitle className="text-lg leading-tight">
                      {post.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 dark:text-gray-300 mb-4 text-sm">
                      {post.excerpt.substring(0, 120)}...
                    </p>
                    <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-4">
                      <div className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        {post.author}
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {post.readTime}
                      </div>
                    </div>
                    <Button variant="outline" size="sm" className="w-full">
                      Baca Artikel
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Tidak ada artikel ditemukan</h3>
              <p className="text-gray-600 dark:text-gray-300">
                Coba ubah kata kunci pencarian atau pilih kategori lain.
              </p>
            </div>
          )}
        </section>

        {/* Newsletter Subscription */}
        <section className="mt-16">
          <Card className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20">
            <CardContent className="py-12 text-center">
              <h2 className="text-2xl font-bold mb-4">Berlangganan Newsletter</h2>
              <p className="text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
                Dapatkan tips bisnis terbaru, panduan teknologi, dan update fitur KivaPOS 
                langsung di inbox Anda setiap minggu.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                <Input
                  type="email"
                  placeholder="Masukkan email Anda"
                  className="flex-1"
                />
                <Button>
                  Berlangganan
                </Button>
              </div>
            </CardContent>
          </Card>
        </section>
      </div>
    </PublicPageLayout>
  );
};
