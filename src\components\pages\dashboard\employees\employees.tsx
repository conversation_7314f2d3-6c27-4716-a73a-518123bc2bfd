"use client";

import React, { useState } from "react";
import { Role } from "@prisma/client";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { UserCog, Plus } from "lucide-react";
import { useSession } from "next-auth/react";
import Link from "next/link";

// Import the existing employee management component
import EmployeeManagement from "./employee-management";

interface Employee {
  id: string;
  name: string;
  employeeId: string;
  role: Role;
  createdAt?: Date;
}

interface EmployeesPageProps {
  employees: Employee[];
}

const EmployeesPage: React.FC<EmployeesPageProps> = ({ employees }) => {
  const { data: session } = useSession();
  const userRole = session?.user?.role as Role | undefined;

  // Check if user can access employees (only OWNER)
  const canAccessEmployees = userRole === Role.OWNER;

  if (!canAccessEmployees) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <UserCog className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Akses Terbatas
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Hanya pemilik yang dapat mengakses halaman karyawan.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 rounded-lg p-6 border border-purple-100 dark:border-purple-800">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
              <UserCog className="h-7 w-7 text-purple-600 dark:text-purple-400" />
              Manajemen Karyawan
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Kelola akun karyawan dan hak akses sistem KivaPOS Anda
            </p>
          </div>
          <div className="flex items-center gap-4">
            <Badge
              variant="outline"
              className="bg-purple-50 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400 border-purple-200 dark:border-purple-800 text-sm px-3 py-1"
            >
              {employees.length} Total Karyawan
            </Badge>
          </div>
        </div>
      </div>

      {/* Employee Management Component */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <EmployeeManagement />
      </div>
    </div>
  );
};

export default EmployeesPage;
