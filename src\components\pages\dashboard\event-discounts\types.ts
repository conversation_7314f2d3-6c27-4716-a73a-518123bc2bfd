// Shared types for Event Discount components

export interface EventDiscount {
  id: string;
  name: string;
  description?: string;
  discountPercentage: number;
  startDate: Date;
  endDate: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  products: {
    id: string;
    product: {
      id: string;
      name: string;
      price: number;
    };
  }[];
  _count: {
    products: number;
    sales: number;
  };
}

// Raw database type (what comes from Prisma)
export interface EventDiscountRaw {
  id: string;
  name: string;
  description: string | null;
  discountPercentage: any; // Decimal type from Prisma
  startDate: Date;
  endDate: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  products: {
    id: string;
    product: {
      id: string;
      name: string;
      price: any; // Decimal type from Prisma
    };
  }[];
  _count: {
    products: number;
    sales: number;
  };
}

// Transform function to convert raw database data to component-friendly format
export function transformEventDiscount(raw: EventDiscountRaw): EventDiscount {
  return {
    ...raw,
    description: raw.description || undefined,
    discountPercentage: Number(raw.discountPercentage),
    products: raw.products.map(p => ({
      ...p,
      product: {
        ...p.product,
        price: Number(p.product.price),
      },
    })),
  };
}

export function transformEventDiscounts(rawDiscounts: EventDiscountRaw[]): EventDiscount[] {
  return rawDiscounts.map(transformEventDiscount);
}
