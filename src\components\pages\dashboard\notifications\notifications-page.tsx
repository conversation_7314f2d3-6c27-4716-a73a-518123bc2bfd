"use client";

import React, { useEffect } from "react";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import {
  BellIcon,
  FunnelIcon,
  ArrowPathIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  InformationCircleIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  CalendarDaysIcon,
  EyeIcon,
  EyeSlashIcon,
  SparklesIcon,
} from "@heroicons/react/24/outline";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  NotificationItem,
  NotificationType,
} from "@/actions/notifications/notifications";
import { useNotificationStore } from "@/stores/notification-store";
import { cn } from "@/lib/utils";

const ITEMS_PER_PAGE = 10;

export default function NotificationsPage() {
  const {
    notifications,
    loading,
    error,
    totalCount,
    currentPage,
    activeTab,
    dateRange,
    readStatus,
    showUnreadOnly,
    fetchNotifications,
    setPage,
    setTab,
    setDateRange,
    setReadStatus,
    setShowUnreadOnly,
    clearFilters,
    markAsRead,
    markAllAsRead,
    refresh,
  } = useNotificationStore();

  useEffect(() => {
    fetchNotifications();
  }, [fetchNotifications]);

  const handlePageChange = (page: number) => {
    if (page > 0 && page <= totalPages) {
      setPage(page);
    }
  };

  const handleTabChange = (value: string) => {
    setTab(value as NotificationType | "all");
  };

  const handleDateRangeChange = (
    field: "startDate" | "endDate",
    date: Date | undefined
  ) => {
    if (date) {
      setDateRange(field, format(date, "yyyy-MM-dd"));
    } else {
      setDateRange(field, "");
    }
  };

  const handleReadStatusChange = (value: string) => {
    setReadStatus(value as "all" | "read" | "unread");
  };

  const handleUnreadOnlyChange = (checked: boolean) => {
    setShowUnreadOnly(checked);
  };

  const handleRefresh = () => {
    refresh();
  };

  const handleClearFilters = () => {
    clearFilters();
  };

  const handleMarkAsRead = (id: string) => {
    markAsRead(id);
  };

  const handleMarkAllAsRead = () => {
    markAllAsRead();
  };

  const totalPages = Math.ceil(totalCount / ITEMS_PER_PAGE);
  const unreadCount = notifications.filter(
    (n: NotificationItem) => !n.isRead
  ).length;

  const getNotificationIcon = (type: NotificationType) => {
    const iconClass = "h-5 w-5";
    switch (type) {
      case "info":
        return (
          <InformationCircleIcon className={`${iconClass} text-blue-600`} />
        );
      case "warning":
        return (
          <ExclamationTriangleIcon className={`${iconClass} text-amber-600`} />
        );
      case "success":
        return <CheckCircleIcon className={`${iconClass} text-emerald-600`} />;
      case "error":
        return <XCircleIcon className={`${iconClass} text-red-600`} />;
      default:
        return <BellIcon className={`${iconClass} text-slate-600`} />;
    }
  };

  const getNotificationColor = (type: NotificationType) => {
    switch (type) {
      case "info":
        return "bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950/50 dark:text-blue-300 dark:border-blue-800/50";
      case "warning":
        return "bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-950/50 dark:text-amber-300 dark:border-amber-800/50";
      case "success":
        return "bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-950/50 dark:text-emerald-300 dark:border-emerald-800/50";
      case "error":
        return "bg-red-50 text-red-700 border-red-200 dark:bg-red-950/50 dark:text-red-300 dark:border-red-800/50";
      default:
        return "bg-slate-50 text-slate-700 border-slate-200 dark:bg-slate-800/50 dark:text-slate-300 dark:border-slate-700/50";
    }
  };

  const getNotificationBadgeIcon = (type: NotificationType) => {
    const iconClass = "h-3 w-3";
    switch (type) {
      case "info":
        return <InformationCircleIcon className={iconClass} />;
      case "warning":
        return <ExclamationTriangleIcon className={iconClass} />;
      case "success":
        return <CheckCircleIcon className={iconClass} />;
      case "error":
        return <XCircleIcon className={iconClass} />;
      default:
        return <BellIcon className={iconClass} />;
    }
  };

  return (
    <div className="min-h-screen">
      {/* Header Section */}
      <div className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm border-b border-slate-200/60 dark:border-slate-700/60 sticky top-0 z-10">
        <div className="mx-auto px-2 lg:px-4">
          <div className="py-6 sm:py-8">
            <div className="flex flex-col sm:flex-row sm:items-center gap-4">
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 rounded-2xl bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center shadow-lg relative">
                  <BellIcon className="h-6 w-6 text-white" />
                  {unreadCount > 0 && (
                    <div className="absolute -top-1 -right-1 h-5 w-5 bg-red-500 rounded-full flex items-center justify-center">
                      <span className="text-xs font-bold text-white">
                        {unreadCount > 99 ? "99+" : unreadCount}
                      </span>
                    </div>
                  )}
                </div>
                <div>
                  <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-white dark:to-slate-300 bg-clip-text text-transparent">
                    Notifikasi
                  </h1>
                  <p className="text-slate-600 dark:text-slate-400 text-sm sm:text-base">
                    Kelola semua notifikasi dan pemberitahuan sistem
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-2 sm:ml-auto">
                {unreadCount > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleMarkAllAsRead}
                    className="flex items-center gap-2 bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border-slate-200 dark:border-slate-700 hover:bg-white dark:hover:bg-slate-800"
                  >
                    <CheckCircleIcon className="h-4 w-4" />
                    <span className="hidden sm:inline">Tandai Semua</span>
                  </Button>
                )}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRefresh}
                  className="flex items-center gap-2 bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border-slate-200 dark:border-slate-700 hover:bg-white dark:hover:bg-slate-800 cursor-pointer"
                >
                  <ArrowPathIcon className="h-4 w-4" />
                  <span className="hidden sm:inline">Refresh</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleClearFilters}
                  className="flex items-center gap-2 bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border-slate-200 dark:border-slate-700 hover:bg-white dark:hover:bg-slate-800 cursor-pointer"
                >
                  <FunnelIcon className="h-4 w-4" />
                  <span className="hidden sm:inline">Reset</span>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="mx-auto py-6 sm:py-8">
        <Card className="shadow-xl bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm border-0 ring-1 ring-slate-200/60 dark:ring-slate-700/60">
          <CardHeader className="pb-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <CardTitle className="text-xl sm:text-2xl font-semibold text-slate-900 dark:text-white">
                  Daftar Notifikasi
                </CardTitle>
                <CardDescription className="text-slate-600 dark:text-slate-400 mt-1">
                  Semua notifikasi dan pemberitahuan sistem dalam waktu
                  real-time
                </CardDescription>
              </div>

              {/* Notification Stats */}
              <div className="flex items-center gap-6 text-sm text-slate-600 dark:text-slate-400">
                <div className="flex items-center gap-2">
                  <div className="h-2 w-2 rounded-full bg-green-500 animate-pulse"></div>
                  <span>Live</span>
                </div>
                {unreadCount > 0 && (
                  <div className="hidden sm:flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-red-500"></div>
                    <span className="font-medium text-slate-900 dark:text-white">
                      {unreadCount}
                    </span>
                    <span>Belum dibaca</span>
                  </div>
                )}
                <div className="hidden sm:flex items-center gap-2">
                  <span className="font-medium text-slate-900 dark:text-white">
                    {totalCount}
                  </span>
                  <span>Total</span>
                </div>
              </div>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Enhanced Filter Section */}
            <div className="space-y-6">
              {/* Notification Type Tabs */}
              <Tabs
                defaultValue="all"
                value={activeTab}
                onValueChange={handleTabChange}
                className="w-full"
              >
                <TabsList className="grid grid-cols-5 h-12 bg-slate-100/80 dark:bg-slate-700/50 p-1 rounded-xl w-full md:w-fit">
                  <TabsTrigger
                    value="all"
                    className="text-xs sm:text-sm font-medium rounded-lg data-[state=active]:bg-white dark:data-[state=active]:bg-slate-600 data-[state=active]:shadow-sm"
                  >
                    <span className="hidden sm:inline">Semua</span>
                    <span className="sm:hidden">All</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="info"
                    className="text-xs sm:text-sm font-medium rounded-lg data-[state=active]:bg-white dark:data-[state=active]:bg-slate-600 data-[state=active]:shadow-sm"
                  >
                    <InformationCircleIcon className="h-4 w-4 sm:hidden" />
                    <span className="hidden sm:inline">Info</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="warning"
                    className="text-xs sm:text-sm font-medium rounded-lg data-[state=active]:bg-white dark:data-[state=active]:bg-slate-600 data-[state=active]:shadow-sm"
                  >
                    <ExclamationTriangleIcon className="h-4 w-4 sm:hidden" />
                    <span className="hidden sm:inline">Peringatan</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="success"
                    className="text-xs sm:text-sm font-medium rounded-lg data-[state=active]:bg-white dark:data-[state=active]:bg-slate-600 data-[state=active]:shadow-sm"
                  >
                    <CheckCircleIcon className="h-4 w-4 sm:hidden" />
                    <span className="hidden sm:inline">Sukses</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="error"
                    className="text-xs sm:text-sm font-medium rounded-lg data-[state=active]:bg-white dark:data-[state=active]:bg-slate-600 data-[state=active]:shadow-sm"
                  >
                    <XCircleIcon className="h-4 w-4 sm:hidden" />
                    <span className="hidden sm:inline">Error</span>
                  </TabsTrigger>
                </TabsList>
              </Tabs>

              {/* Date and Status Filters */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <Label
                    htmlFor="startDate"
                    className="text-sm font-medium text-slate-700 dark:text-slate-300 flex items-center gap-2"
                  >
                    <CalendarDaysIcon className="h-4 w-4" />
                    Tanggal Mulai
                  </Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal bg-white/80 dark:bg-slate-700/50 border-slate-200 dark:border-slate-600 !h-11",
                          !dateRange.startDate && "text-muted-foreground"
                        )}
                      >
                        <CalendarDaysIcon className="mr-2 h-4 w-4" />
                        {dateRange.startDate ? (
                          format(new Date(dateRange.startDate), "PPP", {
                            locale: id,
                          })
                        ) : (
                          <span>Pilih tanggal</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={
                          dateRange.startDate
                            ? new Date(dateRange.startDate)
                            : undefined
                        }
                        onSelect={(date) =>
                          handleDateRangeChange("startDate", date)
                        }
                        initialFocus
                        locale={id}
                      />
                    </PopoverContent>
                  </Popover>
                </div>
                <div className="space-y-2">
                  <Label
                    htmlFor="endDate"
                    className="text-sm font-medium text-slate-700 dark:text-slate-300 flex items-center gap-2"
                  >
                    <CalendarDaysIcon className="h-4 w-4" />
                    Tanggal Akhir
                  </Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal bg-white/80 dark:bg-slate-700/50 border-slate-200 dark:border-slate-600 !h-11",
                          !dateRange.endDate && "text-muted-foreground"
                        )}
                      >
                        <CalendarDaysIcon className="mr-2 h-4 w-4" />
                        {dateRange.endDate ? (
                          format(new Date(dateRange.endDate), "PPP", {
                            locale: id,
                          })
                        ) : (
                          <span>Pilih tanggal</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={
                          dateRange.endDate
                            ? new Date(dateRange.endDate)
                            : undefined
                        }
                        onSelect={(date) =>
                          handleDateRangeChange("endDate", date)
                        }
                        initialFocus
                        locale={id}
                      />
                    </PopoverContent>
                  </Popover>
                </div>
                <div className="space-y-2">
                  <Label
                    htmlFor="readStatus"
                    className="text-sm font-medium text-slate-700 dark:text-slate-300 flex items-center gap-2"
                  >
                    <EyeIcon className="h-4 w-4" />
                    Status Dibaca
                  </Label>
                  <Select
                    value={readStatus}
                    onValueChange={handleReadStatusChange}
                    disabled={showUnreadOnly}
                  >
                    <SelectTrigger
                      id="readStatus"
                      className="bg-white/80 dark:bg-slate-700/50 border-slate-200 dark:border-slate-600 focus:ring-2 focus:ring-blue-500"
                    >
                      <SelectValue placeholder="Pilih status dibaca" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Semua</SelectItem>
                      <SelectItem value="read">Sudah Dibaca</SelectItem>
                      <SelectItem value="unread">Belum Dibaca</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label
                    htmlFor="unread-only"
                    className="text-sm font-medium text-slate-700 dark:text-slate-300 flex items-center gap-2"
                  >
                    <EyeSlashIcon className="h-4 w-4" />
                    Filter Khusus
                  </Label>
                  <div className="flex items-center space-x-3 bg-white/80 dark:bg-slate-700/50 border border-slate-200 dark:border-slate-600 rounded-lg px-3 py-2 h-10">
                    <Switch
                      id="unread-only"
                      checked={showUnreadOnly}
                      onCheckedChange={handleUnreadOnlyChange}
                      className="data-[state=checked]:bg-blue-600"
                    />
                    <Label
                      htmlFor="unread-only"
                      className="text-sm cursor-pointer"
                    >
                      Hanya Belum Dibaca
                    </Label>
                  </div>
                </div>
              </div>
            </div>

            <div className="h-px bg-gradient-to-r from-transparent via-slate-200 dark:via-slate-700 to-transparent" />

            {/* Notification List */}
            <div className="space-y-3">
              {loading ? (
                // Enhanced Loading skeleton
                <div className="space-y-3">
                  {Array.from({ length: 5 }).map((_, index: number) => (
                    <div
                      key={index}
                      className="flex items-start gap-4 p-4 sm:p-6 bg-white/60 dark:bg-slate-700/30 rounded-2xl border border-slate-200/60 dark:border-slate-600/60"
                    >
                      <Skeleton className="h-12 w-12 rounded-2xl" />
                      <div className="flex-1 space-y-3">
                        <div className="flex items-center gap-2">
                          <Skeleton className="h-6 w-20 rounded-full" />
                          <Skeleton className="h-4 w-24" />
                          <Skeleton className="h-6 w-24 rounded-full ml-auto" />
                        </div>
                        <Skeleton className="h-5 w-full max-w-md" />
                        <Skeleton className="h-4 w-full max-w-lg" />
                        <Skeleton className="h-8 w-24 rounded-lg" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : error ? (
                <div className="p-6 text-red-600 bg-red-50 dark:bg-red-900/20 rounded-2xl border border-red-200 dark:border-red-800/50">
                  <div className="flex items-center gap-3">
                    <div className="h-8 w-8 rounded-full bg-red-100 dark:bg-red-900/40 flex items-center justify-center">
                      <XCircleIcon className="h-4 w-4 text-red-600" />
                    </div>
                    <div>
                      <h3 className="font-medium">Terjadi Kesalahan</h3>
                      <p className="text-sm text-red-500 dark:text-red-400">
                        {error}
                      </p>
                    </div>
                  </div>
                </div>
              ) : notifications.length === 0 ? (
                <div className="p-12 text-center">
                  <div className="mx-auto h-20 w-20 rounded-3xl bg-slate-100 dark:bg-slate-700 flex items-center justify-center mb-6">
                    <BellIcon className="h-10 w-10 text-slate-400 dark:text-slate-500" />
                  </div>
                  <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">
                    Tidak ada notifikasi
                  </h3>
                  <p className="text-slate-600 dark:text-slate-400 max-w-md mx-auto">
                    Tidak ada notifikasi yang ditemukan dengan filter yang
                    dipilih. Coba ubah kriteria pencarian Anda.
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  {notifications.map(
                    (notification: NotificationItem, index: number) => (
                      <div
                        key={notification.id}
                        className={`group flex items-start gap-4 p-4 sm:p-6 rounded-2xl border border-slate-200/60 dark:border-slate-600/60 hover:shadow-lg hover:scale-[1.01] transition-all duration-200 cursor-pointer ${
                          !notification.isRead
                            ? "bg-gradient-to-r from-blue-50/80 to-white dark:from-blue-950/30 dark:to-slate-700/30 ring-1 ring-blue-200/50 dark:ring-blue-800/30"
                            : "bg-white/80 dark:bg-slate-700/30 hover:bg-white dark:hover:bg-slate-700/50"
                        }`}
                        style={{
                          animationDelay: `${index * 100}ms`,
                          animation: "fadeInUp 0.5s ease-out forwards",
                        }}
                      >
                        <div className="h-12 w-12 rounded-2xl bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-600 dark:to-slate-700 flex items-center justify-center group-hover:scale-110 transition-transform duration-200 shadow-sm relative">
                          {getNotificationIcon(notification.type)}
                          {!notification.isRead && (
                            <div className="absolute -top-1 -right-1 h-3 w-3 bg-blue-500 rounded-full animate-pulse"></div>
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-2">
                            <div className="flex items-center gap-2">
                              <Badge
                                variant="outline"
                                className={`w-fit text-xs font-medium px-3 py-1 rounded-full border ${getNotificationColor(notification.type)} flex items-center gap-1.5`}
                              >
                                {getNotificationBadgeIcon(notification.type)}
                                {notification.type.toUpperCase()}
                              </Badge>
                              <span className="text-xs text-slate-500 dark:text-slate-400 flex items-center gap-1">
                                <CalendarDaysIcon className="h-3 w-3" />
                                {notification.timestamp}
                              </span>
                            </div>
                            {!notification.isRead && (
                              <Badge
                                variant="outline"
                                className="w-fit bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950/50 dark:text-blue-300 dark:border-blue-800/50 text-xs px-2 py-1 rounded-full flex items-center gap-1"
                              >
                                <SparklesIcon className="h-3 w-3" />
                                Belum Dibaca
                              </Badge>
                            )}
                          </div>
                          <h3 className="text-sm sm:text-base font-semibold text-slate-900 dark:text-white mb-2 leading-relaxed">
                            {notification.title}
                          </h3>
                          <p className="text-sm text-slate-600 dark:text-slate-400 leading-relaxed mb-3">
                            {notification.message}
                          </p>
                          {!notification.isRead && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={(e: React.MouseEvent) => {
                                e.stopPropagation();
                                handleMarkAsRead(notification.id);
                              }}
                              className="h-8 text-xs bg-white/80 dark:bg-slate-600/50 border-slate-200 dark:border-slate-600 hover:bg-blue-50 dark:hover:bg-blue-900/30 hover:border-blue-300 dark:hover:border-blue-700 hover:text-blue-700 dark:hover:text-blue-300 transition-colors"
                            >
                              <CheckCircleIcon className="h-3 w-3 mr-1" />
                              Tandai Dibaca
                            </Button>
                          )}
                        </div>
                      </div>
                    )
                  )}
                </div>
              )}
            </div>

            {/* Enhanced Pagination */}
            {!loading && !error && totalPages > 0 && (
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 pt-6 border-t border-slate-200/60 dark:border-slate-700/60">
                <div className="text-sm text-slate-600 dark:text-slate-400 text-center sm:text-left">
                  Menampilkan{" "}
                  <span className="font-medium text-slate-900 dark:text-white">
                    {notifications.length}
                  </span>{" "}
                  dari{" "}
                  <span className="font-medium text-slate-900 dark:text-white">
                    {totalCount}
                  </span>{" "}
                  notifikasi
                </div>
                <div className="flex items-center justify-center sm:justify-end gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="h-10 w-10 p-0 rounded-xl bg-white/80 dark:bg-slate-700/50 border-slate-200 dark:border-slate-600 hover:bg-white dark:hover:bg-slate-700 disabled:opacity-40"
                  >
                    <ChevronLeftIcon className="h-4 w-4" />
                  </Button>

                  <div className="flex items-center gap-2 px-4 py-2 bg-slate-100/80 dark:bg-slate-700/50 rounded-xl">
                    <span className="text-sm font-medium text-slate-900 dark:text-white">
                      {currentPage}
                    </span>
                    <span className="text-sm text-slate-400">dari</span>
                    <span className="text-sm font-medium text-slate-900 dark:text-white">
                      {totalPages}
                    </span>
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="h-10 w-10 p-0 rounded-xl bg-white/80 dark:bg-slate-700/50 border-slate-200 dark:border-slate-600 hover:bg-white dark:hover:bg-slate-700 disabled:opacity-40"
                  >
                    <ChevronRightIcon className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
    </div>
  );
}
