"use client";

import React from "react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Package } from "lucide-react";
import Image from "next/image";

interface Category {
  id: string;
  name: string;
}

interface ProductSummaryProps {
  name: string;
  image: string | null;
  price: number;
  wholesalePrice: number | null;
  cost: number | null;
  stock: number;
  minStockLevel?: number;
  category: Category | null;
}

const ProductSummary: React.FC<ProductSummaryProps> = ({
  name,
  image,
  price,
  wholesalePrice,
  cost,
  stock,
  minStockLevel,
  category,
}) => {
  // Calculate profit if both price and cost are available
  const profit = price && cost ? price - cost : null;
  const profitPercentage = profit && cost ? (profit / cost) * 100 : null;

  // Determine stock status
  const getStockStatus = () => {
    if (stock <= 0) return { label: "Habis", color: "destructive" };
    if (minStockLevel && stock <= minStockLevel)
      return { label: "Stok Rendah", color: "warning" };
    return { label: "Tersedia", color: "success" };
  };

  const stockStatus = getStockStatus();

  return (
    <div className="space-y-6">
      {/* Product Image Card */}
      <Card className="gap-2">
        <CardHeader className="">
          <CardTitle className="text-lg">Gambar Produk</CardTitle>
        </CardHeader>
        <CardContent>
          {image ? (
            <div className="relative w-full h-64 rounded-md overflow-hidden">
              <Image
                src={image}
                alt={name}
                fill
                style={{ objectFit: "contain" }}
                className="bg-white rounded-md"
              />
            </div>
          ) : (
            <div className="w-full h-64 border rounded-md flex items-center justify-center bg-gray-100">
              <Package className="h-12 w-12 text-gray-400" />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Summary Card - Simple & Interactive */}
      <Card className="hover:shadow-lg transition-shadow duration-200 gap-2">
        <CardHeader>
          <CardTitle className="text-base sm:text-lg">Ringkasan</CardTitle>
        </CardHeader>

        <CardContent className="space-y-3 sm:space-y-4 text-sm sm:text-base">
          {/* Stock Status */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 p-3 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-200">
            <div className="flex items-center gap-2 sm:gap-3">
              <div
                className={`w-2.5 h-2.5 sm:w-3 sm:h-3 rounded-full ${
                  stockStatus.color === "destructive"
                    ? "bg-red-500"
                    : stockStatus.color === "warning"
                      ? "bg-amber-500"
                      : "bg-green-500"
                }`}
              />
              <span className="text-xs sm:text-sm font-medium">
                Status Stok
              </span>
            </div>
            <Badge
              variant={
                stockStatus.color === "warning"
                  ? "outline"
                  : stockStatus.color === "success"
                    ? "secondary"
                    : "destructive"
              }
              className={`text-xs sm:text-sm ${
                stockStatus.color === "warning"
                  ? "bg-amber-100 text-amber-700 dark:bg-amber-900/20 dark:text-amber-400 border-amber-200 dark:border-amber-800"
                  : stockStatus.color === "success"
                    ? "bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400 border-green-200 dark:border-green-800"
                    : ""
              }`}
            >
              {stockStatus.label}
            </Badge>
          </div>

          {/* Price Info */}
          <div className="space-y-2 sm:space-y-3">
            <div className="p-3 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-200">
              <p className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                Harga Jual
              </p>
              <p className="text-xl sm:text-2xl font-bold">
                Rp {price.toLocaleString("id-ID")}
              </p>
            </div>

            {wholesalePrice && (
              <div className="p-3 rounded-lg border hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors duration-200">
                <p className="text-xs sm:text-sm font-medium text-blue-600 dark:text-blue-400 mb-1">
                  Harga Grosir
                </p>
                <p className="text-lg sm:text-xl font-bold text-blue-700 dark:text-blue-300">
                  Rp {wholesalePrice.toLocaleString("id-ID")}
                </p>
              </div>
            )}
          </div>

          {/* Cost and Profit */}
          {cost !== null && (
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
              <div className="p-3 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-200">
                <p className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                  Harga Beli
                </p>
                <p className="text-base sm:text-lg font-bold">
                  Rp {cost.toLocaleString("id-ID")}
                </p>
              </div>
              {profit !== null && (
                <div className="p-3 rounded-lg border hover:bg-emerald-50 dark:hover:bg-emerald-900/20 transition-colors duration-200">
                  <p className="text-xs sm:text-sm font-medium text-emerald-600 dark:text-emerald-400 mb-1">
                    Keuntungan
                  </p>
                  <div className="flex items-center gap-1 sm:gap-2">
                    <p className="text-base sm:text-lg font-bold text-emerald-700 dark:text-emerald-300">
                      Rp {profit.toLocaleString("id-ID")}
                    </p>
                    {profitPercentage !== null && (
                      <Badge
                        variant="outline"
                        className="text-emerald-600 border-emerald-300 text-xs sm:text-sm"
                      >
                        +{profitPercentage.toFixed(1)}%
                      </Badge>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Stock Info */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
            <div className="p-3 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-200">
              <p className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                Stok Saat Ini
              </p>
              <div className="flex items-baseline gap-1">
                <span className="text-base sm:text-lg font-bold">{stock}</span>
                <span className="text-xs sm:text-sm text-gray-500 dark:text-gray-400">
                  unit
                </span>
              </div>
            </div>
            {minStockLevel !== undefined && (
              <div className="p-3 rounded-lg border hover:bg-orange-50 dark:hover:bg-orange-900/20 transition-colors duration-200">
                <p className="text-xs sm:text-sm font-medium text-orange-600 dark:text-orange-400 mb-1">
                  Stok Minimum
                </p>
                <div className="flex items-baseline gap-1">
                  <span className="text-base sm:text-lg font-bold">
                    {minStockLevel}
                  </span>
                  <span className="text-xs sm:text-sm text-gray-500 dark:text-gray-400">
                    unit
                  </span>
                </div>
              </div>
            )}
          </div>

          {/* Category */}
          {category && (
            <div className="flex items-center justify-between p-3 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-200">
              <span className="text-xs sm:text-sm font-medium">Kategori</span>
              <Badge variant="secondary" className="text-xs sm:text-sm">
                {category.name}
              </Badge>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ProductSummary;
