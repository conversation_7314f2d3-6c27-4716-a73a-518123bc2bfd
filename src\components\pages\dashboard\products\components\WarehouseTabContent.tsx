"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { WarehouseManagement } from "../../warehouses/components/WarehouseManagement";
import { WarehouseStockOverview } from "../../warehouses/components/WarehouseStockOverview";
import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent, CardHeader } from "@/components/ui/card";

// Skeleton components for each tab
const WarehouseManagementSkeleton = () => (
  <div className="space-y-6">
    {/* Summary Cards Skeleton */}
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      {[...Array(3)].map((_, i) => (
        <Card key={i}>
          <CardHeader className="pb-2">
            <Skeleton className="h-4 w-24" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-8 w-16 mb-2" />
            <Skeleton className="h-3 w-32" />
          </CardContent>
        </Card>
      ))}
    </div>

    {/* Search and Actions Skeleton */}
    <div className="flex flex-col sm:flex-row gap-4 justify-between">
      <Skeleton className="h-10 w-full sm:w-80" />
      <div className="flex gap-2">
        <Skeleton className="h-10 w-24" />
        <Skeleton className="h-10 w-20" />
        <Skeleton className="h-10 w-20" />
        <Skeleton className="h-10 w-32" />
      </div>
    </div>

    {/* Table Skeleton */}
    <Card>
      <CardHeader>
        <Skeleton className="h-6 w-40" />
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex items-center space-x-4">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-4 w-48" />
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-16" />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  </div>
);

const WarehouseStockSkeleton = () => (
  <div className="space-y-6">
    {/* Warehouse Selector Skeleton */}
    <div className="flex flex-col sm:flex-row gap-4">
      <Skeleton className="h-10 w-full sm:w-80" />
      <Skeleton className="h-10 w-20" />
    </div>

    {/* Stock Table Skeleton */}
    <Card>
      <CardHeader>
        <Skeleton className="h-6 w-48" />
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {[...Array(8)].map((_, i) => (
            <div
              key={i}
              className="flex items-center justify-between p-3 border rounded-lg"
            >
              <div className="flex-1">
                <Skeleton className="h-4 w-40 mb-2" />
                <Skeleton className="h-3 w-24" />
              </div>
              <div className="flex items-center gap-4">
                <div className="text-right">
                  <Skeleton className="h-4 w-16 mb-1" />
                  <Skeleton className="h-3 w-12" />
                </div>
                <Skeleton className="h-6 w-16" />
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  </div>
);

const StockMovementSkeleton = () => (
  <div className="space-y-6">
    {/* Filters Skeleton */}
    <div className="flex flex-col sm:flex-row gap-4">
      <Skeleton className="h-10 w-full sm:w-60" />
      <Skeleton className="h-10 w-full sm:w-40" />
      <Skeleton className="h-10 w-20" />
    </div>

    {/* Movement History Skeleton */}
    <Card>
      <CardHeader>
        <Skeleton className="h-6 w-48" />
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {[...Array(6)].map((_, i) => (
            <div
              key={i}
              className="flex items-center justify-between p-4 border rounded-lg"
            >
              <div className="flex items-center gap-4">
                <Skeleton className="h-8 w-8 rounded-full" />
                <div>
                  <Skeleton className="h-4 w-32 mb-2" />
                  <Skeleton className="h-3 w-48" />
                </div>
              </div>
              <div className="text-right">
                <Skeleton className="h-4 w-20 mb-1" />
                <Skeleton className="h-3 w-16" />
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  </div>
);

export const WarehouseTabContent: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);

  // Initialize activeTab from URL parameter or default to "warehouses"
  const [activeTab, setActiveTab] = useState(() => {
    const tabParam = searchParams.get("warehouseTab");
    if (tabParam === "stock" || tabParam === "stok-gudang") {
      return "stock";
    } else if (tabParam === "movements" || tabParam === "riwayat-pergerakan") {
      return "movements";
    } else if (tabParam === "warehouses" || tabParam === "daftar-gudang") {
      return "warehouses";
    }
    return "warehouses";
  });

  // Update activeTab when URL changes
  useEffect(() => {
    const tabParam = searchParams.get("warehouseTab");
    if (tabParam === "stock" || tabParam === "stok-gudang") {
      setActiveTab("stock");
    } else if (tabParam === "movements" || tabParam === "riwayat-pergerakan") {
      setActiveTab("movements");
    } else if (tabParam === "warehouses" || tabParam === "daftar-gudang") {
      setActiveTab("warehouses");
    }
  }, [searchParams]);

  // Handle tab change with loading state and URL update
  const handleTabChange = (tabId: string) => {
    if (tabId !== activeTab) {
      setIsLoading(true);
      setActiveTab(tabId);

      // Create new URLSearchParams from current search params
      const params = new URLSearchParams(searchParams.toString());

      if (tabId === "stock") {
        params.set("warehouseTab", "stok-gudang");
      } else if (tabId === "movements") {
        params.set("warehouseTab", "riwayat-pergerakan");
      } else if (tabId === "warehouses") {
        params.set("warehouseTab", "daftar-gudang");
      } else {
        params.delete("warehouseTab"); // Remove tab parameter for default
      }

      // Use router.replace to update URL without refresh or navigation
      const newUrl = `${window.location.pathname}?${params.toString()}`;
      router.replace(newUrl, { scroll: false });

      // Simulate loading time for smooth transition
      setTimeout(() => {
        setIsLoading(false);
      }, 300);
    }
  };

  return (
    <div className="space-y-6">
      {/* shadcn/ui Tabs */}
      <Tabs
        value={activeTab}
        onValueChange={handleTabChange}
        className="w-full"
      >
        <TabsList className="grid w-full md:w-fit grid-cols-2">
          <TabsTrigger
            className="data-[state=active]:bg-blue-400 data-[state=active]:text-white"
            value="warehouses"
          >
            Daftar Gudang
          </TabsTrigger>
          <TabsTrigger
            className="data-[state=active]:bg-blue-400 data-[state=active]:text-white"
            value="stock"
          >
            Stok Gudang
          </TabsTrigger>
        </TabsList>

        <TabsContent value="warehouses" className="space-y-6">
          {isLoading ? (
            <WarehouseManagementSkeleton />
          ) : (
            <WarehouseManagement />
          )}
        </TabsContent>

        <TabsContent value="stock" className="space-y-6">
          {isLoading ? <WarehouseStockSkeleton /> : <WarehouseStockOverview />}
        </TabsContent>
      </Tabs>
    </div>
  );
};
