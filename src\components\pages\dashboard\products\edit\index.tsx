"use client";

import React, { useEffect, useTransition, useState } from "react";
// Link is no longer needed
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import {
  uploadProductImage,
  deleteProductImage,
} from "@/actions/uploads/images";
import { updateProduct } from "@/actions/entities/products";

import DashboardLayout from "@/components/layout/dashboardlayout";
import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

import { EnhancedProductSchema } from "../new/types";
import { ProductFormValues } from "../new/types";
import ProductFormTabs from "../new/components/ProductFormTabs";
import { ArrowLeft, Check, Save, PackageOpen } from "lucide-react";
import { useUnsavedChangesWarning } from "@/hooks/useUnsavedChangesWarning";

interface Category {
  id: string;
  name: string;
}

interface Product {
  id: string;
  name: string;
  description: string | null;
  sku: string | null;
  barcode: string | null;
  price: number;
  wholesalePrice: number | null;
  cost: number | null;
  stock: number;
  image: string | null;
  weight: number | null;
  length: number | null;
  width: number | null;
  height: number | null;
  tags: string[];
  isDraft: boolean;
  createdAt: Date;
  updatedAt: Date;
  categoryId: string | null;
  category: Category | null;
  unit: string | null;
  unitId: string | null;
  // Individual tax rates for each price type
  salePriceTaxRate: number | null;
  wholesalePriceTaxRate: number | null;
  costPriceTaxRate: number | null;
}

interface EditProductPageProps {
  product: Product;
}

const EnhancedEditProductPage: React.FC<EditProductPageProps> = ({
  product,
}) => {
  const router = useRouter();
  // We don't need pathname for this component
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isPending, startTransition] = useTransition();
  // We need to use imageUrl for the form and preview
  const [imageUrl, setImageUrl] = React.useState<string>(product.image || "");
  const [isUploading, setIsUploading] = React.useState(false);
  const [previewUrl, setPreviewUrl] = React.useState<string>(
    product.image || ""
  );
  const [fileInputKey, setFileInputKey] = React.useState<number>(0);
  const fileInputRef = React.useRef<HTMLInputElement | null>(null);
  const [isFormValid, setIsFormValid] = useState(true); // Assume valid initially since we're editing
  const [originalValues, setOriginalValues] =
    useState<ProductFormValues | null>(null);

  // Initialize the form with enhanced schema and product data
  const form = useForm<ProductFormValues>({
    resolver: zodResolver(EnhancedProductSchema),
    defaultValues: {
      name: product.name,
      description: product.description || "",
      sku: product.sku || "",
      price: product.price,
      wholesalePrice: product.wholesalePrice || undefined,
      cost: product.cost || 0,
      stock: product.stock,
      image: product.image || "",
      categoryId: product.categoryId || undefined,
      barcode: product.barcode || "",
      salePriceTaxRate: product.salePriceTaxRate || 0,
      wholesalePriceTaxRate: product.wholesalePriceTaxRate || 0,
      costPriceTaxRate: product.costPriceTaxRate || 0,
      hasVariants: (product as any).hasVariants || false,
      trackInventory: true,
      minStockLevel: 0,
      weight: product.weight || 0,
      length: product.length || 0,
      width: product.width || 0,
      height: product.height || 0,
      unit: product.unit || "Pcs",
      unitId: product.unitId || undefined,
      tags: product.tags || [],
      colorVariants: Array.isArray((product as any).variants)
        ? (product as any).variants.map((variant: any) => ({
            ...variant,
            sku: variant.sku || "",
            image: variant.image || "",
          }))
        : [],
      isDraft: product.isDraft || false,
    },
  });

  // Handle image deletion
  const handleImageDelete = async (imageUrl: string) => {
    try {
      const result = await deleteProductImage(imageUrl);
      if (result.success) {
        toast.success("Gambar berhasil dihapus");
        setImageUrl("");
        setPreviewUrl("");
      } else {
        toast.error(result.error || "Gagal menghapus gambar");
      }
    } catch (error) {
      console.error("Delete Error:", error);
      toast.error("Gagal menghapus gambar");
    }
  };

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Create a preview URL for the selected image
    const objectUrl = URL.createObjectURL(file);
    setPreviewUrl(objectUrl);

    // Upload the image to Vercel Blob
    setIsUploading(true);
    try {
      const formData = new FormData();
      formData.append("file", file);

      const result = await uploadProductImage(formData);

      if (result.success && result.url) {
        setImageUrl(result.url);
        form.setValue("image", result.url);
        toast.success("Gambar berhasil diunggah");
      } else {
        toast.error(result.error || "Gagal mengunggah gambar");
        // Clear the file input
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      }
    } catch (error) {
      console.error("Upload Error:", error);
      toast.error("Gagal mengunggah gambar");
    } finally {
      setIsUploading(false);
    }
  };

  // Function to highlight invalid fields with red borders
  const highlightInvalidFields = (fieldNames: string[]) => {
    fieldNames.forEach((fieldName) => {
      // Special handling for categoryId field
      if (fieldName === "categoryId") {
        // Find the category combobox button within the categoryId form field
        const categoryFormField = document.querySelector(
          '[data-field="categoryId"]'
        );
        if (categoryFormField) {
          const comboboxButton = categoryFormField.querySelector(
            'button[role="combobox"]'
          );
          if (comboboxButton) {
            comboboxButton.classList.add(
              "border-red-500",
              "ring-1",
              "ring-red-500"
            );
          }
        }
        return;
      }

      // Standard handling for other input fields
      const inputElement = document.querySelector(
        `[name="${fieldName}"]`
      ) as HTMLElement;
      if (inputElement) {
        // Find the closest parent with form-control class and add red border
        const formControl =
          inputElement.closest(".form-control") || inputElement.parentElement;
        if (formControl) {
          formControl.classList.add("border-red-500", "ring-1", "ring-red-500");
        }
      }
    });
  };

  const onSubmit = (values: ProductFormValues) => {
    // Reset any previous validation styling
    document.querySelectorAll(".border-red-500").forEach((el) => {
      el.classList.remove("border-red-500", "ring-1", "ring-red-500");
    });

    // Also reset any combobox buttons that might have validation styling
    document.querySelectorAll('button[role="combobox"]').forEach((el) => {
      el.classList.remove("border-red-500", "ring-1", "ring-red-500");
    });

    // Track invalid fields
    const invalidFields: string[] = [];

    // Validate required fields
    if (!values.name) {
      toast.error("Nama Produk wajib diisi");
      invalidFields.push("name");
    }

    if (!values.categoryId) {
      toast.error("Kategori Produk wajib diisi");
      invalidFields.push("categoryId");
    }

    if (!values.price || values.price <= 0) {
      toast.error("Harga Jual Satuan wajib diisi dengan nilai lebih dari 0");
      invalidFields.push("price");
    }

    // If there are invalid fields, highlight them and stop submission
    if (invalidFields.length > 0) {
      highlightInvalidFields(invalidFields);
      return;
    }

    startTransition(async () => {
      try {
        // Include all necessary fields including categoryId and variants
        const productData = {
          name: values.name,
          description: values.description,
          sku: values.sku,
          price: values.price,
          wholesalePrice: values.wholesalePrice,
          cost: values.cost,
          stock: values.stock,
          image: values.image,
          categoryId: values.categoryId,
          barcode: values.barcode,
          salePriceTaxRate: values.salePriceTaxRate,
          wholesalePriceTaxRate: values.wholesalePriceTaxRate,
          costPriceTaxRate: values.costPriceTaxRate,
          hasVariants: values.hasVariants,
          trackInventory: values.trackInventory,
          minStockLevel: values.minStockLevel,
          weight: values.weight,
          length: values.length,
          width: values.width,
          height: values.height,
          unit: values.unit,
          unitId: values.unitId, // Add unitId field
          tags: values.tags,
          isDraft: false, // Always set to false when publishing
          colorVariants: values.hasVariants
            ? (values.colorVariants || []).map((variant: any) => ({
                ...variant,
                sku: variant.sku || null,
                image: variant.image || null,
              }))
            : [],
        };

        const result = await updateProduct(product.id, productData);
        if (result.success) {
          toast.success(result.success);
          // Redirect to product detail page
          router.push(`/dashboard/products/detail/${product.id}`);
        } else if (result.error) {
          toast.error(result.error);
        } else {
          toast.error("Terjadi kesalahan yang tidak diketahui.");
        }
      } catch (error) {
        console.error("Submit Error:", error);
        toast.error("Gagal menghubungi server.");
      }
    });
  };

  // Watch form values for summary and validation
  const formValues = form.watch();
  const unitId = form.watch("unitId");

  // Update unit field when unitId changes
  useEffect(() => {
    if (unitId) {
      // Find the unit name from the selected unitId
      const getUnitName = async () => {
        try {
          const response = await fetch(`/api/units/${unitId}`);
          if (response.ok) {
            const data = await response.json();
            if (data.unit && data.unit.name) {
              form.setValue("unit", data.unit.name);
            }
          }
        } catch (error) {
          console.error("Error fetching unit name:", error);
        }
      };

      getUnitName();
    }
  }, [unitId, form]);

  // Listen for unitSelected event from UnitCombobox
  useEffect(() => {
    const handleUnitSelected = (event: any) => {
      const { name } = event.detail;
      if (name) {
        form.setValue("unit", name);
      }
    };

    window.addEventListener("unitSelected", handleUnitSelected);

    return () => {
      window.removeEventListener("unitSelected", handleUnitSelected);
    };
  }, [form]);

  // Store original values for comparison
  useEffect(() => {
    // Add a small delay to ensure form is fully initialized
    const timer = setTimeout(() => {
      setOriginalValues({
        name: product.name,
        description: product.description || "",
        sku: product.sku || "",
        price: product.price,
        wholesalePrice: product.wholesalePrice || undefined,
        cost: product.cost || 0,
        stock: product.stock,
        image: product.image || "",
        categoryId: product.categoryId || undefined,
        barcode: product.barcode || "",
        salePriceTaxRate: product.salePriceTaxRate || 0,
        wholesalePriceTaxRate: product.wholesalePriceTaxRate || 0,
        costPriceTaxRate: product.costPriceTaxRate || 0,
        hasVariants: (product as any).hasVariants || false,
        trackInventory: true,
        minStockLevel: 0,
        weight: product.weight || 0,
        length: product.length || 0,
        width: product.width || 0,
        height: product.height || 0,
        unit: product.unit || "Pcs",
        unitId: product.unitId || undefined,
        tags: product.tags || [],
        colorVariants: Array.isArray((product as any).variants)
          ? (product as any).variants.map((variant: any) => ({
              ...variant,
              sku: variant.sku || "",
              image: variant.image || "",
            }))
          : [],
        isDraft: product.isDraft || false,
      });
    }, 100);

    return () => clearTimeout(timer);
  }, [product]);

  // Check form validity
  useEffect(() => {
    const isValid = form.formState.isValid;
    setIsFormValid(isValid);
  }, [form.formState.isValid]);

  // Check for unsaved changes by comparing with original values
  useEffect(() => {
    if (originalValues) {
      const hasChanges = hasChangesFunction(formValues, originalValues);
      setHasUnsavedChanges(hasChanges);
    }
  }, [formValues, originalValues]);

  // Use the unsaved changes warning hook
  const {
    showExitDialog,
    setShowExitDialog,
    handleNavigation,
    confirmNavigation,
    cancelNavigation,
  } = useUnsavedChangesWarning(hasUnsavedChanges);

  // Compare form values with original values to detect changes
  const hasChangesFunction = (
    current: ProductFormValues,
    original: ProductFormValues
  ): boolean => {
    return (
      current.name !== original.name ||
      current.description !== original.description ||
      current.sku !== original.sku ||
      current.barcode !== original.barcode ||
      current.price !== original.price ||
      current.wholesalePrice !== original.wholesalePrice ||
      current.cost !== original.cost ||
      current.stock !== original.stock ||
      current.image !== original.image ||
      current.categoryId !== original.categoryId ||
      current.salePriceTaxRate !== original.salePriceTaxRate ||
      current.wholesalePriceTaxRate !== original.wholesalePriceTaxRate ||
      current.costPriceTaxRate !== original.costPriceTaxRate ||
      current.weight !== original.weight ||
      current.length !== original.length ||
      current.width !== original.width ||
      current.height !== original.height ||
      current.unit !== original.unit ||
      current.unitId !== original.unitId ||
      JSON.stringify(current.tags) !== JSON.stringify(original.tags) ||
      current.hasVariants !== original.hasVariants ||
      JSON.stringify(current.colorVariants) !==
        JSON.stringify(original.colorVariants) ||
      current.isDraft !== original.isDraft
    );
  };

  // Save as draft to database
  const saveAsDraft = () => {
    startTransition(async () => {
      try {
        // Get current form values and explicitly structure the data
        const currentValues = form.getValues();

        // Set isDraft to true and include color variants if hasVariants is true
        const productData = {
          name: currentValues.name || product.name,
          description: currentValues.description || "",
          sku: currentValues.sku || "",
          price: currentValues.price || product.price,
          wholesalePrice: currentValues.wholesalePrice,
          cost: currentValues.cost || 0,
          stock: currentValues.stock || product.stock,
          image: currentValues.image || "",
          categoryId:
            currentValues.categoryId || product.categoryId || undefined,
          barcode: currentValues.barcode || "",
          salePriceTaxRate: currentValues.salePriceTaxRate || 0,
          wholesalePriceTaxRate: currentValues.wholesalePriceTaxRate || 0,
          costPriceTaxRate: currentValues.costPriceTaxRate || 0,
          hasVariants: currentValues.hasVariants || false,
          trackInventory: currentValues.trackInventory || true,
          minStockLevel: currentValues.minStockLevel || 0,
          weight: currentValues.weight || 0,
          length: currentValues.length || 0,
          width: currentValues.width || 0,
          height: currentValues.height || 0,
          unit: currentValues.unit || "Pcs",
          unitId: currentValues.unitId || "",
          tags: currentValues.tags || [],
          isDraft: true,
          colorVariants: currentValues.hasVariants
            ? (currentValues.colorVariants || []).map((variant: any) => ({
                ...variant,
                sku: variant.sku || null,
                image: variant.image || null,
              }))
            : [],
        };

        const result = await updateProduct(product.id, productData);
        if (result.success) {
          toast.success("Produk berhasil disimpan sebagai draft!");
          // Redirect to product detail page
          router.push(`/dashboard/products/detail/${product.id}`);
        } else if (result.error) {
          toast.error(result.error);
        } else {
          toast.error("Terjadi kesalahan yang tidak diketahui.");
        }
      } catch (error) {
        console.error("Submit Error:", error);
        toast.error("Gagal menghubungi server.");
      }
    });
  };

  return (
    <DashboardLayout>
      <div className="w-full px-4">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <PackageOpen className="h-6 w-6 text-primary" />
            <h1 className="text-2xl font-bold">Edit Produk</h1>
          </div>
          <Button
            variant="outline"
            className="gap-2 cursor-pointer"
            onClick={() =>
              handleNavigation(`/dashboard/products/detail/${product.id}`)
            }
          >
            <ArrowLeft className="h-4 w-4" />
            Kembali
          </Button>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="w-full">
              {/* Main Form Content */}
              <ProductFormTabs
                control={form.control}
                isPending={isPending}
                handleImageUpload={handleImageUpload}
                isUploading={isUploading}
                previewUrl={previewUrl}
                fileInputRef={fileInputRef}
                fileInputKey={fileInputKey}
                onDeleteImage={handleImageDelete}
              />
            </div>

            {/* Form Actions */}
            <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700 flex justify-end gap-4">
              <Button
                type="button"
                variant="outline"
                disabled={isPending}
                className="cursor-pointer"
                onClick={() =>
                  handleNavigation(`/dashboard/products/detail/${product.id}`)
                }
              >
                Batal
              </Button>
              <Button
                type="button"
                variant="secondary"
                disabled={isPending || !hasUnsavedChanges}
                className="gap-2 cursor-pointer"
                onClick={saveAsDraft}
              >
                <Save className="h-4 w-4" />
                <span>Simpan ke Draft</span>
              </Button>
              <Button
                type="submit"
                disabled={isPending || !hasUnsavedChanges}
                className="gap-2 cursor-pointer"
              >
                {isPending ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Menyimpan...</span>
                  </>
                ) : (
                  <>
                    <Check className="h-4 w-4" />
                    <span>Edit Produk</span>
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </div>

      {/* Unsaved Changes Dialog */}
      <AlertDialog open={showExitDialog} onOpenChange={setShowExitDialog}>
        <AlertDialogContent className="w-[95%] max-w-md md:max-w-xl lg:max-w-2xl">
          <AlertDialogHeader>
            <AlertDialogTitle>Perubahan Belum Tersimpan</AlertDialogTitle>
            <AlertDialogDescription>
              Anda memiliki perubahan yang belum tersimpan. Jika Anda
              meninggalkan halaman ini, perubahan Anda akan hilang.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex flex-col space-y-2 md:flex-row md:space-y-0 md:space-x-2 md:justify-end">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-2 w-full">
              <AlertDialogCancel
                onClick={cancelNavigation}
                className="cursor-pointer w-full"
              >
                Kembali ke Form
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={confirmNavigation}
                className="cursor-pointer bg-red-500 hover:bg-red-600 w-full"
              >
                Buang Perubahan
              </AlertDialogAction>
              <Button
                type="button"
                variant="default"
                className="cursor-pointer w-full"
                onClick={() => {
                  saveAsDraft();
                  setShowExitDialog(false);
                }}
              >
                <Save className="h-4 w-4 mr-2" />
                Simpan ke Draft
              </Button>
            </div>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </DashboardLayout>
  );
};

export default EnhancedEditProductPage;
