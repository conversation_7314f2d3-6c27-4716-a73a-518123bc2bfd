"use client";

import React, { useEffect, useState } from "react";
import { Control } from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { ProductFormValues } from "../types";
import { getCategories } from "@/actions/entities/categories";
import { getUnits } from "@/actions/entities/units";
import { toast } from "sonner";
import CategoryCombobox from "./CategoryCombobox";
import UnitCombobox from "./UnitCombobox";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Plus,
  X,
  ShoppingBag,
  Barcode,
  FileText,
  Tag,
  Hash,
  Tags,
  Box,
} from "lucide-react";

interface ProductBasicInfoProps {
  control: Control<ProductFormValues>;
  isPending: boolean;
}

const ProductBasicInfo: React.FC<ProductBasicInfoProps> = ({
  control,
  isPending,
}) => {
  // State for categories
  const [categories, setCategories] = useState<{ id: string; name: string }[]>(
    []
  );
  const [isLoadingCategories, setIsLoadingCategories] = useState(false);

  // State for units
  const [units, setUnits] = useState<{ id: string; name: string }[]>([]);
  const [isLoadingUnits, setIsLoadingUnits] = useState(false);

  // State for tags input
  const [tagInput, setTagInput] = useState("");
  const [tags, setTags] = useState<string[]>([]);

  // Initialize tags from form values
  React.useEffect(() => {
    if (control._formValues.tags && Array.isArray(control._formValues.tags)) {
      setTags(control._formValues.tags);
    }
  }, [control._formValues.tags]);

  // Fetch categories function
  const fetchCategories = async () => {
    setIsLoadingCategories(true);
    try {
      const result = await getCategories();
      if (result.error) {
        toast.error(result.error);
        return;
      }
      if (result.categories) {
        setCategories(result.categories);
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
      toast.error("Gagal mengambil data kategori");
    } finally {
      setIsLoadingCategories(false);
    }
  };

  // Fetch units function
  const fetchUnits = async () => {
    setIsLoadingUnits(true);
    try {
      const result = await getUnits();
      if (result.error) {
        toast.error(result.error);
        return;
      }
      if (result.units) {
        setUnits(result.units);
      }
    } catch (error) {
      console.error("Error fetching units:", error);
      toast.error("Gagal mengambil data satuan");
    } finally {
      setIsLoadingUnits(false);
    }
  };

  // Fetch categories and units on component mount
  useEffect(() => {
    fetchCategories();
    fetchUnits();
  }, []);

  // Handle category added
  const handleCategoryAdded = (category: { id: string; name: string }) => {
    setCategories((prev) => [...prev, category]);
  };

  // Handle unit added
  const handleUnitAdded = (unit: { id: string; name: string }) => {
    setUnits((prev) => [...prev, unit]);
  };

  // Handle units updated (for management dialog)
  const handleUnitsUpdated = () => {
    fetchUnits();
  };

  // Handle categories updated (for management dialog)
  const handleCategoriesUpdated = () => {
    fetchCategories();
  };

  // Handle adding a new tag
  const handleAddTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      // Check if maximum tags limit (3) is reached
      if (tags.length >= 3) {
        toast.error("Maksimal hanya 3 tag yang diperbolehkan");
        return;
      }

      const newTags = [...tags, tagInput.trim()];
      setTags(newTags);
      setTagInput("");

      // Update form value
      control._formValues.tags = newTags;
    }
  };

  // Handle removing a tag
  const handleRemoveTag = (tag: string) => {
    const newTags = tags.filter((t) => t !== tag);
    setTags(newTags);

    // Update form value
    control._formValues.tags = newTags;
  };

  // Handle key press in tag input
  const handleTagKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" || e.key === ",") {
      e.preventDefault();
      handleAddTag();
    }
  };
  return (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
      {/* Row 1: Nama Produk and SKU */}
      <div className="md:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Nama Produk */}
        <FormField
          control={control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-1.5">
                <ShoppingBag className="h-4 w-4 text-primary" />
                Nama Produk <span className="text-red-500 font-bold">*</span>
              </FormLabel>
              <FormControl className="form-control">
                <Input
                  placeholder="Masukkan nama produk"
                  {...field}
                  name="name"
                  disabled={isPending}
                  className="pr-4 h-12 md:text-lg font-semibold border-gray-200 dark:border-gray-600 focus:border-green-500 focus:ring-green-500/20 transition-all duration-200 bg-white dark:bg-gray-800"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* SKU */}
        <FormField
          control={control}
          name="sku"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-1.5">
                <Hash className="h-4 w-4 text-blue-500" />
                Kode Produk / SKU
              </FormLabel>
              <FormControl>
                <Input
                  placeholder="Contoh: BRG001"
                  {...field}
                  disabled={isPending}
                  className="pr-4 h-12 md:text-lg font-semibold border-gray-200 dark:border-gray-600 focus:border-green-500 focus:ring-green-500/20 transition-all duration-200 bg-white dark:bg-gray-800"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Row 2: Barcode and Unit */}
      <div className="md:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Barcode */}
        <FormField
          control={control}
          name="barcode"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-1.5">
                <Barcode className="h-4 w-4 text-orange-500" />
                Barcode{" "}
                <span className="text-xs text-gray-500">(Opsional)</span>
              </FormLabel>
              <FormControl>
                <Input
                  placeholder="Contoh: 8992761123456"
                  {...field}
                  disabled={isPending}
                  className="pr-4 h-12 md:text-lg font-semibold border-gray-200 dark:border-gray-600 focus:border-green-500 focus:ring-green-500/20 transition-all duration-200 bg-white dark:bg-gray-800"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Unit */}
        <FormField
          control={control}
          name="unitId"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-1.5">
                <Box className="h-4 w-4 text-teal-500" />
                Satuan
              </FormLabel>
              <UnitCombobox
                units={units}
                isLoadingUnits={isLoadingUnits}
                onUnitAdded={handleUnitAdded}
                onUnitsUpdated={handleUnitsUpdated}
                field={field}
                isPending={isPending}
              />
              <FormDescription></FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Row 3: Kategori produk and Tag produk */}
      <div className="md:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Categories Section - Combined combobox */}
        <FormField
          control={control}
          name="categoryId"
          render={({ field }) => (
            <FormItem data-field="categoryId">
              <FormLabel className="flex items-center gap-1.5">
                <Tag className="h-4 w-4 text-purple-500" />
                Kategori Produk{" "}
                <span className="text-red-500 font-bold">*</span>{" "}
              </FormLabel>
              <CategoryCombobox
                categories={categories}
                isLoadingCategories={isLoadingCategories}
                onCategoryAdded={handleCategoryAdded}
                onCategoriesUpdated={handleCategoriesUpdated}
                field={field}
                isPending={isPending}
              />
              <FormDescription></FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Tags Section */}
        <FormItem>
          <FormLabel className="flex items-center gap-1.5">
            <Tags className="h-4 w-4 text-amber-500" />
            Tag Produk {tags.length > 0 && `(${tags.length}/3)`}
            {tags.length >= 3 && (
              <span className="text-xs text-muted-foreground">
                (Maksimal 3 tag)
              </span>
            )}
          </FormLabel>
          <div className="space-y-4">
            <div className="flex gap-2">
              <Input
                placeholder={
                  tags.length >= 3
                    ? "Maksimal 3 tag tercapai"
                    : "Tambahkan tag (tekan Enter)"
                }
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                onKeyDown={handleTagKeyPress}
                disabled={isPending || tags.length >= 3}
                className="flex-1 pr-4 h-12 md:text-lg font-semibold border-gray-200 dark:border-gray-600 focus:border-green-500 focus:ring-green-500/20 transition-all duration-200 bg-white dark:bg-gray-800"
              />
              <Button
                className="cursor-pointer h-12"
                type="button"
                onClick={handleAddTag}
                disabled={isPending || !tagInput.trim() || tags.length >= 3}
              >
                <Plus className="h-4 w-4 mr-2" />
                Tambah
              </Button>
            </div>

            <div className="flex flex-wrap gap-2">
              {tags.length > 0 ? (
                tags.map((tag) => (
                  <Badge
                    key={tag}
                    variant="secondary"
                    className="flex items-center gap-1 bg-gray-100 dark:bg-gray-700 shadow-sm"
                  >
                    {tag}
                    <button
                      type="button"
                      onClick={() => handleRemoveTag(tag)}
                      className="ml-1 hover:text-destructive cursor-pointer"
                      disabled={isPending}
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))
              ) : (
                <div className="text-sm text-muted-foreground"></div>
              )}
            </div>
          </div>
        </FormItem>
      </div>

      {/* Deskripsi */}
      <FormField
        control={control}
        name="description"
        render={({ field }) => (
          <FormItem className="md:col-span-2">
            <FormLabel className="flex items-center gap-1.5">
              <FileText className="h-4 w-4 text-green-500" />
              Deskripsi{" "}
              <span className="text-xs text-gray-500">(Opsional)</span>
            </FormLabel>
            <FormControl>
              <Textarea
                placeholder="Deskripsi singkat tentang produk..."
                className="resize-y min-h-[120px]"
                maxLength={3000}
                {...field}
                onChange={(e) => {
                  // Limit to 3000 characters
                  if (e.target.value.length <= 3000) {
                    field.onChange(e);
                  }
                }}
                disabled={isPending}
              />
            </FormControl>
            <div className="flex justify-end">
              <p className="text-xs text-muted-foreground mt-1">
                {field.value ? field.value.length : 0}/3000 karakter
              </p>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};

export default ProductBasicInfo;
