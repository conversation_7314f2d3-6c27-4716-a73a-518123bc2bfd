"use client";

import * as React from "react";
import { Check, ChevronsUpDown, Plus } from "lucide-react";
import { toast } from "sonner";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { addUnit } from "@/actions/entities/units";
import { FormControl } from "@/components/ui/form";
import { ControllerRenderProps } from "react-hook-form";
import UnitManagementDialog from "./UnitManagementDialog";

interface UnitComboboxProps {
  units: { id: string; name: string }[];
  isLoadingUnits: boolean;
  onUnitAdded: (unit: { id: string; name: string }) => void;
  onUnitsUpdated: () => void;
  field: ControllerRenderProps<any, "unitId">;
  isPending: boolean;
}

const UnitCombobox: React.FC<UnitComboboxProps> = ({
  units,
  isLoadingUnits,
  onUnitAdded,
  onUnitsUpdated,
  field,
  isPending,
}) => {
  const [open, setOpen] = React.useState(false);
  const [inputValue, setInputValue] = React.useState("");
  const [isAddingUnit, setIsAddingUnit] = React.useState(false);
  const [showAllUnits, setShowAllUnits] = React.useState(false);

  const handleAddUnit = async () => {
    if (!inputValue.trim()) {
      toast.error("Nama satuan tidak boleh kosong");
      return;
    }

    // Check for duplicate unit in current user's units
    const trimmedInput = inputValue.trim();
    const existingUnit = units.find(
      (unit) => unit.name.toLowerCase() === trimmedInput.toLowerCase()
    );

    if (existingUnit) {
      toast.error(
        `Satuan "${trimmedInput}" sudah ada! Silakan pilih dari daftar atau gunakan nama yang berbeda.`
      );
      return;
    }

    setIsAddingUnit(true);
    try {
      const result = await addUnit({ name: trimmedInput });

      if (result.error) {
        toast.error(result.error);
        return;
      }

      if (result.success && result.unit) {
        toast.success(result.success);
        onUnitAdded(result.unit);
        field.onChange(result.unit.id);

        // Emit an event to notify parent component about the selected unit
        if (typeof window !== "undefined") {
          // Create a custom event with the selected unit data
          const event = new CustomEvent("unitSelected", {
            detail: { id: result.unit.id, name: result.unit.name },
          });
          window.dispatchEvent(event);
        }

        setOpen(false);
        setInputValue("");
      }
    } catch (error) {
      toast.error("Terjadi kesalahan saat menambahkan satuan");
      console.error(error);
    } finally {
      setIsAddingUnit(false);
    }
  };

  const handleInputChange = (value: string) => {
    setInputValue(value);
    // Show all units when searching, otherwise show only recent ones
    setShowAllUnits(value.trim().length > 0);
  };

  const handleSelect = (currentValue: string) => {
    // Always set the selected value without toggling
    field.onChange(currentValue);

    // Find the selected unit name to update the unit field in the parent form
    const selectedUnit = units.find((unit) => unit.id === currentValue);
    if (selectedUnit) {
      // Emit an event to notify parent component about the selected unit
      if (typeof window !== "undefined") {
        // Create a custom event with the selected unit data
        const event = new CustomEvent("unitSelected", {
          detail: { id: selectedUnit.id, name: selectedUnit.name },
        });
        window.dispatchEvent(event);
      }
    }

    setOpen(false);
    setInputValue("");
  };

  return (
    <Popover
      open={open}
      onOpenChange={(isOpen) => {
        setOpen(isOpen);
        // Clear input value and reset view when closing the popover
        if (!isOpen) {
          setInputValue("");
          setShowAllUnits(false);
        }
      }}
    >
      <PopoverTrigger asChild>
        <FormControl>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn(
              "w-full justify-between cursor-pointer h-12 border-gray-200 dark:border-gray-600 focus:border-green-500 focus:ring-green-500/20 transition-all duration-200 bg-white dark:bg-gray-800",
              !field.value && "text-muted-foreground"
            )}
            disabled={isPending || isLoadingUnits}
            onClick={() => setOpen(!open)}
            type="button"
          >
            {field.value
              ? units.find((unit) => unit.id === field.value)?.name ||
                "Satuan terpilih"
              : "Pilih satuan atau ketik untuk tambah baru"}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </FormControl>
      </PopoverTrigger>
      <PopoverContent className="p-0 w-[var(--radix-popover-trigger-width)]">
        <div className="max-h-[200px] overflow-auto">
          {/* Management Link */}
          <div className="p-2 border-b bg-muted/30">
            <UnitManagementDialog onUnitsUpdated={onUnitsUpdated} />
          </div>

          <div className="p-2 border-b">
            <input
              className="w-full px-2 py-1 text-sm border rounded focus:outline-none focus:ring-1 focus:ring-primary"
              placeholder="Cari atau ketik untuk tambah satuan..."
              value={inputValue}
              onChange={(e) => handleInputChange(e.target.value)}
            />
          </div>

          {/* Empty state */}
          {units.length === 0 && !inputValue && (
            <div className="p-2 text-sm text-center text-muted-foreground">
              Belum ada satuan
            </div>
          )}

          {/* Create New Unit Button - Always show when there's input */}
          {inputValue && (
            <div className="flex flex-col gap-2 p-2 border-b">
              <Button
                type="button"
                size="sm"
                className="cursor-pointer w-full"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleAddUnit();
                }}
                disabled={isAddingUnit}
              >
                <Plus className="mr-2 h-4 w-4" />
                {isAddingUnit ? "Menambahkan..." : `Tambah "${inputValue}"`}
              </Button>
            </div>
          )}

          {/* Unit list */}
          <div className="py-1">
            {/* Filter units based on search input and limit to 3 if not searching */}
            {units
              .filter(
                (unit) => showAllUnits || inputValue || units.indexOf(unit) < 3
              )
              .filter(
                (unit) =>
                  !inputValue ||
                  unit.name.toLowerCase().includes(inputValue.toLowerCase())
              )
              .map((unit) => (
                <div
                  key={unit.id}
                  className="flex items-center px-2 py-1.5 text-sm hover:bg-accent hover:text-accent-foreground cursor-pointer"
                  onClick={() => {
                    handleSelect(unit.id);
                  }}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      field.value === unit.id ? "opacity-100" : "opacity-0"
                    )}
                  />
                  {unit.name}
                </div>
              ))}

            {/* Show "View All" button if there are more than 3 units and not already showing all */}
            {!showAllUnits && !inputValue && units.length > 3 && (
              <div
                className="flex justify-center items-center py-2 text-sm text-primary hover:underline cursor-pointer"
                onClick={() => setShowAllUnits(true)}
              >
                Lihat Semua Satuan ({units.length})
              </div>
            )}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default UnitCombobox;
