"use client";

import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";
import {
  Edit,
  Trash2,
  Check,
  X,
  Settings,
  Search,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { getUnits, updateUnit, deleteUnit } from "@/actions/entities/units";

const UnitSchema = z.object({
  name: z.string().min(1, "Nama satuan harus diisi"),
});

type UnitFormValues = z.infer<typeof UnitSchema>;

interface Unit {
  id: string;
  name: string;
}

interface UnitManagementDialogProps {
  onUnitsUpdated: () => void;
}

const UnitManagementDialog: React.FC<UnitManagementDialogProps> = ({
  onUnitsUpdated,
}) => {
  const [open, setOpen] = useState(false);
  const [units, setUnits] = useState<Unit[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [unitToDelete, setUnitToDelete] = useState<Unit | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Search and pagination state
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;

  const form = useForm<UnitFormValues>({
    resolver: zodResolver(UnitSchema),
    defaultValues: {
      name: "",
    },
  });

  // Fetch units when dialog opens
  const fetchUnits = async () => {
    setIsLoading(true);
    try {
      const result = await getUnits();
      if (result.error) {
        toast.error(result.error);
        return;
      }
      if (result.units) {
        setUnits(result.units);
      }
    } catch (error) {
      console.error("Error fetching units:", error);
      toast.error("Gagal mengambil data satuan");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (open) {
      fetchUnits();
      setSearchQuery("");
      setCurrentPage(1);
    }
  }, [open]);

  // Filter units based on search query
  const filteredUnits = units.filter((unit) =>
    unit.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Calculate pagination
  const totalPages = Math.ceil(filteredUnits.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedUnits = filteredUnits.slice(startIndex, endIndex);

  // Reset to first page when search changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery]);

  // Handle edit start
  const handleEditStart = (unit: Unit) => {
    setEditingId(unit.id);
    form.setValue("name", unit.name);
  };

  // Handle edit cancel
  const handleEditCancel = () => {
    setEditingId(null);
    form.reset();
  };

  // Handle edit submit
  const handleEditSubmit = async (unitId: string) => {
    const values = form.getValues();

    try {
      const result = await updateUnit(unitId, values);

      if (result.error) {
        toast.error(result.error);
        return;
      }

      if (result.success && result.unit) {
        toast.success(result.success);
        setUnits((prev) =>
          prev.map((unit) =>
            unit.id === unitId ? { ...unit, name: result.unit!.name } : unit
          )
        );
        setEditingId(null);
        form.reset();
        onUnitsUpdated();
      }
    } catch (error) {
      toast.error("Terjadi kesalahan saat memperbarui satuan");
      console.error(error);
    }
  };

  // Handle delete confirmation
  const handleDeleteClick = (unit: Unit) => {
    setUnitToDelete(unit);
    setDeleteDialogOpen(true);
  };

  // Handle delete confirm
  const handleDeleteConfirm = async () => {
    if (!unitToDelete) return;

    setIsDeleting(true);
    try {
      const result = await deleteUnit(unitToDelete.id);

      if (result.error) {
        toast.error(result.error);
        return;
      }

      if (result.success) {
        toast.success(result.success);
        setUnits((prev) => prev.filter((unit) => unit.id !== unitToDelete.id));
        onUnitsUpdated();
      }
    } catch (error) {
      toast.error("Terjadi kesalahan saat menghapus satuan");
      console.error(error);
    } finally {
      setIsDeleting(false);
      setDeleteDialogOpen(false);
      setUnitToDelete(null);
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>
          <button
            type="button"
            className="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium flex items-center gap-1 px-2 py-1 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded transition-colors cursor-pointer"
          >
            <Settings className="h-3 w-3" />
            Kelola Satuan
          </button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle>Kelola Satuan</DialogTitle>
            <DialogDescription>
              Edit atau hapus satuan yang sudah ada. Satuan yang digunakan dalam
              produk tidak dapat dihapus.
            </DialogDescription>
          </DialogHeader>

          {/* Search Bar */}
          <div className="px-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <input
                type="text"
                placeholder="Cari satuan..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-input rounded-md bg-background text-sm focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
              />
            </div>
          </div>

          <div className="flex-1 overflow-y-auto min-h-[300px]">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : filteredUnits.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                {searchQuery
                  ? `Tidak ada satuan yang cocok dengan "${searchQuery}"`
                  : "Belum ada satuan yang dibuat"}
              </div>
            ) : (
              <div className="space-y-2 px-1">
                {paginatedUnits.map((unit) => (
                  <div
                    key={unit.id}
                    className="flex items-center gap-2 p-3 border rounded-lg hover:bg-muted/50 transition-colors"
                  >
                    {editingId === unit.id ? (
                      <Form {...form}>
                        <div className="flex items-center gap-2 flex-1">
                          <FormField
                            control={form.control}
                            name="name"
                            render={({ field }) => (
                              <FormItem className="flex-1">
                                <FormControl>
                                  <Input
                                    {...field}
                                    className="h-8"
                                    autoFocus
                                    onKeyDown={(e) => {
                                      if (e.key === "Enter") {
                                        e.preventDefault();
                                        handleEditSubmit(unit.id);
                                      } else if (e.key === "Escape") {
                                        handleEditCancel();
                                      }
                                    }}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <Button
                            type="button"
                            size="sm"
                            variant="ghost"
                            className="h-8 w-8 p-0 text-green-600 hover:text-green-700 hover:bg-green-50"
                            onClick={() => handleEditSubmit(unit.id)}
                          >
                            <Check className="h-4 w-4" />
                          </Button>
                          <Button
                            type="button"
                            size="sm"
                            variant="ghost"
                            className="h-8 w-8 p-0 text-gray-600 hover:text-gray-700 hover:bg-gray-50"
                            onClick={handleEditCancel}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      </Form>
                    ) : (
                      <>
                        <span className="flex-1 font-medium">{unit.name}</span>
                        <Button
                          type="button"
                          size="sm"
                          variant="ghost"
                          className="h-8 w-8 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                          onClick={() => handleEditStart(unit)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          type="button"
                          size="sm"
                          variant="ghost"
                          className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                          onClick={() => handleDeleteClick(unit)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Pagination Controls */}
          {!isLoading && filteredUnits.length > itemsPerPage && (
            <div className="flex items-center justify-between px-1 py-2 border-t">
              <div className="text-sm text-muted-foreground">
                Menampilkan {startIndex + 1}-
                {Math.min(endIndex, filteredUnits.length)} dari{" "}
                {filteredUnits.length} satuan
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setCurrentPage((prev) => Math.max(prev - 1, 1))
                  }
                  disabled={currentPage === 1}
                  className="h-8 w-8 p-0"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <span className="text-sm font-medium">
                  {currentPage} / {totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                  }
                  disabled={currentPage === totalPages}
                  className="h-8 w-8 p-0"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Konfirmasi Hapus</AlertDialogTitle>
            <AlertDialogDescription>
              Apakah Anda yakin ingin menghapus satuan{" "}
              <span className="font-semibold">{unitToDelete?.name}</span>?{" "}
              Tindakan ini tidak dapat dibatalkan.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Batal</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isDeleting ? "Menghapus..." : "Hapus"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default UnitManagementDialog;
