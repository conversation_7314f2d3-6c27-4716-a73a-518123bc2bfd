"use client";

import React, { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Card } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import {
  Download,
  Upload,
  CheckCircle,
  Info,
  Calendar,
  Settings,
  FileSpreadsheet,
} from "lucide-react";
import * as XLSX from "xlsx-js-style";
import { toast } from "sonner";
import { createPurchaseImportTemplate } from "@/utils/imports/purchasesImport";
import { importPurchases } from "@/actions/import/purchases";
import { getPurchaseReportDataWithFilters } from "@/actions/reports";
import {
  createPurchasesExcelReport,
  createPurchasesImportFriendlyExcelReport,
} from "@/utils/exports/purchasesExport";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import {
  generateDateStringForFilename,
  validateExportPeriod,
} from "@/utils/dateUtils";

interface ImportSummary {
  purchasesCreated: number;
  suppliersCreated: number;
  warehousesCreated: number;
  errors: string[];
}

interface ExportConfig {
  reportType: "harian" | "bulanan" | "tahunan";
  selectedDate: Date;
  selectedMonth: number;
  selectedYear: number;
  includeSummary: boolean;
  includeCharts: boolean;
  importFriendly: boolean; // New option for import-friendly format
}

interface PurchaseImportExportProps {
  onRefresh?: () => void;
}

export const PurchaseImportExport: React.FC<PurchaseImportExportProps> = ({
  onRefresh,
}) => {
  // Import states
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [importSummary, setImportSummary] = useState<ImportSummary | null>(
    null
  );
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Export states
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [exportConfig, setExportConfig] = useState<ExportConfig>({
    reportType: "bulanan",
    selectedDate: new Date(),
    selectedMonth: new Date().getMonth(),
    selectedYear: new Date().getFullYear(),
    includeSummary: false,
    includeCharts: false,
    importFriendly: false, // Default to regular export
  });

  // Download template function
  const downloadTemplate = () => {
    try {
      const workbook = createPurchaseImportTemplate();
      const fileName = `template-import-pembelian-${new Date().toISOString().split("T")[0]}.xlsx`;
      XLSX.writeFile(workbook, fileName);
      toast.success("Template berhasil diunduh!");
    } catch (error) {
      console.error("Template download error:", error);
      toast.error("Gagal mengunduh template");
    }
  };

  // Handle import function
  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.name.match(/\.(xlsx|xls)$/)) {
      toast.error(
        "Format file tidak didukung. Gunakan file Excel (.xlsx atau .xls)"
      );
      return;
    }

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      toast.error("Ukuran file terlalu besar. Maksimal 10MB");
      return;
    }

    setIsImporting(true);
    setImportProgress(0);
    setImportSummary(null);

    try {
      setImportProgress(20);

      const arrayBuffer = await file.arrayBuffer();
      setImportProgress(40);

      const result = await importPurchases(arrayBuffer);
      setImportProgress(80);

      if (result.success) {
        setImportProgress(100);
        setImportSummary(result.summary || null);
        toast.success(
          result.success + " Lihat notifikasi untuk detail lengkap."
        );

        // Auto-refresh data on successful import to show newly imported items
        if (onRefresh) {
          setTimeout(() => {
            onRefresh();
          }, 1500); // Small delay to ensure notifications are processed
        }
      } else if (result.error) {
        setImportSummary(result.summary || null);

        // Show detailed error message
        let errorMessage = result.error;
        if (result.summary?.errors && result.summary.errors.length > 0) {
          const firstError = result.summary.errors[0];
          if (firstError.includes("Unique constraint failed")) {
            errorMessage =
              "Terjadi konflik data saat import. Beberapa data mungkin sudah ada di sistem.";
          } else if (
            firstError.includes("Produk") &&
            firstError.includes("tidak ditemukan")
          ) {
            errorMessage =
              "Beberapa produk dalam file tidak ditemukan di sistem. Pastikan semua produk sudah dibuat terlebih dahulu.";
          }
        }

        toast.error(errorMessage);
      }
    } catch (error) {
      console.error("Import error:", error);

      // Show more specific error messages
      let errorMessage = "Gagal mengimpor file";
      if (error instanceof Error) {
        if (error.message.includes("Unique constraint")) {
          errorMessage =
            "Terjadi konflik data saat import. Beberapa data mungkin sudah ada di sistem.";
        } else if (error.message.includes("timeout")) {
          errorMessage =
            "Import timeout. File terlalu besar atau koneksi lambat. Coba dengan file yang lebih kecil.";
        } else if (error.message.includes("authentication")) {
          errorMessage = "Sesi Anda telah berakhir. Silakan login kembali.";
        }
      }

      toast.error(errorMessage);

      // Set error summary for display
      setImportSummary({
        purchasesCreated: 0,
        suppliersCreated: 0,
        warehousesCreated: 0,
        errors: [errorMessage],
      });
    } finally {
      setIsImporting(false);
      setImportProgress(0);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  // Handle advanced export function
  const handleAdvancedExport = async () => {
    setIsExporting(true);
    setExportProgress(0);

    try {
      // Validate export period
      const validationError = validateExportPeriod(exportConfig.reportType, {
        selectedDate: exportConfig.selectedDate,
        selectedMonth: exportConfig.selectedMonth,
        selectedYear: exportConfig.selectedYear,
      });

      if (validationError) {
        toast.error(validationError);
        return;
      }

      setExportProgress(20);

      // Determine date range and period label
      let startDate: Date | undefined;
      let endDate: Date | undefined;
      let dateRange: string = "monthly";
      let periodLabel: string = "Data Pembelian";

      if (exportConfig.reportType === "harian") {
        startDate = new Date(exportConfig.selectedDate);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(exportConfig.selectedDate);
        endDate.setHours(23, 59, 59, 999);
        dateRange = "daily";
        periodLabel = `Harian - ${startDate.toLocaleDateString("id-ID")}`;
      } else if (exportConfig.reportType === "bulanan") {
        const year = exportConfig.selectedYear || new Date().getFullYear();
        const month = exportConfig.selectedMonth || new Date().getMonth();
        startDate = new Date(year, month, 1);
        endDate = new Date(year, month + 1, 0, 23, 59, 59, 999);
        dateRange = "monthly";
        periodLabel = `Bulanan - ${startDate.toLocaleDateString("id-ID", { month: "long", year: "numeric" })}`;
      } else if (exportConfig.reportType === "tahunan") {
        const year = exportConfig.selectedYear || new Date().getFullYear();
        startDate = new Date(year, 0, 1);
        endDate = new Date(year, 11, 31, 23, 59, 59, 999);
        dateRange = "yearly";
        periodLabel = `Tahunan - ${year}`;
      }

      setExportProgress(40);

      // Fetch purchase data with date filtering
      const purchaseResult = await getPurchaseReportDataWithFilters({
        dateRange,
        startDate,
        endDate,
        supplier: undefined,
      });

      setExportProgress(70);

      if (purchaseResult.error) {
        console.error("Purchase data fetch error:", purchaseResult.error);
        throw new Error(purchaseResult.error);
      }

      // Check if we have data
      if (!purchaseResult.data || purchaseResult.data.length === 0) {
        toast.error(
          "Tidak ada data pembelian untuk diekspor pada periode yang dipilih"
        );
        return;
      }

      setExportProgress(85);

      // Generate Excel report based on format selection
      const workbook = exportConfig.importFriendly
        ? createPurchasesImportFriendlyExcelReport(purchaseResult.data)
        : createPurchasesExcelReport(purchaseResult.data, periodLabel, {
            companyName: "KivaPOS",
            reportTitle: "Laporan Pembelian",
          });

      // Convert workbook to buffer
      const excelBuffer = XLSX.write(workbook, {
        type: "array",
        bookType: "xlsx",
      });

      setExportProgress(100);

      // Generate filename based on selected date range
      const dateString = generateDateStringForFilename(
        exportConfig.reportType,
        {
          selectedDate: exportConfig.selectedDate,
          selectedMonth: exportConfig.selectedMonth,
          selectedYear: exportConfig.selectedYear,
        }
      );

      // Download the file
      const fileName = exportConfig.importFriendly
        ? `data-pembelian-import-${dateString}.xlsx`
        : `laporan-pembelian-${exportConfig.reportType}-${dateString}.xlsx`;
      const blob = new Blob([excelBuffer], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast.success("Export berhasil!");
      setShowExportDialog(false);
    } catch (error) {
      console.error("Export error:", error);
      toast.error("Gagal mengekspor data");
    } finally {
      setIsExporting(false);
      setExportProgress(0);
    }
  };

  return (
    <div className="flex items-center gap-2">
      {/* Import Button and Dialog */}
      <Dialog open={showImportDialog} onOpenChange={setShowImportDialog}>
        <DialogTrigger asChild>
          <Button
            variant="outline"
            className="flex items-center gap-2 cursor-pointer"
          >
            <Upload className="h-4 w-4" />
            Import
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-2xl max-h-[80vh] flex flex-col rounded-md">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Import Data Pembelian
            </DialogTitle>
            <DialogDescription className="flex text-left">
              Import data pembelian dari file Excel dengan mudah dan aman
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto space-y-4 p-2">
            {/* Instructions */}
            <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg">
              <div className="flex items-start gap-3">
                <Info className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div className="space-y-2">
                  <h4 className="font-medium text-blue-900 dark:text-blue-100">
                    Cara Import Data Pembelian:
                  </h4>
                  <ol className="text-sm text-blue-800 dark:text-blue-200 space-y-1 list-decimal list-inside">
                    <li>
                      Download template Excel dengan klik tombol &quot;Download
                      Template&quot;
                    </li>
                    <li>Isi data pembelian sesuai format yang tersedia</li>
                    <li>Upload file Excel yang sudah diisi</li>
                    <li>Tunggu proses import selesai</li>
                  </ol>
                </div>
              </div>
            </div>

            {/* Download Template */}
            <div className="space-y-2">
              <h4 className="font-medium">1. Download Template Excel</h4>
              <Button
                onClick={downloadTemplate}
                variant="outline"
                className="w-full cursor-pointer"
              >
                <Download className="mr-2 h-4 w-4" />
                Download Template Pembelian
              </Button>
            </div>

            <Separator />

            {/* File Upload */}
            <div className="space-y-2">
              <h4 className="font-medium">2. Upload File Excel</h4>
              <div className="border-2 border-dashed border-slate-300 rounded-lg p-6 text-center">
                <Upload className="h-12 w-12 mx-auto text-slate-400 mb-4" />
                <p className="text-sm text-slate-600 mb-2">
                  Klik untuk memilih file atau drag & drop
                </p>
                <p className="text-xs text-slate-500">
                  Format: .xlsx, .xls (Maksimal 10MB)
                </p>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".xlsx,.xls"
                  onChange={handleImport}
                  className="hidden"
                />
                <Button
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                  className="mt-4 cursor-pointer"
                  disabled={isImporting}
                >
                  {isImporting ? "Memproses..." : "Pilih File Excel"}
                </Button>
              </div>
            </div>

            {/* Import Progress */}
            {isImporting && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progress Import</span>
                  <span>{importProgress}%</span>
                </div>
                <Progress value={importProgress} className="w-full" />
              </div>
            )}

            {/* Import Summary */}
            {importSummary && (
              <div className="space-y-3">
                <h4 className="font-medium">Hasil Import:</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm font-medium">
                        Pembelian Dibuat
                      </span>
                    </div>
                    <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                      {importSummary.purchasesCreated}
                    </div>
                  </div>
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-blue-500" />
                      <span className="text-sm font-medium">Supplier Baru</span>
                    </div>
                    <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                      {importSummary.suppliersCreated}
                    </div>
                  </div>
                </div>

                {importSummary.errors.length > 0 && (
                  <div className="bg-red-50 dark:bg-red-900/20 p-3 rounded-lg">
                    <h5 className="font-medium text-red-800 dark:text-red-200 mb-2">
                      Error ({importSummary.errors.length}):
                    </h5>
                    <div className="max-h-32 overflow-y-auto space-y-1">
                      {importSummary.errors.slice(0, 5).map((error, index) => (
                        <div
                          key={index}
                          className="text-sm text-red-700 dark:text-red-300"
                        >
                          • {error}
                        </div>
                      ))}
                      {importSummary.errors.length > 5 && (
                        <div className="text-sm text-red-600 dark:text-red-400">
                          ... dan {importSummary.errors.length - 5} error
                          lainnya
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Export Button and Dialog */}
      <Dialog open={showExportDialog} onOpenChange={setShowExportDialog}>
        <DialogTrigger asChild>
          <Button
            variant="outline"
            className="flex items-center gap-2 cursor-pointer"
          >
            <Download className="h-4 w-4" />
            Export
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-3xl max-h-[80vh] flex flex-col rounded-md">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Export Data Pembelian
            </DialogTitle>
            <DialogDescription className="flex text-left">
              Pilih periode dan format yang ingin diekspor untuk data pembelian
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto space-y-4 p-2">
            {/* Period Selection */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">Periode Laporan</Label>
              <div className="grid grid-cols-3 gap-2">
                {[
                  { value: "harian", label: "Harian", icon: Calendar },
                  { value: "bulanan", label: "Bulanan", icon: Calendar },
                  { value: "tahunan", label: "Tahunan", icon: Calendar },
                ].map((type) => (
                  <Card
                    key={type.value}
                    className={`cursor-pointer transition-all hover:shadow-sm ${
                      exportConfig.reportType === type.value
                        ? "ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950"
                        : "hover:bg-gray-50 dark:hover:bg-gray-800"
                    }`}
                    onClick={() =>
                      setExportConfig((prev) => ({
                        ...prev,
                        reportType: type.value as any,
                      }))
                    }
                  >
                    <div className="p-3 text-center">
                      <type.icon className="h-5 w-5 mx-auto mb-1" />
                      <div className="text-sm font-medium">{type.label}</div>
                    </div>
                  </Card>
                ))}
              </div>
            </div>

            {/* Date/Period Selection */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Pilih Periode</Label>

              {exportConfig.reportType === "harian" && (
                <div>
                  <DatePicker
                    date={exportConfig.selectedDate}
                    setDate={(date) => {
                      setExportConfig((prev) => ({
                        ...prev,
                        selectedDate: date || new Date(),
                      }));
                    }}
                    placeholder="Pilih tanggal"
                    className="w-full h-9 cursor-pointer"
                  />
                </div>
              )}

              {exportConfig.reportType === "bulanan" && (
                <div className="grid grid-cols-2 gap-2">
                  <Select
                    value={exportConfig.selectedMonth?.toString() || ""}
                    onValueChange={(value) => {
                      setExportConfig((prev) => ({
                        ...prev,
                        selectedMonth: parseInt(value),
                      }));
                    }}
                  >
                    <SelectTrigger className="h-9 w-full">
                      <SelectValue placeholder="Bulan" />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.from({ length: 12 }, (_, i) => (
                        <SelectItem key={i} value={i.toString()}>
                          {new Date(0, i).toLocaleDateString("id-ID", {
                            month: "long",
                          })}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Input
                    type="number"
                    min="2020"
                    max="2030"
                    placeholder="Tahun"
                    value={exportConfig.selectedYear || ""}
                    onChange={(e) => {
                      setExportConfig((prev) => ({
                        ...prev,
                        selectedYear:
                          parseInt(e.target.value) || new Date().getFullYear(),
                      }));
                    }}
                    className="h-11"
                  />
                </div>
              )}

              {exportConfig.reportType === "tahunan" && (
                <div>
                  <Input
                    type="number"
                    min="2020"
                    max="2030"
                    placeholder="Tahun"
                    value={exportConfig.selectedYear || ""}
                    onChange={(e) => {
                      setExportConfig((prev) => ({
                        ...prev,
                        selectedYear:
                          parseInt(e.target.value) || new Date().getFullYear(),
                      }));
                    }}
                    className="w-full h-9"
                  />
                </div>
              )}
            </div>

            <Separator />

            {/* Format Selection - Excel Only */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Format Export</Label>
              <div className="flex items-center gap-2 p-3 border rounded-md bg-gray-50 dark:bg-gray-800">
                <FileSpreadsheet className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium">Excel (.xlsx)</span>
                <span className="text-xs text-gray-500 ml-auto">
                  Dengan sheet &apos;Data Produk&apos; dan &apos;Info
                  Dokumen&apos;
                </span>
              </div>
            </div>

            <Separator />

            {/* Additional Options */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Opsi Tambahan</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="importFriendly"
                    checked={exportConfig.importFriendly}
                    onCheckedChange={(checked) =>
                      setExportConfig((prev) => ({
                        ...prev,
                        importFriendly: checked as boolean,
                      }))
                    }
                  />
                  <Label htmlFor="importFriendly" className="text-sm">
                    Format untuk Import
                  </Label>
                  <Info className="h-3 w-3 text-gray-400" />
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="includeSummary"
                    checked={exportConfig.includeSummary}
                    onCheckedChange={(checked) =>
                      setExportConfig((prev) => ({
                        ...prev,
                        includeSummary: checked as boolean,
                      }))
                    }
                  />
                  <Label
                    htmlFor="includeSummary"
                    className="text-sm cursor-pointer"
                  >
                    Sertakan ringkasan data
                  </Label>
                </div>
              </div>
            </div>

            {/* Export Progress */}
            {isExporting && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progress Export</span>
                  <span>{exportProgress}%</span>
                </div>
                <Progress value={exportProgress} className="w-full" />
              </div>
            )}
          </div>

          {/* Action Buttons - Fixed at bottom */}
          <div className="flex-shrink-0 flex justify-between pt-4 border-t bg-white dark:bg-gray-900">
            <Button
              className="cursor-pointer"
              variant="outline"
              onClick={() => setShowExportDialog(false)}
            >
              Batal
            </Button>
            <Button
              onClick={handleAdvancedExport}
              disabled={isExporting}
              className="flex items-center gap-2 cursor-pointer"
            >
              <Download className="h-4 w-4" />
              {isExporting ? "Mengekspor..." : "Export Data Pembelian"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
