"use client";

import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { User, Package, CreditCard, Box, FileText } from "lucide-react";
import { PurchaseFormValues, Product, Supplier } from "../types";
import { Control, UseFieldArrayRemove } from "react-hook-form";
import PurchaseInfoSection from "./PurchaseInfoSection";
import PurchaseItemTable from "./PurchaseItemTable";
import AdditionalInfo from "./AdditionalInfo";
import { Separator } from "@/components/ui/separator";

interface CombinedPurchaseFormProps {
  control: Control<PurchaseFormValues>;
  isPending: boolean;
  products: Product[];
  suppliers: Supplier[];
  items: PurchaseFormValues["items"];
  fields: any[];
  append: (value: any) => void;
  remove: UseFieldArrayRemove;
  handleProductChange: (index: number, productId: string) => void;
  totalAmount: number;
  createdAt?: string; // Optional createdAt for edit mode
  onSuppliersRefresh?: () => void; // Callback to refresh suppliers
  setValue?: (name: keyof PurchaseFormValues, value: any) => void; // For setting form values
  trigger?: (
    name?: keyof PurchaseFormValues | (keyof PurchaseFormValues)[]
  ) => Promise<boolean>; // Optional trigger function
}

const CombinedPurchaseForm: React.FC<CombinedPurchaseFormProps> = ({
  control,
  isPending,
  products,
  suppliers,
  items,
  fields,
  append,
  remove,
  handleProductChange,
  totalAmount,
  createdAt,
  onSuppliersRefresh,
  setValue,
  trigger,
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Formulir Pembelian</CardTitle>
        <CardDescription>
          Lengkapi semua informasi pembelian di bawah ini
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-8">
        {/* Supplier Information Section */}
        <div>
          <PurchaseInfoSection
            control={control}
            isPending={isPending}
            suppliers={suppliers}
            createdAt={createdAt}
            onSuppliersRefresh={onSuppliersRefresh}
            setValue={setValue}
            trigger={trigger}
          />
        </div>

        <Separator />

        {/* Items Section */}
        <div className="my-2">
          <div className="flex items-center gap-2 mb-4">
            <Package className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-medium">Item Pembelian</h3>
          </div>
          <PurchaseItemTable
            control={control}
            isPending={isPending}
            products={products}
            items={items}
            fields={fields}
            append={append}
            remove={remove}
            handleProductChange={handleProductChange}
          />
        </div>

        {/* Payment Section */}
        {/* <div>
          <div className="flex items-center gap-2 mb-4">
            <CreditCard className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-medium">Status Pembayaran</h3>
          </div>
          <PurchasePaymentSection
            control={control}
            isPending={isPending}
            totalAmount={totalAmount}
            items={items}
          />
        </div> */}

        <Separator />

        {/* Additional Info Section (Memo, Lampiran) */}
        <div>
          <div className="flex items-center gap-2 mb-4">
            <FileText className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-medium">Informasi Tambahan</h3>
          </div>
          <AdditionalInfo
            control={control}
            isPending={isPending}
            items={items}
          />
        </div>
      </CardContent>
    </Card>
  );
};

export default CombinedPurchaseForm;
