"use client";

import type { NextPage } from "next";
import Head from "next/head";
import {
  ExclamationCircleIcon,
  XCircleIcon,
  CheckCircleIcon,
  ChevronUpIcon,
  ChevronDownIcon,
  ArrowsUpDownIcon,
} from "@heroicons/react/24/outline";
import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "sonner";
import { Pagination } from "@/components/ui/pagination";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";

// Import types and components
import { Purchase, PurchaseCounts, ColumnVisibility } from "./types";
import { PurchaseSummaryCards } from "./components/StockSummaryCards";
import { PurchaseActions } from "./components/PurchaseActions";
import { PurchaseTableDesktop } from "./components/PurchaseTableDesktop";
import { deletePurchase } from "@/actions/entities/purchases";
import { PurchaseFilterState } from "./components/PurchaseFilter";

interface PurchasesPageProps {
  purchases: Purchase[];
  suppliers?: Array<{ id: string; name: string }>;
  warehouses?: Array<{ id: string; name: string }>;
}

const PurchasesPage: NextPage<PurchasesPageProps> = ({
  purchases,
  suppliers = [],
  warehouses = [],
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [searchTerm, setSearchTerm] = useState("");

  // Initialize subTab from URL parameter or default to "all-purchases"
  const [subTab, setSubTab] = useState(() => {
    const tabParam = searchParams.get("tab");
    if (tabParam === "drafts" || tabParam === "draft-pembelian") {
      return "drafts";
    } else if (
      tabParam === "all-purchases" ||
      tabParam === "daftar-pembelian"
    ) {
      return "all-purchases";
    }
    return "all-purchases";
  });

  const [filteredPurchases, setFilteredPurchases] =
    useState<Purchase[]>(purchases);

  // Filter state
  const [filters, setFilters] = useState<PurchaseFilterState>({
    supplier: undefined,
    dateRange: { start: undefined, end: undefined },
    amountRange: { min: undefined, max: undefined },
    status: "all",
    hasInvoice: undefined,
    warehouse: undefined,
  });

  // Update activeTab when URL changes
  useEffect(() => {
    const tabParam = searchParams.get("tab");
    if (tabParam === "drafts" || tabParam === "draft-pembelian") {
      setSubTab("drafts");
    } else if (
      tabParam === "all-purchases" ||
      tabParam === "daftar-pembelian"
    ) {
      setSubTab("all-purchases");
    }
  }, [searchParams]);

  // Handle tab change and update URL
  const handleTabChange = (value: string) => {
    setSubTab(value);

    // Create new URLSearchParams from current search params
    const params = new URLSearchParams(searchParams.toString());

    if (value === "drafts") {
      params.set("tab", "draft-pembelian");
    } else if (value === "all-purchases") {
      params.set("tab", "daftar-pembelian");
    } else {
      params.delete("tab"); // Remove tab parameter for default
    }

    // Use router.replace to update URL without refresh or navigation
    const newUrl = `${window.location.pathname}?${params.toString()}`;
    router.replace(newUrl, { scroll: false });
  };
  const [sortField, setSortField] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [paginatedPurchases, setPaginatedPurchases] = useState<Purchase[]>([]);

  // Helper function to get default column visibility
  const getDefaultColumnVisibility = (): ColumnVisibility => ({
    id: true,
    date: true,
    supplier: true,
    totalAmount: true,
    invoiceRef: true,
    itemCount: true,
    paymentDueDate: true,
    tags: true,
    quantity: true,
    status: true, // New status column
  });

  // Column visibility state with localStorage persistence
  const [columnVisibility, setColumnVisibility] = useState<ColumnVisibility>(
    () => {
      // Try to get saved column visibility from localStorage
      if (typeof window !== "undefined") {
        try {
          const savedVisibility = localStorage.getItem(
            "purchaseColumnVisibility"
          );
          if (savedVisibility) {
            const parsed = JSON.parse(savedVisibility) as ColumnVisibility;

            // Validate that all required keys exist in the saved data
            const defaultVisibility = getDefaultColumnVisibility();
            const hasAllKeys = Object.keys(defaultVisibility).every(
              (key) => key in parsed
            );

            if (hasAllKeys) {
              console.log(
                "Using saved purchase column visibility from localStorage:",
                parsed
              );
              return parsed;
            } else {
              // If saved data is incomplete, remove it and use defaults
              localStorage.removeItem("purchaseColumnVisibility");
              console.warn(
                "Incomplete purchase column visibility data found, using defaults"
              );
            }
          }
        } catch (error) {
          console.error(
            "Failed to parse saved purchase column visibility:",
            error
          );
          // Clear corrupted data
          localStorage.removeItem("purchaseColumnVisibility");
        }
      }

      // Default column visibility for new users or when localStorage is invalid
      const defaultVisibility = getDefaultColumnVisibility();
      console.log(
        "Using default purchase column visibility for new user:",
        defaultVisibility
      );
      return defaultVisibility;
    }
  );

  // State for selected purchases
  const [selectedPurchases, setSelectedPurchases] = useState<string[]>([]);

  // Calculate purchase counts for the summary cards
  const purchaseCounts: PurchaseCounts = {
    total: purchases.length,
    thisMonth: purchases.filter((p) => {
      const now = new Date();
      const purchaseDate = new Date(p.purchaseDate);
      return (
        purchaseDate.getMonth() === now.getMonth() &&
        purchaseDate.getFullYear() === now.getFullYear()
      );
    }).length,
    today: purchases.filter((p) => {
      const now = new Date();
      const purchaseDate = new Date(p.purchaseDate);
      return (
        purchaseDate.getDate() === now.getDate() &&
        purchaseDate.getMonth() === now.getMonth() &&
        purchaseDate.getFullYear() === now.getFullYear()
      );
    }).length,
    lastMonth: 0, // This would normally be calculated from the data
    pending: 0, // This would normally be calculated from the data
    drafts: purchases.filter((p) => p.isDraft).length,
  };

  // Filter purchases based on search term and tab
  useEffect(() => {
    // Reset to first page when search term changes
    setCurrentPage(1);

    // Start with all purchases
    let filtered = [...purchases];

    // Apply sub-tab filters
    if (subTab === "all-purchases") {
      // Filter out draft purchases from all-purchases tab
      filtered = filtered.filter((purchase) => !purchase.isDraft);
    } else if (subTab === "drafts") {
      // Show only draft purchases
      filtered = filtered.filter((purchase) => purchase.isDraft);
    }

    // Apply search term filter
    if (searchTerm.trim()) {
      const lowercasedTerm = searchTerm.toLowerCase();
      filtered = filtered.filter((purchase) => {
        return (
          purchase.id.toLowerCase().includes(lowercasedTerm) ||
          (purchase.supplier?.name &&
            purchase.supplier.name.toLowerCase().includes(lowercasedTerm)) ||
          (purchase.invoiceRef &&
            purchase.invoiceRef.toLowerCase().includes(lowercasedTerm)) ||
          (purchase.tags &&
            purchase.tags.some((tag) =>
              tag.toLowerCase().includes(lowercasedTerm)
            ))
        );
      });
    }

    // Apply advanced filters
    if (filters.supplier) {
      filtered = filtered.filter(
        (purchase) => purchase.supplierId === filters.supplier
      );
    }

    if (filters.dateRange?.start) {
      filtered = filtered.filter(
        (purchase) =>
          new Date(purchase.purchaseDate) >= filters.dateRange!.start!
      );
    }

    if (filters.dateRange?.end) {
      filtered = filtered.filter(
        (purchase) => new Date(purchase.purchaseDate) <= filters.dateRange!.end!
      );
    }

    if (filters.amountRange?.min !== undefined) {
      filtered = filtered.filter(
        (purchase) => purchase.totalAmount >= filters.amountRange!.min!
      );
    }

    if (filters.amountRange?.max !== undefined) {
      filtered = filtered.filter(
        (purchase) => purchase.totalAmount <= filters.amountRange!.max!
      );
    }

    if (filters.status && filters.status !== "all") {
      switch (filters.status) {
        case "completed":
          filtered = filtered.filter((purchase) => !purchase.isDraft);
          break;
        case "draft":
          filtered = filtered.filter((purchase) => purchase.isDraft);
          break;
      }
    }

    if (filters.hasInvoice !== undefined) {
      filtered = filtered.filter((purchase) =>
        filters.hasInvoice ? !!purchase.invoiceRef : !purchase.invoiceRef
      );
    }

    if (filters.warehouse) {
      filtered = filtered.filter(
        (purchase) => purchase.warehouseId === filters.warehouse
      );
    }

    setFilteredPurchases(filtered);
  }, [searchTerm, purchases, subTab, filters]);

  // Apply pagination to filtered purchases
  useEffect(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    setPaginatedPurchases(filteredPurchases.slice(startIndex, endIndex));
  }, [filteredPurchases, currentPage, itemsPerPage]);

  // Save column visibility to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== "undefined") {
      try {
        localStorage.setItem(
          "purchaseColumnVisibility",
          JSON.stringify(columnVisibility)
        );
      } catch (error) {
        console.error(
          "Failed to save purchase column visibility to localStorage:",
          error
        );
      }
    }
  }, [columnVisibility]);

  // Utility function to reset column visibility to defaults
  const resetColumnVisibility = () => {
    const defaultVisibility = getDefaultColumnVisibility();
    setColumnVisibility(defaultVisibility);

    // Also clear from localStorage to ensure clean state
    if (typeof window !== "undefined") {
      try {
        localStorage.removeItem("purchaseColumnVisibility");
      } catch (error) {
        console.error(
          "Failed to clear purchase column visibility from localStorage:",
          error
        );
      }
    }
  };

  // Sort purchases
  const handleSort = (field: string) => {
    // Reset to first page when sorting changes
    setCurrentPage(1);

    if (sortField === field) {
      // Toggle sort direction if clicking the same field
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      // Set new sort field and default to ascending
      setSortField(field);
      setSortDirection("asc");
    }

    // Sort the filtered purchases
    const sortedPurchases = [...filteredPurchases].sort((a, b) => {
      let aValue, bValue;

      // Handle special case for itemCount which is derived
      if (field === "itemCount") {
        aValue = a.items.length;
        bValue = b.items.length;
      } else if (field === "supplier") {
        // Handle supplier name sorting
        aValue = a.supplier?.name || "";
        bValue = b.supplier?.name || "";
      } else if (field === "tags") {
        // Handle tags sorting (join tags into a string for sorting)
        aValue = a.tags?.join(", ") || "";
        bValue = b.tags?.join(", ") || "";
      } else if (field === "paymentDueDate") {
        // Handle payment due date sorting
        aValue = a.paymentDueDate ? new Date(a.paymentDueDate).getTime() : 0;
        bValue = b.paymentDueDate ? new Date(b.paymentDueDate).getTime() : 0;
      } else {
        // Handle other fields
        aValue = a[field as keyof Purchase] || "";
        bValue = b[field as keyof Purchase] || "";
      }

      // Compare values
      if (aValue < bValue) return sortDirection === "asc" ? -1 : 1;
      if (aValue > bValue) return sortDirection === "asc" ? 1 : -1;
      return 0;
    });

    setFilteredPurchases(sortedPurchases);
  };

  // Get sort icon based on current sort state
  const getSortIcon = (field: string) => {
    if (sortField !== field) {
      return <ArrowsUpDownIcon className="h-4 w-4 ml-1" />;
    }
    return sortDirection === "asc" ? (
      <ChevronUpIcon className="h-4 w-4 ml-1" />
    ) : (
      <ChevronDownIcon className="h-4 w-4 ml-1" />
    );
  };

  // Handle batch delete of selected purchases
  const handleBatchDelete = async () => {
    if (selectedPurchases.length === 0) return;

    try {
      // Delete each selected purchase
      for (const id of selectedPurchases) {
        const result = await deletePurchase(id);
        if (result.error) {
          toast.error(`Gagal menghapus pembelian: ${result.error}`);
        }
      }

      // Show success message
      toast.success(`${selectedPurchases.length} pembelian berhasil dihapus`);

      // Clear selection
      setSelectedPurchases([]);

      // Refresh the page
      window.location.reload();
    } catch (error) {
      console.error("Error deleting purchases:", error);
      toast.error("Terjadi kesalahan saat menghapus pembelian.");
    }
  };

  return (
    <>
      <Head>
        <title>Pembelian - KivaPOS</title>
      </Head>

      <div className="space-y-6">
        {/* Main Tabs */}
        {/* Purchase Status Summary Cards */}
        <PurchaseSummaryCards purchaseCounts={purchaseCounts} />

        {/* Sub Tabs */}
        <Tabs
          defaultValue="all-purchases"
          value={subTab}
          onValueChange={handleTabChange}
          className="w-full"
        >
          <TabsList className="mb-4 w-full md:w-fit">
            <TabsTrigger value="all-purchases">Daftar Pembelian</TabsTrigger>
            <TabsTrigger value="drafts">Draft Pembelian</TabsTrigger>
          </TabsList>

          <TabsContent value="all-purchases" className="space-y-6">
            {/* Header Actions */}
            <PurchaseActions
              columnVisibility={columnVisibility}
              setColumnVisibility={setColumnVisibility}
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
              filters={filters}
              onFilterChange={setFilters}
              suppliers={suppliers}
              warehouses={warehouses}
              selectedPurchases={selectedPurchases}
              onBatchDelete={handleBatchDelete}
              onRefresh={() =>
                console.log(
                  "Refresh disabled to prevent navigation interference"
                )
              }
            />

            {/* Purchases List */}
            <div className="overflow-x-auto">
              {/* Desktop Table View */}
              <PurchaseTableDesktop
                purchases={paginatedPurchases}
                columnVisibility={columnVisibility}
                handleSort={handleSort}
                getSortIcon={getSortIcon}
                searchTerm={searchTerm}
                onSelectionChange={setSelectedPurchases}
              />
            </div>

            {/* Pagination - Moved outside the overflow container */}
            <div className="mt-4">
              <Pagination
                currentPage={currentPage}
                totalPages={Math.ceil(filteredPurchases.length / itemsPerPage)}
                onPageChange={setCurrentPage}
                itemsPerPage={itemsPerPage}
                onItemsPerPageChange={setItemsPerPage}
                totalItems={filteredPurchases.length}
              />
            </div>
          </TabsContent>

          <TabsContent value="drafts" className="space-y-6">
            {/* Header Actions */}
            <PurchaseActions
              columnVisibility={columnVisibility}
              setColumnVisibility={setColumnVisibility}
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
              filters={filters}
              onFilterChange={setFilters}
              suppliers={suppliers}
              warehouses={warehouses}
              selectedPurchases={selectedPurchases}
              onBatchDelete={handleBatchDelete}
              onRefresh={() =>
                console.log(
                  "Refresh disabled to prevent navigation interference"
                )
              }
            />

            {/* Purchases List */}
            <div className="overflow-x-auto">
              {/* Desktop Table View */}
              <PurchaseTableDesktop
                purchases={paginatedPurchases}
                columnVisibility={columnVisibility}
                handleSort={handleSort}
                getSortIcon={getSortIcon}
                searchTerm={searchTerm}
                onSelectionChange={setSelectedPurchases}
              />
            </div>

            {/* Pagination - Moved outside the overflow container */}
            <div className="mt-4">
              <Pagination
                currentPage={currentPage}
                totalPages={Math.ceil(filteredPurchases.length / itemsPerPage)}
                onPageChange={setCurrentPage}
                itemsPerPage={itemsPerPage}
                onItemsPerPageChange={setItemsPerPage}
                totalItems={filteredPurchases.length}
              />
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
};

export default PurchasesPage;
