import type { Purchase } from "../types";

/**
 * Horizontal Template 1 - Based on custom1 template with horizontal orientation
 */
export const renderHorizontalTemplate1 = (purchase: Purchase): string => {
  return `
    <!DOCTYPE html>
    <html lang="id">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Faktur Pembelian - ${purchase.transactionNumber || purchase.id.substring(0, 8)}</title>
      <style>
        @page {
          size: A4 landscape;
          margin: 1.5cm;
        }
        body {
          font-family: Arial, sans-serif;
          margin: 0;
          padding: 0;
          color: #000;
          background-color: white;
          font-size: 12pt;
        }
        .invoice-container {
          max-width: 29.7cm;
          margin: 0 auto;
          padding: 20px;
          box-sizing: border-box;
        }
        .letterhead {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
          border-bottom: 2px solid #333;
          padding-bottom: 15px;
        }
        .company-info {
          flex: 2;
        }
        .company-name {
          font-size: 24pt;
          font-weight: bold;
          margin-bottom: 5px;
        }
        .company-details {
          font-size: 10pt;
        }
        .invoice-label {
          flex: 1;
          text-align: right;
        }
        .invoice-title {
          font-size: 28pt;
          font-weight: bold;
          color: #333;
        }
        .invoice-details {
          display: flex;
          justify-content: space-between;
          margin-bottom: 20px;
          font-size: 11pt;
        }
        .invoice-number {
          font-weight: bold;
        }
        .invoice-date {
          text-align: right;
        }
        .parties {
          display: flex;
          justify-content: space-between;
          margin-bottom: 15px;
        }
        .supplier-info, .buyer-info {
          width: 48%;
          padding: 0;
          border-bottom: 1px solid #ddd;
        }
        .section-title {
          font-weight: bold;
          font-size: 11pt;
          margin-bottom: 6px;
        }
        .info-row {
          margin-bottom: 4px;
          display: flex;
        }
        .info-label {
          font-weight: bold;
          width: 80px;
          font-size: 10pt;
        }
        .info-value {
          flex: 1;
          font-size: 10pt;
          padding-left: 10px;
        }
        table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 20px;
        }
        th, td {
          border: 1px solid #ddd;
          padding: 10px;
          text-align: left;
          font-size: 11pt;
        }
        th {
          background-color: #f2f2f2;
          font-weight: bold;
        }
        .text-right {
          text-align: right;
        }
        .text-center {
          text-align: center;
        }
        .total-section {
          margin-top: 20px;
          text-align: right;
        }
        .total-row {
          font-weight: bold;
          font-size: 12pt;
        }
        .signatures {
          display: flex;
          justify-content: space-between;
          margin-top: 50px;
          page-break-inside: avoid;
        }
        .signature-box {
          width: 45%;
          text-align: center;
        }
        .signature-line {
          border-bottom: 1px solid #000;
          margin-bottom: 10px;
          height: 70px;
        }
        .signature-name {
          font-weight: bold;
        }
        .signature-title {
          font-size: 10pt;
        }
        .footer {
          margin-top: 30px;
          text-align: center;
          font-size: 10pt;
          color: #555;
          border-top: 1px solid #ddd;
          padding-top: 10px;
          page-break-inside: avoid;
        }
        .memo {
          margin-top: 20px;
          border: 1px solid #ddd;
          padding: 10px;
          font-size: 10pt;
          page-break-inside: avoid;
          background-color: #f9f9f9;
          border-radius: 5px;
        }
        .memo-title {
          font-weight: bold;
          margin-bottom: 5px;
        }
        @media print {
          body {
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
          }
          .invoice-container {
            padding: 0;
          }
          .no-print {
            display: none;
          }
        }
      </style>
    </head>
    <body>
      <div class="invoice-container">
        <!-- Letterhead -->
        <div class="letterhead">
          <div class="company-info">
            <div class="company-name">${purchase.companyInfo?.companyName || purchase.user?.name || "KivaPOS"}</div>
            <div class="company-details">
              ${purchase.companyInfo?.companyAddress || "-"}<br>
              No. HP: ${purchase.companyInfo?.companyPhone || "-"} | Email: ${purchase.companyInfo?.companyEmail || "-"}
            </div>
          </div>
          <div class="invoice-label">
            <div class="invoice-title">FAKTUR PEMBELIAN</div>
          </div>
        </div>

        <!-- Invoice Details and Supplier Info -->
        <div class="parties">
          <div class="supplier-info" style="width: 48%;">
            <div class="section-title">Supplier</div>
            <div class="info-row">
              <div class="info-label">Nama</div>
              <div class="info-value">: ${purchase.supplier ? purchase.supplier.name : "-"}</div>
            </div>
            <div class="info-row">
              <div class="info-label">Email</div>
              <div class="info-value">: ${purchase.supplier?.email || "-"}</div>
            </div>
            <div class="info-row">
              <div class="info-label">No. HP</div>
              <div class="info-value">: ${purchase.supplier?.phone || "-"}</div>
            </div>
            <div class="info-row">
              <div class="info-label">Alamat</div>
              <div class="info-value">: ${purchase.supplier?.address || "-"}</div>
            </div>
          </div>

          <div class="supplier-info" style="width: 48%;">
            <div class="section-title">Informasi Faktur</div>
            <div class="info-row">
              <div class="info-label">No. Faktur</div>
              <div class="info-value">: ${purchase.transactionNumber || purchase.id.substring(0, 8)}</div>
            </div>
            <div class="info-row">
              <div class="info-label">No. Ref</div>
              <div class="info-value">: ${purchase.invoiceRef || "-"}</div>
            </div>
            <div class="info-row">
              <div class="info-label">Tanggal</div>
              <div class="info-value">: ${new Date(
                purchase.purchaseDate
              ).toLocaleDateString("id-ID", {
                day: "numeric",
                month: "long",
                year: "numeric",
              })}</div>
            </div>
            <div class="info-row">
              <div class="info-label">Jth Tempo</div>
              <div class="info-value">: ${
                purchase.paymentDueDate
                  ? new Date(purchase.paymentDueDate).toLocaleDateString(
                      "id-ID",
                      {
                        day: "numeric",
                        month: "long",
                        year: "numeric",
                      }
                    )
                  : "-"
              }</div>
            </div>
          </div>
        </div>

        <!-- Items Table -->
        <table>
          <thead>
            <tr>
              <th class="text-center" style="width: 5%;">No.</th>
              <th style="width: 40%;">Nama Barang</th>
              <th class="text-center" style="width: 10%;">Quantity</th>
              <th class="text-center" style="width: 10%;">Satuan</th>
              <th class="text-right" style="width: 15%;">Harga Satuan</th>
              <th class="text-right" style="width: 20%;">Total</th>
            </tr>
          </thead>
          <tbody>
            ${purchase.items
              .map(
                (item, index) => `
              <tr>
                <td class="text-center">${index + 1}</td>
                <td>${item.product.name}</td>
                <td class="text-center">${item.quantity}</td>
                <td class="text-center">${item.unit || "Buah"}</td>
                <td class="text-right">Rp ${item.costAtPurchase.toLocaleString("id-ID")}</td>
                <td class="text-right">Rp ${(item.quantity * item.costAtPurchase).toLocaleString("id-ID")}</td>
              </tr>
            `
              )
              .join("")}
          </tbody>
        </table>

        <!-- Summary Section -->
        <table style="border-collapse: collapse; margin-top: -20px; width: 100%;">
          ${(() => {
            // Calculate subtotal (before discount and tax)
            const subtotal = purchase.items.reduce(
              (sum, item) => sum + item.quantity * item.costAtPurchase,
              0
            );

            // Calculate total discount amount (convert percentage to nominal if needed)
            const totalDiscount = purchase.items.reduce((sum, item) => {
              if (item.discountAmount) {
                return sum + item.discountAmount;
              } else if (item.discountPercentage) {
                return (
                  sum +
                  item.quantity *
                    item.costAtPurchase *
                    (item.discountPercentage / 100)
                );
              }
              return sum;
            }, 0);

            // Calculate total tax amount (assuming tax is stored as percentage or amount)
            const totalTax = purchase.items.reduce((sum, item) => {
              if (item.tax && !isNaN(parseFloat(item.tax))) {
                // If tax is a number, treat it as percentage
                return (
                  sum +
                  item.quantity *
                    item.costAtPurchase *
                    (parseFloat(item.tax) / 100)
                );
              }
              return sum;
            }, 0);

            return `
              <tr>
                <td colspan="5" style="border: none; text-align: right; padding: 4px 10px; line-height: 1.2;">Sub-Total:</td>
                <td style="border: none; text-align: right; padding: 4px 10px; width: 20%; line-height: 1.2;">Rp ${subtotal.toLocaleString("id-ID")}</td>
              </tr>
              <tr>
                <td colspan="5" style="border: none; text-align: right; padding: 4px 10px; line-height: 1.2;">Diskon:</td>
                <td style="border: none; text-align: right; padding: 4px 10px; line-height: 1.2;">${totalDiscount > 0 ? `Rp ${totalDiscount.toLocaleString("id-ID")}` : "-"}</td>
              </tr>
              <tr>
                <td colspan="5" style="border: none; text-align: right; padding: 4px 10px; line-height: 1.2;">PPN:</td>
                <td style="border: none; text-align: right; padding: 4px 10px; line-height: 1.2;">${totalTax > 0 ? `Rp ${totalTax.toLocaleString("id-ID")}` : "-"}</td>
              </tr>
              <tr style="font-weight: bold;">
                <td colspan="5" style="border: none; text-align: right; padding: 4px 10px; line-height: 1.2;">Total:</td>
                <td style="border: none; text-align: right; padding: 4px 10px; line-height: 1.2;">Rp ${purchase.totalAmount.toLocaleString("id-ID")}</td>
              </tr>
            `;
          })()}
        </table>

        <!-- Memo Section (if available) -->
        ${
          purchase.memo
            ? `
        <div class="memo">
          <div class="memo-title">Catatan:</div>
          <div>${purchase.memo}</div>
        </div>
        `
            : ""
        }

        <!-- Signatures -->
        <div class="signatures">
          <div class="signature-box">
            <div class="signature-line"></div>
            <div class="signature-name">( ${purchase.supplier ? purchase.supplier.name : "Supplier"} )</div>
            <div class="signature-title">Supplier</div>
          </div>
          <div class="signature-box">
            <div class="signature-line"></div>
            <div class="signature-name">( ${purchase.companyInfo?.companyName || purchase.user?.name || "KivaPOS"} )</div>
            <div class="signature-title">Pembeli</div>
          </div>
        </div>

        <!-- Footer -->
        <div class="footer">
          <p>Faktur ini adalah bukti resmi pembelian. Terima kasih atas kerjasamanya.</p>
          <p>Dicetak melalui ${purchase.companyInfo?.companyName || purchase.user?.name || "KivaPOS"} pada ${new Date().toLocaleDateString(
            "id-ID",
            {
              day: "numeric",
              month: "long",
              year: "numeric",
            }
          )}</p>
        </div>
      </div>
    </body>
    </html>
  `;
};
