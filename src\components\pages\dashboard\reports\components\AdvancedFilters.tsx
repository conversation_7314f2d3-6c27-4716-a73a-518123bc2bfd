"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Switch } from "@/components/ui/switch";
import {
  Filter,
  Calendar as CalendarIcon,
  RefreshCw,
  Settings,
  X,
} from "lucide-react";
import { format } from "date-fns";

interface FilterState {
  dateRange: string;
  startDate?: Date;
  endDate?: Date;
  category?: string;
  supplier?: string;
  customer?: string;
  status?: string;
}

interface AdvancedFiltersProps {
  filters: FilterState;
  onFilterChange: (filters: Partial<FilterState>) => void;
  onRefreshIntervalChange: (interval: number | null) => void;
}

export const AdvancedFilters: React.FC<AdvancedFiltersProps> = ({
  filters,
  onFilterChange,
  onRefreshIntervalChange,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState(30);

  const handleDateRangeChange = (value: string) => {
    onFilterChange({ dateRange: value });

    // Set predefined date ranges
    const now = new Date();
    let startDate: Date | undefined;
    let endDate: Date | undefined = now;

    switch (value) {
      case "today":
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case "7d":
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case "30d":
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case "month":
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case "year":
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
      default:
        startDate = undefined;
        endDate = undefined;
    }

    if (startDate && endDate) {
      onFilterChange({ startDate, endDate });
    }
  };

  const handleAutoRefreshToggle = (enabled: boolean) => {
    setAutoRefresh(enabled);
    if (enabled) {
      onRefreshIntervalChange(refreshInterval);
    } else {
      onRefreshIntervalChange(null);
    }
  };

  const handleRefreshIntervalChange = (interval: number) => {
    setRefreshInterval(interval);
    if (autoRefresh) {
      onRefreshIntervalChange(interval);
    }
  };

  const resetFilters = () => {
    try {
      onFilterChange({
        dateRange: "30d",
        startDate: undefined,
        endDate: undefined,
        category: undefined,
        supplier: undefined,
        customer: undefined,
        status: undefined,
      });
    } catch (error) {
      console.error("Error resetting filters:", error);
    }
  };

  return (
    <div className="flex items-center gap-2">
      {/* Quick Date Range Selector */}
      <Select value={filters.dateRange} onValueChange={handleDateRangeChange}>
        <SelectTrigger className="w-[180px] !h-9">
          <SelectValue placeholder="Pilih periode" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="today">Hari Ini</SelectItem>
          <SelectItem value="7d">7 Hari Terakhir</SelectItem>
          <SelectItem value="30d">30 Hari Terakhir</SelectItem>
          <SelectItem value="month">Bulan Ini</SelectItem>
          <SelectItem value="year">Tahun Ini</SelectItem>
          <SelectItem value="custom">Kustom</SelectItem>
        </SelectContent>
      </Select>

      {/* Refresh Button */}
      <Button
        variant="outline"
        size="icon"
        onClick={() => window.location.reload()}
        className="shrink-0 cursor-pointer"
      >
        <RefreshCw className="h-4 w-4" />
      </Button>
    </div>
  );
};
