import React, { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { Sale, ColumnVisibility } from "../types"; // Import from the types file
import { salesColumnConfig } from "../config/columnConfig";
import { Trash, Printer, Edit, Share2, LoaderCircle } from "lucide-react";
import { InlineStatusEditor } from "./InlineStatusEditor";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { deleteSale } from "@/actions/entities/sales";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  <PERSON>lt<PERSON>,
  Toolt<PERSON>Content,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Use shared column configuration
const columnConfig = salesColumnConfig;

interface SaleTableDesktopProps {
  sales: Sale[];
  columnVisibility: ColumnVisibility;
  handleSort: (field: string) => void;
  getSortIcon: (field: string) => React.ReactNode;
  searchTerm: string;
  selectedSales?: string[];
  setSelectedSales?: React.Dispatch<React.SetStateAction<string[]>>;
}

export const SaleTableDesktop: React.FC<SaleTableDesktopProps> = ({
  sales,
  columnVisibility,
  handleSort,
  getSortIcon,
  searchTerm,
  selectedSales = [],
  setSelectedSales = () => {},
}) => {
  const router = useRouter();
  const [saleToDelete, setSaleToDelete] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isPrintDialogOpen, setIsPrintDialogOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] =
    useState<string>("horizontal1");
  const [selectedOrientation, setSelectedOrientation] = useState<
    "horizontal" | "vertical"
  >("horizontal");
  const [saleToPrint, setSaleToPrint] = useState<Sale | null>(null);

  // Helper function to render cell content based on column key
  const renderCellContent = (sale: Sale, columnKey: keyof ColumnVisibility) => {
    switch (columnKey) {
      case "id":
        return (
          <Link
            href={`/dashboard/sales/detail/${sale.transactionNumber || sale.id}`}
            className="hover:underline text-blue-600 dark:text-blue-400 cursor-pointer"
          >
            {sale.transactionNumber ||
              `${sale.id.substring(0, 4)}-${sale.id.substring(4, 8)}-${sale.id.substring(8, 12)}`}
          </Link>
        );
      case "date":
        return formatDate(sale.saleDate);
      case "paymentDueDate":
        return sale.paymentDueDate ? formatDate(sale.paymentDueDate) : "-";
      case "customer":
        return sale.customer?.name || "Umum";
      case "totalAmount":
        return `Rp ${sale.totalAmount.toLocaleString("id-ID")}`;
      case "itemCount":
        return `${sale.items.length} item`;
      case "totalQuantity":
        return sale.items
          .reduce((sum, item) => sum + item.quantity, 0)
          .toString();
      case "invoiceRef":
        return sale.invoiceRef || "-";
      case "tags":
        return sale.tags && sale.tags.length > 0 ? sale.tags.join(", ") : "-";
      case "status":
        return (
          <InlineStatusEditor saleId={sale.id} currentStatus={sale.status} />
        );
      default:
        return "-";
    }
  };

  // Handle delete sale
  const handleDeleteSale = async (id: string) => {
    console.log(`[SaleTable] Starting delete process for sale ID: ${id}`);
    setSaleToDelete(id);
    setIsDeleting(true);
    try {
      console.log(`[SaleTable] Calling deleteSale action for ID: ${id}`);
      const result = await deleteSale(id);
      console.log(`[SaleTable] Delete result:`, result);

      if (result.success) {
        console.log(`[SaleTable] Delete successful, showing success toast`);
        toast.success(result.success);
        console.log(`[SaleTable] Calling router.refresh()`);
        // Refresh the page to show updated data
        router.refresh();
      } else if (result.error) {
        console.log(`[SaleTable] Delete failed with error:`, result.error);
        toast.error(result.error);
      }
    } catch (error) {
      console.error("[SaleTable] Exception during delete:", error);
      toast.error("Terjadi kesalahan saat menghapus penjualan.");
    } finally {
      console.log(`[SaleTable] Cleaning up delete state for ID: ${id}`);
      setIsDeleting(false);
      setSaleToDelete(null);
    }
  };

  // Handle print invoice - open dialog
  const handlePrintInvoice = (sale: Sale) => {
    setSaleToPrint(sale);
    setIsPrintDialogOpen(true);
  };

  // Handle actual print after template selection
  const handlePrint = () => {
    if (!saleToPrint) return;

    // Close the dialog
    setIsPrintDialogOpen(false);

    // Create a new window for printing
    const printWindow = window.open("", "_blank");
    if (!printWindow) {
      toast.error(
        "Popup diblokir oleh browser. Mohon izinkan popup untuk mencetak faktur."
      );
      return;
    }

    // Import the template dynamically to avoid bundling all templates
    import("../templates")
      .then(({ getInvoiceTemplate }) => {
        // Use the selected template
        const templateContent = getInvoiceTemplate(
          selectedTemplate as any,
          saleToPrint
        );

        // Write the template content to the new window
        const parser = new DOMParser();
        const htmlDoc = parser.parseFromString(templateContent, "text/html");

        // Clear the document and append the new content
        printWindow.document.documentElement.innerHTML = "";
        Array.from(htmlDoc.head.childNodes).forEach((node) => {
          printWindow.document.head.appendChild(
            printWindow.document.importNode(node, true)
          );
        });
        Array.from(htmlDoc.body.childNodes).forEach((node) => {
          printWindow.document.body.appendChild(
            printWindow.document.importNode(node, true)
          );
        });

        // Wait for images and resources to load before printing
        printWindow.onload = () => {
          printWindow.print();
          // printWindow.close(); // Uncomment to auto-close after print dialog
        };
      })
      .catch((error) => {
        console.error("Error loading templates:", error);
        toast.error("Terjadi kesalahan saat memuat template faktur.");
        printWindow.close();
      });
  };

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString("id-ID", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  // Handle select all sales
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedSales(sales.map((sale) => sale.id));
    } else {
      setSelectedSales([]);
    }
  };

  // Handle select individual sale
  const handleSelectSale = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedSales([...selectedSales, id]);
    } else {
      setSelectedSales(selectedSales.filter((saleId) => saleId !== id));
    }
  };

  return (
    <div className="relative overflow-x-auto bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700 rounded-lg">
      <table className="w-full text-sm text-left text-gray-500 dark:text-gray-400">
        <thead className="sticky top-0 z-10 text-xs text-gray-700 dark:text-gray-300 uppercase bg-white shadow-[0_2px_4px_rgba(0,0,0,0.05)] dark:bg-gray-700">
          <tr>
            <th
              scope="col"
              className="px-4 py-3 border-r border-gray-200 dark:border-gray-700"
            >
              <Checkbox
                checked={
                  sales.length > 0 && selectedSales.length === sales.length
                }
                onCheckedChange={handleSelectAll}
                aria-label="Select all"
              />
            </th>
            {columnConfig.map(
              (column) =>
                columnVisibility[column.key] && (
                  <th
                    key={column.key}
                    scope="col"
                    className="px-4 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 border-r border-gray-200 dark:border-gray-700"
                    onClick={() => handleSort(column.sortKey)}
                  >
                    <div className="flex items-center">
                      {column.label}
                      {getSortIcon(column.sortKey)}
                    </div>
                  </th>
                )
            )}
            <th scope="col" className="px-6 py-3 text-right">
              Aksi
            </th>
          </tr>
        </thead>
        <tbody>
          {sales.length > 0 ? (
            sales.map((sale) => (
              <tr
                key={sale.id}
                className="bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                <td className="px-4 py-4 border-r border-gray-200 dark:border-gray-700">
                  <Checkbox
                    checked={selectedSales.includes(sale.id)}
                    onCheckedChange={(checked) =>
                      handleSelectSale(sale.id, checked as boolean)
                    }
                    aria-label={`Select sale ${sale.id}`}
                  />
                </td>
                {columnConfig.map(
                  (column) =>
                    columnVisibility[column.key] && (
                      <td
                        key={column.key}
                        className={`px-4 py-4 whitespace-nowrap border-r border-gray-200 dark:border-gray-700 ${
                          column.key === "id"
                            ? "font-medium text-gray-900 dark:text-gray-100"
                            : "text-gray-500 dark:text-gray-400"
                        }`}
                      >
                        {renderCellContent(sale, column.key)}
                      </td>
                    )
                )}
                <td className="px-6 py-4 text-right whitespace-nowrap border-l border-gray-200 dark:border-gray-700">
                  <div className="flex justify-end space-x-1">
                    {/* Print Button */}
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 bg-yellow-300 text-black hover:text-gray-700 cursor-pointer hover:bg-yellow-200"
                          onClick={() => handlePrintInvoice(sale)}
                        >
                          <Printer className="h-4 w-4" />
                          <span className="sr-only">Print Invoice</span>
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Aksi</p>
                      </TooltipContent>
                    </Tooltip>

                    {/* Share Button (WhatsApp) */}
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 bg-green-500 text-white cursor-pointer hover:bg-green-400"
                          onClick={() => {
                            const baseMessage = `Penjualan ${sale.transactionNumber || sale.id} - Total: ${new Intl.NumberFormat(
                              "id-ID",
                              {
                                style: "currency",
                                currency: "IDR",
                                minimumFractionDigits: 0,
                              }
                            ).format(sale.totalAmount)}`;

                            const publicUrl = `${process.env.NEXT_PUBLIC_APP_URL}/cek/sales/${sale.transactionNumber || sale.id}`;
                            const message = sale.isPublic
                              ? `${baseMessage} - cek detail disini: ${publicUrl}`
                              : `${baseMessage} - cek detail disini: ${publicUrl}`;

                            const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
                            window.open(whatsappUrl, "_blank");
                          }}
                        >
                          <Share2 className="h-4 w-4" />
                          <span className="sr-only">Share via WhatsApp</span>
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Bagikan</p>
                      </TooltipContent>
                    </Tooltip>

                    {/* Edit Button */}
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 bg-blue-500 text-white cursor-pointer hover:bg-blue-400"
                          onClick={() =>
                            router.push(
                              `/dashboard/sales/edit/${sale.transactionNumber || sale.id}`
                            )
                          }
                        >
                          <Edit className="h-4 w-4" />
                          <span className="sr-only">Edit</span>
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Edit</p>
                      </TooltipContent>
                    </Tooltip>

                    {/* Delete Button */}
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 bg-red-500 text-white cursor-pointer hover:bg-red-400"
                          disabled={isDeleting && saleToDelete === sale.id}
                          data-deleting={isDeleting && saleToDelete === sale.id}
                          onClick={() => {
                            console.log(
                              `[SaleTable] Delete button clicked for sale ID: ${sale.id}`
                            );
                          }}
                        >
                          {isDeleting && saleToDelete === sale.id ? (
                            <LoaderCircle className="h-4 w-4 animate-spin" />
                          ) : (
                            <Trash className="h-4 w-4" />
                          )}
                          <span className="sr-only">Delete</span>
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Konfirmasi Hapus</AlertDialogTitle>
                          <AlertDialogDescription>
                            Apakah Anda yakin ingin menghapus penjualan ini?
                            Tindakan ini tidak dapat dibatalkan dan akan
                            mengembalikan stok produk.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Batal</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => {
                              console.log(
                                `[SaleTable] AlertDialogAction clicked for sale ID: ${sale.id}`
                              );
                              handleDeleteSale(sale.id);
                            }}
                            disabled={isDeleting && saleToDelete === sale.id}
                            className="bg-red-500 hover:bg-red-600"
                          >
                            {isDeleting && saleToDelete === sale.id
                              ? "Menghapus..."
                              : "Hapus"}
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </td>
              </tr>
            ))
          ) : (
            <tr>
              <td
                colSpan={
                  Object.values(columnVisibility).filter(Boolean).length + 2
                }
                className="px-6 py-4 text-center text-gray-500 dark:text-gray-400"
              >
                {searchTerm
                  ? "Tidak ada transaksi yang sesuai dengan pencarian."
                  : "Belum ada data penjualan."}
              </td>
            </tr>
          )}
        </tbody>
      </table>

      {/* Print Dialog */}
      <Dialog open={isPrintDialogOpen} onOpenChange={setIsPrintDialogOpen}>
        <DialogContent className="max-h-[80vh] h-[80vh] overflow-y-auto max-w-[90vw] w-[900px] overflow-x-hidden">
          <DialogHeader>
            <DialogTitle>Cetak Faktur Penjualan</DialogTitle>
            <DialogDescription>
              Pilih template faktur yang ingin Anda cetak.
            </DialogDescription>
          </DialogHeader>

          <div className="grid grid-cols-1 md:grid-cols-[300px_minmax(0,1fr)] gap-6 py-4">
            {/* Left Column - Options */}
            <div className="space-y-6 flex flex-col h-full">
              {/* Orientation Options */}
              <div className="space-y-3">
                <div className="font-medium">Jenis Faktur</div>
                <div className="grid grid-cols-2 gap-3">
                  <div
                    className={`border rounded-md p-3 cursor-pointer ${
                      selectedOrientation === "horizontal"
                        ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                        : "hover:bg-gray-50 dark:hover:bg-gray-800"
                    }`}
                    onClick={() => {
                      setSelectedOrientation("horizontal");
                      // Update template to match orientation
                      if (selectedTemplate.startsWith("vertical")) {
                        const styleNumber = selectedTemplate.replace(
                          "vertical",
                          ""
                        );
                        setSelectedTemplate(`horizontal${styleNumber}`);
                      }
                    }}
                  >
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="20"
                          height="20"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="mr-1.5 text-blue-600"
                        >
                          <rect
                            x="3"
                            y="3"
                            width="18"
                            height="18"
                            rx="2"
                            ry="2"
                          ></rect>
                          <line x1="3" y1="9" x2="21" y2="9"></line>
                          <line x1="3" y1="15" x2="21" y2="15"></line>
                        </svg>
                        <span className="font-medium text-sm">
                          Faktur Lebar
                        </span>
                      </div>
                      <div className="w-full h-12 bg-white border border-gray-200 rounded-md flex items-center justify-center relative">
                        <div className="w-full h-full p-2 flex flex-col justify-center">
                          <div className="h-1.5 bg-gray-200 w-full mb-1 rounded"></div>
                          <div className="flex justify-between items-center">
                            <div className="w-1/3">
                              <div className="h-1 bg-gray-300 w-full mb-0.5 rounded"></div>
                              <div className="h-1 bg-gray-300 w-full mb-0.5 rounded"></div>
                            </div>
                            <div className="w-3/5">
                              <div className="h-1 bg-gray-200 w-full mb-0.5 rounded"></div>
                              <div className="h-1 bg-gray-200 w-full rounded"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="text-xs text-gray-500 mt-1.5">
                        Kertas A4
                      </div>
                    </div>
                  </div>

                  <div
                    className={`border rounded-md p-3 cursor-pointer ${
                      selectedOrientation === "vertical"
                        ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                        : "hover:bg-gray-50 dark:hover:bg-gray-800"
                    }`}
                    onClick={() => {
                      setSelectedOrientation("vertical");
                      // Update template to match orientation
                      if (selectedTemplate.startsWith("horizontal")) {
                        const styleNumber = selectedTemplate.replace(
                          "horizontal",
                          ""
                        );
                        setSelectedTemplate(`vertical${styleNumber}`);
                      }
                    }}
                  >
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="20"
                          height="20"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="mr-1.5 text-purple-600"
                        >
                          <rect
                            x="3"
                            y="3"
                            width="18"
                            height="18"
                            rx="2"
                            ry="2"
                          ></rect>
                          <line x1="8" y1="3" x2="8" y2="21"></line>
                          <line x1="16" y1="3" x2="16" y2="21"></line>
                        </svg>
                        <span className="font-medium text-sm">
                          Faktur Kecil
                        </span>
                      </div>
                      <div className="w-full h-12 bg-white border border-gray-200 rounded-md flex items-center justify-center relative">
                        <div className="w-full h-full p-2 flex flex-col justify-center">
                          <div className="h-1.5 bg-gray-200 w-full mb-1 rounded"></div>
                          <div className="flex flex-col items-center">
                            <div className="w-3/4">
                              <div className="h-1 bg-gray-300 w-full mb-0.5 rounded"></div>
                              <div className="h-1 bg-gray-300 w-full mb-0.5 rounded"></div>
                            </div>
                            <div className="w-full">
                              <div className="h-1 bg-gray-200 w-full rounded"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="text-xs text-gray-500 mt-1.5">
                        Thermal Printer (58mm/80mm)
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Template Style Options */}
              <div className="space-y-3">
                <div className="font-medium">Gaya Template</div>
                <Select
                  value={selectedTemplate}
                  onValueChange={(value) => {
                    setSelectedTemplate(value);
                  }}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Pilih template" />
                  </SelectTrigger>
                  <SelectContent>
                    {selectedOrientation === "horizontal" ? (
                      <SelectGroup>
                        <SelectLabel>Template Faktur Lebar (A4)</SelectLabel>
                        <SelectItem value="horizontal1">Template 1</SelectItem>
                        <SelectItem value="horizontal2">Template 2</SelectItem>
                        <SelectItem value="horizontal3">Template 3</SelectItem>
                      </SelectGroup>
                    ) : (
                      <SelectGroup>
                        <SelectLabel>
                          Template Faktur Kecil (Thermal)
                        </SelectLabel>
                        <SelectItem value="vertical1">Template 1</SelectItem>
                        <SelectItem value="vertical2">Template 2</SelectItem>
                        <SelectItem value="vertical3">Template 3</SelectItem>
                      </SelectGroup>
                    )}
                  </SelectContent>
                </Select>
              </div>

              {/* Action Buttons */}
              <div className="mt-auto pt-6">
                <div className="flex flex-col gap-3">
                  <Button onClick={handlePrint} className="w-full">
                    Cetak Sekarang
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setIsPrintDialogOpen(false)}
                    className="w-full"
                  >
                    Batal
                  </Button>
                </div>
              </div>
            </div>

            {/* Right Column - Preview */}
            <div className="border rounded-md p-4 overflow-hidden">
              <div className="font-medium mb-3 flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="mr-2 text-gray-500"
                >
                  <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                  <circle cx="12" cy="12" r="3"></circle>
                </svg>
                Pratinjau Template
              </div>
              <div
                className={`${
                  selectedOrientation === "horizontal"
                    ? "aspect-[16/9]"
                    : "aspect-[3/4]"
                } bg-gray-100 rounded-md flex items-center justify-center overflow-hidden border border-dashed border-gray-300 min-h-[400px] max-w-full`}
              >
                {/* Image placeholder for template preview */}
                <div className="w-full h-full relative flex flex-col items-center justify-center overflow-hidden">
                  {/* Template image will be placed here */}
                  <div className="text-gray-400 flex flex-col items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="64"
                      height="64"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="1"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="mb-4"
                    >
                      <rect
                        x="3"
                        y="3"
                        width="18"
                        height="18"
                        rx="2"
                        ry="2"
                      ></rect>
                      <circle cx="8.5" cy="8.5" r="1.5"></circle>
                      <polyline points="21,15 16,10 5,21"></polyline>
                    </svg>
                    <div className="text-sm font-medium mb-1">
                      Pratinjau Template
                    </div>
                    <div className="text-xs text-center max-w-[200px]">
                      Template {selectedTemplate} akan ditampilkan di sini
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
