import { ColumnVisibility } from "../types";

export interface ColumnConfig {
  key: keyof ColumnVisibility;
  label: string;
  sortKey: string;
}

// Sales column configuration with order
export const salesColumnConfig: ColumnConfig[] = [
  { key: "id", label: "No. Transaksi", sortKey: "id" },
  { key: "date", label: "Tanggal", sortKey: "saleDate" },
  { key: "invoiceRef", label: "No. Faktur", sortKey: "invoiceRef" },
  {
    key: "paymentDueDate",
    label: "Tgl. Jatuh Tempo",
    sortKey: "paymentDueDate",
  },
  { key: "customer", label: "Pelanggan", sortKey: "customer" },
  { key: "totalAmount", label: "Total", sortKey: "totalAmount" },
  { key: "itemCount", label: "Jumlah Item", sortKey: "itemCount" },
  { key: "totalQuantity", label: "Qty", sortKey: "totalQuantity" }, // New column
  { key: "status", label: "Status", sortKey: "status" }, // New status column
  { key: "tags", label: "Tag", sortKey: "tags" },
];
