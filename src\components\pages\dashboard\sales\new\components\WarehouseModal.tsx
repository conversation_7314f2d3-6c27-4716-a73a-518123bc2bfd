import React, { useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON>ton } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Form,
} from "@/components/ui/form";
import { Warehouse, WarehouseFormData } from "@/types/warehouse";
import { createWarehouse } from "@/actions/entities/warehouses";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";

// Warehouse form schema for the dialog
const warehouseSchema = z.object({
  name: z.string().min(1, "Nama gudang wajib diisi"),
  description: z.string().optional(),
  address: z.string().optional(),
  phone: z.string().optional(),
  email: z
    .string()
    .email("Format email tidak valid")
    .optional()
    .or(z.literal("")),
  contactName: z.string().optional(),
  isActive: z.boolean().default(true),
  isDefault: z.boolean().default(false),
});

interface WarehouseModalProps {
  isOpen: boolean;
  onClose: () => void;
  onWarehouseCreated: (newWarehouse: Warehouse) => void;
}

export const WarehouseModal: React.FC<WarehouseModalProps> = ({
  isOpen,
  onClose,
  onWarehouseCreated,
}) => {
  const [isCreatingWarehouse, setIsCreatingWarehouse] = useState(false);

  // Warehouse form for dialog
  const warehouseForm = useForm<WarehouseFormData>({
    resolver: zodResolver(warehouseSchema),
    defaultValues: {
      name: "",
      description: "",
      address: "",
      phone: "",
      email: "",
      contactName: "",
      isActive: true,
      isDefault: false,
    },
  });

  // Warehouse creation function
  const handleCreateWarehouse = async (data: WarehouseFormData) => {
    setIsCreatingWarehouse(true);
    try {
      const result = await createWarehouse(data);

      if (result) {
        toast.success("Gudang berhasil dibuat");
        warehouseForm.reset();
        onClose();

        // Add the new warehouse to the list
        const newWarehouse: Warehouse = {
          id: result.id,
          name: result.name,
          description: result.description || null,
          address: result.address || null,
          phone: result.phone || null,
          email: result.email || null,
          contactName: result.contactName || null,
          isActive: result.isActive,
          isDefault: result.isDefault,
          createdAt: result.createdAt,
          updatedAt: result.updatedAt,
          userId: result.userId,
        };

        onWarehouseCreated(newWarehouse);
      } else {
        toast.error("Gagal membuat gudang");
      }
    } catch (error) {
      console.error("Error creating warehouse:", error);
      toast.error("Terjadi kesalahan saat membuat gudang");
    } finally {
      setIsCreatingWarehouse(false);
    }
  };

  // Wrapper function to handle warehouse form submission with event prevention
  const handleWarehouseFormSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    event.stopPropagation();
    warehouseForm.handleSubmit(handleCreateWarehouse)(event);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px] max-h-[80vh] overflow-y-auto rounded-md">
        <DialogHeader>
          <DialogTitle>Tambah Gudang Baru</DialogTitle>
          <DialogDescription>
            Buat gudang baru untuk transaksi ini.
          </DialogDescription>
        </DialogHeader>
        <Form {...warehouseForm}>
          <form onSubmit={handleWarehouseFormSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Warehouse Name */}
              <FormField
                control={warehouseForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel>
                      Nama Gudang <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Masukkan nama gudang"
                        {...field}
                        disabled={isCreatingWarehouse}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Description */}
              <FormField
                control={warehouseForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel>Deskripsi</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Deskripsi gudang (opsional)"
                        {...field}
                        disabled={isCreatingWarehouse}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Contact Name */}
              <FormField
                control={warehouseForm.control}
                name="contactName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nama Kontak</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Nama kontak (opsional)"
                        {...field}
                        disabled={isCreatingWarehouse}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Phone */}
              <FormField
                control={warehouseForm.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nomor Telepon</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Nomor telepon (opsional)"
                        {...field}
                        disabled={isCreatingWarehouse}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Email */}
              <FormField
                control={warehouseForm.control}
                name="email"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="Email (opsional)"
                        {...field}
                        disabled={isCreatingWarehouse}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Address */}
              <FormField
                control={warehouseForm.control}
                name="address"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel>Alamat</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Alamat gudang (opsional)"
                        {...field}
                        disabled={isCreatingWarehouse}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={(e) => {
                  e.stopPropagation();
                  onClose();
                }}
                disabled={isCreatingWarehouse}
              >
                Batal
              </Button>
              <Button type="submit" disabled={isCreatingWarehouse}>
                {isCreatingWarehouse && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                {isCreatingWarehouse ? "Membuat..." : "Buat Gudang"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
