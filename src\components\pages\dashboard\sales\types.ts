export interface SaleItem {
  id: string;
  quantity: number;
  priceAtSale: number;
  saleId: string;
  productId: string;
  createdAt: Date | string;
  updatedAt: Date | string;
  // New discount and metadata fields
  discountPercentage?: number;
  discountAmount?: number;
  eventDiscountId?: string;
  eventDiscountName?: string;
  isWholesale?: boolean;
  unit?: string;
  tax?: string;
  product: {
    name: string;
  };
}

// Company info type for user's company information
export interface CompanyInfo {
  companyName: string | null;
  companyAddress: string | null;
  companyPhone: string | null;
  companyEmail: string | null;
}

import { TransactionPaymentStatus as PrismaStatus } from "@prisma/client";

export type TransactionPaymentStatus = PrismaStatus;
export const TransactionPaymentStatus = {
  LUNAS: "LUNAS" as const,
  BELUM_LUNAS: "BELUM_LUNAS" as const,
};

export interface Sale {
  id: string;
  saleDate: Date | string;
  totalAmount: number;
  transactionNumber?: string | null;
  invoiceRef?: string | null;
  isDraft: boolean;
  isPublic?: boolean;
  status: TransactionPaymentStatus;
  createdAt: Date | string;
  updatedAt: Date | string;
  userId: string;
  user?: {
    id: string;
    name: string | null;
    username: string | null;
  };
  employeeId?: string | null;
  Employee?: {
    id: string;
    name: string;
    employeeId: string;
    role: string;
  } | null;
  items: SaleItem[];
  // Customer relationship
  customerId?: string | null;
  customer?: {
    id: string;
    name: string;
    email?: string;
    phone?: string;
    NIK?: string;
    NPWP?: string;
  };
  // Company info from BusinessInfo
  companyInfo?: CompanyInfo | null;
  // Warehouse relationship
  warehouseId?: string | null;
  warehouse?: {
    id: string;
    name: string;
  };
  // Additional fields from schema
  customerRefNumber?: string;
  shippingAddress?: string;
  paymentDueDate?: Date | string;
  paymentTerms?: string;
  tags?: string[];
  memo?: string;
  lampiran?: { url: string; filename: string }[];
  priceIncludesTax?: boolean;
}

export interface SaleCounts {
  total: number;
  today: number;
  thisMonth: number;
  pending: number;
  drafts: number;
}

export interface ColumnVisibility {
  id: boolean;
  date: boolean;
  paymentDueDate: boolean;
  customer: boolean;
  totalAmount: boolean;
  itemCount: boolean;
  invoiceRef: boolean;
  tags: boolean;
  totalQuantity: boolean; // New column for sum of quantity
  status: boolean; // New column for payment status
}
