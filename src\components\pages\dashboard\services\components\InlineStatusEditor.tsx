"use client";

import React, { useState, useTransition } from "react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { LoaderCircle } from "lucide-react";
import {
  ServiceStatus,
  getServiceStatusDisplayText,
  getServiceStatusOptions,
} from "../types";
import { updateServiceStatus } from "@/actions/entities/services";
import {
  getServiceStatusBadge,
  getServiceStatusSelectItemClasses,
} from "@/utils/serviceStatusColors";

interface InlineStatusEditorProps {
  serviceId: string;
  currentStatus: ServiceStatus;
  disabled?: boolean;
}

export const InlineStatusEditor: React.FC<InlineStatusEditorProps> = ({
  serviceId,
  currentStatus,
  disabled = false,
}) => {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [isEditing, setIsEditing] = useState(false);

  const statusOptions = getServiceStatusOptions();

  const handleStatusChange = (newStatus: ServiceStatus) => {
    if (newStatus === currentStatus) {
      setIsEditing(false);
      return;
    }

    startTransition(async () => {
      try {
        const result = await updateServiceStatus(
          serviceId,
          newStatus,
          `Status diubah dari ${getServiceStatusDisplayText(currentStatus)} ke ${getServiceStatusDisplayText(newStatus)}`
        );

        if (result.success) {
          toast.success(result.success);
          router.refresh();
        } else if (result.error) {
          toast.error(result.error);
        }
      } catch (error) {
        console.error("Error updating service status:", error);
        toast.error("Gagal mengubah status servis.");
      } finally {
        setIsEditing(false);
      }
    });
  };

  // Remove the old badge variant function - now using color utility

  if (disabled || isPending) {
    return (
      <div className="flex items-center gap-2">
        <Badge className={`${getServiceStatusBadge(currentStatus)} border-0`}>
          {getServiceStatusDisplayText(currentStatus)}
        </Badge>
        {isPending && <LoaderCircle className="h-4 w-4 animate-spin" />}
      </div>
    );
  }

  if (isEditing) {
    return (
      <Select
        value={currentStatus}
        onValueChange={(value) => handleStatusChange(value as ServiceStatus)}
        onOpenChange={(open) => {
          if (!open) {
            setIsEditing(false);
          }
        }}
        defaultOpen={true}
      >
        <SelectTrigger className="w-[200px] h-8">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {statusOptions.map((option) => (
            <SelectItem
              key={option.value}
              value={option.value}
              className={getServiceStatusSelectItemClasses(
                option.value as ServiceStatus
              )}
            >
              <div className="flex items-center gap-2">
                <div
                  className={`w-3 h-3 rounded-full ${getServiceStatusBadge(option.value as ServiceStatus)}`}
                />
                {option.label}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    );
  }

  return (
    <Badge
      className={`${getServiceStatusBadge(currentStatus)} border-0 cursor-pointer hover:opacity-80 hover:bg-primary/10 transition-opacity`}
      onClick={() => setIsEditing(true)}
    >
      {getServiceStatusDisplayText(currentStatus)}
    </Badge>
  );
};
