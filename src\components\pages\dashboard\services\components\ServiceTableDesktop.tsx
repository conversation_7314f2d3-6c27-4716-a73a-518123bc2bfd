import React, { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import {
  Service,
  ColumnVisibility,
  ServiceStatus,
  getServiceStatusDisplayText,
} from "../types";
import {
  Trash,
  LoaderCircle,
  Printer,
  Edit,
  Share2,
  Eye,
  Pencil,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { deleteService } from "@/actions/entities/services";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { formatDate } from "@/lib/utils";
import { servicesColumnConfig } from "../config/columnConfig";
import { InlineStatusEditor } from "./InlineStatusEditor";

interface ServiceTableDesktopProps {
  services: Service[];
  columnVisibility: ColumnVisibility;
  handleSort: (field: string) => void;
  getSortIcon: (field: string) => React.ReactNode;
  getStatusBadge: (status: ServiceStatus) => React.ReactNode;
  searchTerm: string;
}

export const ServiceTableDesktop: React.FC<ServiceTableDesktopProps> = ({
  services,
  columnVisibility,
  handleSort,
  getSortIcon,
  getStatusBadge,
  searchTerm,
}) => {
  const router = useRouter();
  const [serviceToDelete, setServiceToDelete] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isPrintDialogOpen, setIsPrintDialogOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] =
    useState<string>("horizontal1");
  const [serviceToPrint, setServiceToPrint] = useState<Service | null>(null);

  // Handle delete service
  const handleDeleteService = async (id: string) => {
    console.log(`[ServiceTable] Starting delete process for service ID: ${id}`);
    setServiceToDelete(id);
    setIsDeleting(true);
    try {
      console.log(`[ServiceTable] Calling deleteService action for ID: ${id}`);
      const result = await deleteService(id);
      console.log(`[ServiceTable] Delete result:`, result);

      if (result.success) {
        console.log(`[ServiceTable] Delete successful, showing success toast`);
        toast.success(result.success);
        console.log(`[ServiceTable] Calling router.refresh()`);
        // Refresh the page to show updated data
        router.refresh();
      } else if (result.error) {
        console.log(`[ServiceTable] Delete failed with error:`, result.error);
        toast.error(result.error);
      }
    } catch (error) {
      console.error("[ServiceTable] Exception during delete:", error);
      toast.error("Terjadi kesalahan saat menghapus servis.");
    } finally {
      console.log(`[ServiceTable] Cleaning up delete state for ID: ${id}`);
      setIsDeleting(false);
      setServiceToDelete(null);
    }
  };
  if (services.length === 0) {
    return (
      <div className="rounded-md border border-dashed p-8 text-center">
        <h3 className="text-lg font-medium">Tidak ada data servis</h3>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          {searchTerm
            ? `Tidak ada hasil untuk pencarian "${searchTerm}"`
            : "Belum ada data servis yang tersedia. Tambahkan servis baru untuk memulai."}
        </p>
      </div>
    );
  }

  // Helper function to render cell content based on column key
  const renderCellContent = (
    service: Service,
    columnKey: keyof ColumnVisibility
  ) => {
    switch (columnKey) {
      case "serviceNumber":
        return (
          <Link
            href={`/dashboard/services/management/detail/${service.serviceNumber}`}
            className="hover:text-blue-600 text-blue-500 dark:hover:text-blue-400 cursor-pointer underline"
          >
            {service.serviceNumber}
          </Link>
        );
      case "customerName":
        return service.customerName;
      case "customerPhone":
        return service.customerPhone;
      case "deviceType":
        return service.deviceType;
      case "deviceBrand":
        return service.deviceBrand;
      case "deviceModel":
        return service.deviceModel;
      case "deviceSerialNumber":
        return service.deviceSerialNumber || "-";
      case "status":
        return (
          <InlineStatusEditor
            serviceId={service.id}
            currentStatus={service.status}
          />
        );
      case "receivedDate":
        return formatDate(service.receivedDate);
      case "estimatedCompletionDate":
        return service.estimatedCompletionDate
          ? formatDate(service.estimatedCompletionDate)
          : "-";
      case "estimatedCost":
        return service.estimatedCost
          ? `Rp ${service.estimatedCost.toLocaleString("id-ID")}`
          : "-";
      case "finalCost":
        return service.finalCost
          ? `Rp ${service.finalCost.toLocaleString("id-ID")}`
          : "-";
      case "warrantyPeriod":
        return service.warrantyPeriod ? `${service.warrantyPeriod} hari` : "-";
      default:
        return "-";
    }
  };

  // Handle print service - open dialog
  const handlePrintService = (service: Service) => {
    setServiceToPrint(service);
    setIsPrintDialogOpen(true);
  };

  // Handle actual print after template selection
  const handlePrint = () => {
    if (!serviceToPrint) return;

    // Close the dialog
    setIsPrintDialogOpen(false);

    // Create a new window for printing
    const printWindow = window.open("", "_blank");
    if (!printWindow) {
      toast.error(
        "Popup diblokir oleh browser. Mohon izinkan popup untuk mencetak faktur."
      );
      return;
    }

    // Dynamically import the service templates
    import("../templates")
      .then(({ getServiceInvoiceTemplate }) => {
        // Use the selected template
        const templateContent = getServiceInvoiceTemplate(
          selectedTemplate as any,
          serviceToPrint
        );

        // Write the template content to the new window
        printWindow.document.write(templateContent);
        printWindow.document.close();

        // Wait for content to load then print
        printWindow.onload = () => {
          printWindow.print();
          // printWindow.close(); // Uncomment to auto-close after print dialog
        };
      })
      .catch((error) => {
        console.error("Error loading service templates:", error);
        toast.error("Terjadi kesalahan saat memuat template faktur servis.");
        printWindow.close();
      });
  };

  // Handle share service (placeholder for future implementation)
  const handleShareService = (service: Service) => {
    toast.info("Fitur share untuk servis akan segera hadir!");
  };

  return (
    <div className="relative overflow-x-auto bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700 rounded-lg">
      <table className="w-full text-sm text-left text-gray-500 dark:text-gray-400">
        <thead className="sticky top-0 z-10 text-xs text-gray-700 dark:text-gray-300 uppercase bg-white shadow-[0_2px_4px_rgba(0,0,0,0.05)] dark:bg-gray-700">
          <tr>
            {servicesColumnConfig.map(
              (column) =>
                columnVisibility[column.key] && (
                  <th
                    key={column.key}
                    scope="col"
                    className="px-4 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 border-r border-gray-200 dark:border-gray-700"
                    onClick={() => handleSort(column.sortKey)}
                  >
                    <div className="flex items-center">
                      {column.label} {getSortIcon(column.sortKey)}
                    </div>
                  </th>
                )
            )}
            <th scope="col" className="px-6 py-3 text-right">
              Aksi
            </th>
          </tr>
        </thead>
        <tbody>
          {services.length > 0 ? (
            services.map((service) => (
              <tr
                key={service.id}
                className="bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                {servicesColumnConfig.map(
                  (column) =>
                    columnVisibility[column.key] && (
                      <td
                        key={column.key}
                        className={`px-4 py-4 whitespace-nowrap border-r border-gray-200 dark:border-gray-700 ${
                          column.key === "serviceNumber"
                            ? "font-medium text-gray-900 dark:text-gray-100"
                            : "text-gray-500 dark:text-gray-400"
                        }`}
                      >
                        {renderCellContent(service, column.key)}
                      </td>
                    )
                )}

                {/* Action Buttons */}
                <td className="px-6 py-4 text-right">
                  <div className="flex items-center justify-end gap-2">
                    {/* Action Button */}
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 bg-yellow-300 text-black hover:text-gray-700 cursor-pointer hover:bg-yellow-200"
                          onClick={() => handlePrintService(service)}
                        >
                          <Printer className="h-4 w-4" />
                          <span className="sr-only">Print Invoice</span>
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Aksi</p>
                      </TooltipContent>
                    </Tooltip>

                    {/* Share Button */}
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 bg-green-500 text-white cursor-pointer hover:bg-green-400"
                          onClick={() => handleShareService(service)}
                        >
                          <Share2 className="h-4 w-4" />
                          <span className="sr-only">Share</span>
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Bagikan</p>
                      </TooltipContent>
                    </Tooltip>

                    {/* Edit Button */}
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 bg-blue-500 text-white cursor-pointer hover:bg-blue-400"
                          onClick={() =>
                            router.push(
                              `/dashboard/services/management/edit/${service.serviceNumber}`
                            )
                          }
                        >
                          <Edit className="h-4 w-4" />
                          <span className="sr-only">Edit</span>
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Edit</p>
                      </TooltipContent>
                    </Tooltip>

                    {/* Delete Button */}
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 bg-red-500 text-white cursor-pointer hover:bg-red-400"
                          disabled={
                            isDeleting && serviceToDelete === service.id
                          }
                          data-deleting={
                            isDeleting && serviceToDelete === service.id
                          }
                          onClick={() => {
                            console.log(
                              `[ServiceTable] Delete button clicked for service ID: ${service.id}`
                            );
                          }}
                        >
                          {isDeleting && serviceToDelete === service.id ? (
                            <LoaderCircle className="h-4 w-4 animate-spin" />
                          ) : (
                            <Trash className="h-4 w-4" />
                          )}
                          <span className="sr-only">Delete</span>
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Hapus Servis</AlertDialogTitle>
                          <AlertDialogDescription>
                            Apakah Anda yakin ingin menghapus servis{" "}
                            <strong>{service.serviceNumber}</strong>? Tindakan
                            ini tidak dapat dibatalkan.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Batal</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => {
                              console.log(
                                `[ServiceTable] AlertDialogAction clicked for service ID: ${service.id}`
                              );
                              handleDeleteService(service.id);
                            }}
                            disabled={
                              isDeleting && serviceToDelete === service.id
                            }
                            className="bg-red-600 hover:bg-red-700"
                          >
                            {isDeleting && serviceToDelete === service.id ? (
                              <div className="flex items-center gap-2">
                                <LoaderCircle className="h-4 w-4 animate-spin" />
                                Menghapus...
                              </div>
                            ) : (
                              "Hapus"
                            )}
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </td>
              </tr>
            ))
          ) : (
            <tr>
              <td
                colSpan={servicesColumnConfig.length + 1}
                className="px-6 py-4 text-center text-gray-500 dark:text-gray-400"
              >
                Tidak ada data servis yang tersedia
              </td>
            </tr>
          )}
        </tbody>
      </table>

      {/* Print Dialog */}
      <Dialog open={isPrintDialogOpen} onOpenChange={setIsPrintDialogOpen}>
        <DialogContent className="max-h-[80vh] h-[80vh] overflow-y-auto max-w-[90vw] w-[900px] overflow-x-hidden">
          <DialogHeader>
            <DialogTitle>Cetak Faktur Servis</DialogTitle>
            <DialogDescription>
              Pilih template faktur servis yang ingin Anda cetak.
            </DialogDescription>
          </DialogHeader>

          <div className="flex flex-col h-full">
            {/* Template Selection */}
            <div className="flex-1 overflow-y-auto">
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium mb-3">Pilih Template</h3>
                  <div className="grid grid-cols-1 gap-3">
                    {/* Horizontal Template 1 */}
                    <div
                      className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                        selectedTemplate === "horizontal1"
                          ? "border-blue-500 bg-blue-50 dark:bg-blue-950"
                          : "border-gray-200 hover:border-gray-300"
                      }`}
                      onClick={() => setSelectedTemplate("horizontal1")}
                    >
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0">
                          <div className="w-16 h-12 bg-gray-100 dark:bg-gray-800 rounded border flex items-center justify-center">
                            <div className="text-xs text-gray-500">A4</div>
                          </div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2">
                            <h4 className="text-sm font-medium">
                              Template Horizontal 1
                            </h4>
                            {selectedTemplate === "horizontal1" && (
                              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                            )}
                          </div>
                          <p className="text-xs text-gray-500 mt-1">
                            Template profesional dengan orientasi landscape,
                            cocok untuk detail servis lengkap
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="mt-auto pt-6">
              <div className="flex flex-col gap-3">
                <Button onClick={handlePrint} className="w-full">
                  Cetak Sekarang
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setIsPrintDialogOpen(false)}
                  className="w-full"
                >
                  Batal
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
