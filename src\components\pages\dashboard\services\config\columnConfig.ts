import { ColumnVisibility } from "../types";

export interface ServiceColumnConfig {
  key: keyof ColumnVisibility;
  label: string;
  sortKey: string;
}

// Services column configuration with order (matching the actual table structure)
export const servicesColumnConfig: ServiceColumnConfig[] = [
  { key: "serviceNumber", label: "No. Servis", sortKey: "serviceNumber" },
  { key: "customerName", label: "Pelanggan", sortKey: "customerName" },
  { key: "customerPhone", label: "Telepon", sortKey: "customerPhone" },
  { key: "deviceType", label: "Tipe", sortKey: "deviceType" },
  { key: "deviceBrand", label: "Merek", sortKey: "deviceBrand" },
  { key: "deviceModel", label: "Model", sortKey: "deviceModel" },
  {
    key: "deviceSerialNumber",
    label: "Nomor Seri",
    sortKey: "deviceSerialNumber",
  },
  { key: "status", label: "Status", sortKey: "status" },
  { key: "receivedDate", label: "<PERSON><PERSON>", sortKey: "receivedDate" },
  {
    key: "estimatedCompletionDate",
    label: "Estimasi Selesai",
    sortKey: "estimatedCompletionDate",
  },
  { key: "estimatedCost", label: "Estimasi Biaya", sortKey: "estimatedCost" },
  { key: "finalCost", label: "Biaya Final", sortKey: "finalCost" },
  { key: "warrantyPeriod", label: "Masa Garansi", sortKey: "warrantyPeriod" },
];

// Default column visibility for services
export const getDefaultServiceColumnVisibility = (): ColumnVisibility => ({
  serviceNumber: true,
  customerName: true,
  customerPhone: true,
  deviceType: true,
  deviceBrand: true,
  deviceModel: false,
  deviceSerialNumber: false,
  status: true,
  receivedDate: true,
  estimatedCompletionDate: false,
  estimatedCost: false,
  finalCost: false,
  warrantyPeriod: false,
});
