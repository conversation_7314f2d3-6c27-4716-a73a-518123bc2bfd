"use client";

import React from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import DashboardLayout from "@/components/layout/dashboardlayout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ArrowLeft,
  Pencil,
  Trash,
  Eye,
  Printer,
  Clock,
  FileText,
} from "lucide-react";
import { Service, ServiceStatus, DeviceType } from "../types";
import { formatDetailedDate } from "@/lib/utils";
import { getServiceInvoiceTemplate, type TemplateType } from "../templates";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { deleteService } from "@/actions/entities/services";

interface ServiceDetailPageProps {
  service: Service;
}

// Helper function to get file type from filename
const getFileType = (filename: string): string => {
  const extension = filename.split(".").pop()?.toLowerCase();
  switch (extension) {
    case "pdf":
      return "PDF Document";
    case "doc":
    case "docx":
      return "Word Document";
    case "xls":
    case "xlsx":
      return "Excel Spreadsheet";
    case "jpg":
    case "jpeg":
    case "png":
    case "gif":
      return "Image";
    case "txt":
      return "Text File";
    default:
      return "Document";
  }
};

// Helper function to get file size (placeholder - would need actual implementation)
const getFileSize = (url: string): string => {
  // This is a placeholder. In a real implementation, you'd fetch the file size
  // from the server or store it when uploading
  return "Unknown size";
};

const ServiceDetailPage: React.FC<ServiceDetailPageProps> = ({ service }) => {
  const router = useRouter();
  const [isDeleting, setIsDeleting] = React.useState(false);
  const [isPrintDialogOpen, setIsPrintDialogOpen] = React.useState(false);
  const [selectedTemplate, setSelectedTemplate] =
    React.useState<TemplateType>("horizontal1");
  const [selectedOrientation, setSelectedOrientation] = React.useState<
    "horizontal" | "vertical"
  >("horizontal");

  // Handle file download
  const handleDownloadFile = (file: { url: string; filename: string }) => {
    const link = document.createElement("a");
    link.href = file.url;
    link.download = file.filename;
    link.target = "_blank";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Handle file view
  const handleViewFile = (file: { url: string; filename: string }) => {
    window.open(file.url, "_blank");
  };

  // Handle print invoice
  const handlePrint = () => {
    if (!service) return;

    // Close the dialog
    setIsPrintDialogOpen(false);

    // Create a new window for printing
    const printWindow = window.open("", "_blank");
    if (!printWindow) {
      toast.error(
        "Popup diblokir oleh browser. Mohon izinkan popup untuk mencetak faktur."
      );
      return;
    }

    // Import the template dynamically to avoid bundling all templates
    import("../templates")
      .then(({ getServiceInvoiceTemplate }) => {
        // Use the selected template directly
        const templateContent = getServiceInvoiceTemplate(
          selectedTemplate as any,
          service
        );

        // Write the template content to the new window using a more modern approach
        const parser = new DOMParser();
        const htmlDoc = parser.parseFromString(templateContent, "text/html");

        // Clear the document and append the new content
        printWindow.document.documentElement.innerHTML = "";
        Array.from(htmlDoc.head.childNodes).forEach((node) => {
          printWindow.document.head.appendChild(
            printWindow.document.importNode(node, true)
          );
        });
        Array.from(htmlDoc.body.childNodes).forEach((node) => {
          printWindow.document.body.appendChild(
            printWindow.document.importNode(node, true)
          );
        });

        // Wait for images and resources to load before printing
        printWindow.onload = () => {
          printWindow.print();
          // printWindow.close(); // Uncomment to auto-close after print dialog
        };
      })
      .catch((error) => {
        console.error("Error loading templates:", error);
        toast.error("Terjadi kesalahan saat memuat template faktur.");
        printWindow.close();
      });
  };

  // Handle preview invoice
  const handlePreviewInvoice = () => {
    try {
      const htmlContent = getServiceInvoiceTemplate(selectedTemplate, service);
      const previewWindow = window.open("", "_blank");

      if (previewWindow) {
        previewWindow.document.write(htmlContent);
        previewWindow.document.close();
        toast.success("Preview faktur servis berhasil dibuka!");
      } else {
        toast.error("Gagal membuka preview. Pastikan popup tidak diblokir.");
      }
    } catch (error) {
      console.error("Error previewing invoice:", error);
      toast.error("Gagal membuka preview faktur servis.");
    }
  };

  // Function to get device type label
  const getDeviceTypeLabel = (type: DeviceType) => {
    switch (type) {
      case DeviceType.LAPTOP:
        return "Laptop";
      case DeviceType.DESKTOP:
        return "Desktop";
      case DeviceType.PHONE:
        return "Smartphone";
      case DeviceType.TABLET:
        return "Tablet";
      case DeviceType.PRINTER:
        return "Printer";
      case DeviceType.OTHER:
        return "Lainnya";
      default:
        return "Tidak Diketahui";
    }
  };

  // Function to get status badge
  const getStatusBadge = (status: ServiceStatus) => {
    switch (status) {
      case ServiceStatus.DITERIMA:
        return (
          <Badge variant="default" className="bg-blue-500">
            <Clock className="h-3 w-3 mr-1" />
            Diterima
          </Badge>
        );
      case ServiceStatus.PROSES_MENUNGGU_SPAREPART:
        return (
          <Badge variant="default" className="bg-amber-500">
            <Clock className="h-3 w-3 mr-1" />
            Proses/Menunggu Sparepart
          </Badge>
        );
      case ServiceStatus.SELESAI_BELUM_DIAMBIL:
        return (
          <Badge variant="default" className="bg-purple-500">
            <Clock className="h-3 w-3 mr-1" />
            Selesai & Belum Diambil
          </Badge>
        );
      case ServiceStatus.SELESAI_SUDAH_DIAMBIL:
        return (
          <Badge variant="default" className="bg-green-500">
            <Clock className="h-3 w-3 mr-1" />
            Selesai & Sudah Diambil
          </Badge>
        );
      default:
        return (
          <Badge variant="default" className="bg-gray-500">
            <Clock className="h-3 w-3 mr-1" />
            Tidak Diketahui
          </Badge>
        );
    }
  };

  // Handle delete service
  const handleDeleteService = async () => {
    setIsDeleting(true);
    try {
      const result = await deleteService(service.id);
      if (result.success) {
        toast.success(result.success);
        router.push("/dashboard/services/management");
      } else if (result.error) {
        toast.error(result.error);
      }
    } catch (error) {
      console.error("Error deleting service:", error);
      toast.error("Terjadi kesalahan saat menghapus servis.");
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="w-full px-4 py-6">
        {/* Back button and Edit button */}
        <div className="flex justify-between items-center mb-6">
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-2 cursor-pointer"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4" />
            Kembali
          </Button>

          <Button
            asChild
            variant="default"
            size="sm"
            className="flex items-center gap-2"
          >
            <Link
              href={`/dashboard/services/management/edit/${service.serviceNumber}`}
            >
              <Pencil className="h-4 w-4" />
              Edit Servis
            </Link>
          </Button>
        </div>

        <Card>
          <CardHeader>
            <div className="flex justify-between items-start">
              <div>
                <div className="flex items-center gap-3 mb-2">
                  <CardTitle className="text-2xl font-bold tracking-tight">
                    Servis #{service.serviceNumber}
                  </CardTitle>
                </div>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  {new Date(service.receivedDate).toLocaleDateString("id-ID", {
                    day: "numeric",
                    month: "long",
                    year: "numeric",
                    hour: "2-digit",
                    minute: "2-digit",
                  })}
                </p>
              </div>
              <div className="flex items-center gap-2">
                {getStatusBadge(service.status)}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="details" className="w-full">
              <TabsList className="grid w-full grid-cols-2 mb-6">
                <TabsTrigger value="details">Detail Servis</TabsTrigger>
                <TabsTrigger value="history">Riwayat Status</TabsTrigger>
              </TabsList>

              <TabsContent value="details" className="space-y-6">
                {/* Service Details */}
                <div>
                  <h3 className="text-lg font-medium mb-3">Informasi Servis</h3>

                  {/* Main Service Information - table-like format */}
                  <div className="grid md:grid-cols-2 gap-6 text-sm mb-6">
                    {/* Left Column */}
                    <div className="space-y-2">
                      <div className="grid grid-cols-2 gap-x-4 gap-y-2">
                        <div className="font-medium">No. Servis</div>
                        <div>{service.serviceNumber}</div>

                        <div className="font-medium">Tanggal Masuk</div>
                        <div>
                          {new Date(service.receivedDate).toLocaleDateString(
                            "id-ID",
                            {
                              day: "numeric",
                              month: "long",
                              year: "numeric",
                            }
                          )}
                        </div>

                        <div className="font-medium">Nama Pelanggan</div>
                        <div>{service.customerName}</div>

                        <div className="font-medium">No. HP Pelanggan</div>
                        <div>{service.customerPhone}</div>
                      </div>
                    </div>

                    {/* Right Column */}
                    <div className="space-y-2">
                      <div className="grid grid-cols-2 gap-x-4 gap-y-2">
                        <div className="font-medium">Status</div>
                        <div>{getStatusBadge(service.status)}</div>

                        <div className="font-medium">Perangkat</div>
                        <div>
                          {service.deviceBrand} {service.deviceModel}
                        </div>

                        <div className="font-medium">Email Pelanggan</div>
                        <div>{service.customerEmail || "-"}</div>

                        <div className="font-medium">Waktu</div>
                        <div>
                          {new Date(service.receivedDate).toLocaleTimeString(
                            "id-ID",
                            {
                              hour: "2-digit",
                              minute: "2-digit",
                            }
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <Separator />

                {/* Problem Description */}
                <div>
                  <h3 className="text-lg font-medium mb-3">
                    Deskripsi Masalah
                  </h3>
                  <div className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg">
                    <p className="text-sm">{service.problemDescription}</p>
                  </div>
                </div>

                {service.diagnosisNotes && (
                  <>
                    <Separator />
                    <div>
                      <h3 className="text-lg font-medium mb-3">Diagnosis</h3>
                      <div className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg">
                        <p className="text-sm">{service.diagnosisNotes}</p>
                      </div>
                    </div>
                  </>
                )}

                {service.repairNotes && (
                  <>
                    <Separator />
                    <div>
                      <h3 className="text-lg font-medium mb-3">
                        Catatan Perbaikan
                      </h3>
                      <div className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg">
                        <p className="text-sm">{service.repairNotes}</p>
                      </div>
                    </div>
                  </>
                )}

                {/* Spare Parts */}
                {service.spareParts && service.spareParts.length > 0 && (
                  <>
                    <Separator />
                    <div>
                      <h3 className="text-lg font-medium mb-3">
                        Sparepart yang Digunakan
                      </h3>
                      <div className="overflow-x-auto border border-gray-200 dark:border-gray-700 rounded-lg">
                        <table className="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                          <thead className="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-800 dark:text-gray-300">
                            <tr>
                              <th scope="col" className="px-6 py-3 text-center">
                                No
                              </th>
                              <th scope="col" className="px-6 py-3">
                                Nama Sparepart
                              </th>
                              <th scope="col" className="px-6 py-3">
                                Barcode
                              </th>
                              <th scope="col" className="px-6 py-3 text-center">
                                Kuantitas
                              </th>
                            </tr>
                          </thead>
                          <tbody>
                            {service.spareParts.map((sparePart, index) => (
                              <tr
                                key={sparePart.id}
                                className="bg-white border-b dark:bg-gray-900 dark:border-gray-700"
                              >
                                <td className="px-6 py-4 text-center">
                                  {index + 1}
                                </td>
                                <td className="px-6 py-4 font-medium text-gray-900 dark:text-gray-100">
                                  {sparePart.name}
                                </td>
                                <td className="px-6 py-4">
                                  {sparePart.barcode || "-"}
                                </td>
                                <td className="px-6 py-4 text-center">
                                  <Badge variant="secondary">
                                    {sparePart.quantity}
                                  </Badge>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </>
                )}

                <Separator />

                {/* Cost Information */}
                <div>
                  <h3 className="text-lg font-medium mb-3">Informasi Biaya</h3>
                  <div className="grid md:grid-cols-2 gap-6 text-sm">
                    <div className="space-y-2">
                      <div className="grid grid-cols-2 gap-x-4 gap-y-2">
                        {service.estimatedCost !== undefined && (
                          <>
                            <div className="font-medium">Estimasi Biaya</div>
                            <div>
                              Rp {service.estimatedCost.toLocaleString("id-ID")}
                            </div>
                          </>
                        )}
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="grid grid-cols-2 gap-x-4 gap-y-2">
                        {service.finalCost !== undefined &&
                          service.finalCost > 0 && (
                            <>
                              <div className="font-medium">Biaya Final</div>
                              <div className="font-bold">
                                Rp {service.finalCost.toLocaleString("id-ID")}
                              </div>
                            </>
                          )}
                      </div>
                    </div>
                  </div>
                </div>

                <Separator />

                {/* Action Buttons */}
                <div className="flex flex-wrap gap-2">
                  <Button
                    variant="outline"
                    onClick={handlePreviewInvoice}
                    className="gap-2"
                  >
                    <Eye className="h-4 w-4" />
                    Preview Faktur
                  </Button>
                  <Dialog
                    open={isPrintDialogOpen}
                    onOpenChange={setIsPrintDialogOpen}
                  >
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        className="gap-2 cursor-pointer"
                      >
                        <Printer className="h-4 w-4" />
                        Cetak Faktur
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-h-[80vh] h-[80vh] overflow-y-auto max-w-[90vw] w-[900px] overflow-x-hidden">
                      <DialogHeader>
                        <DialogTitle>Cetak Faktur Servis</DialogTitle>
                        <DialogDescription>
                          Pilih template faktur yang ingin Anda cetak.
                        </DialogDescription>
                      </DialogHeader>

                      <div className="grid grid-cols-1 md:grid-cols-[300px_minmax(0,1fr)] gap-6 py-4">
                        {/* Left Column - Options */}
                        <div className="space-y-6 flex flex-col h-full">
                          {/* Orientation Options */}
                          <div className="space-y-3">
                            <div className="font-medium">Jenis Faktur</div>
                            <div className="grid grid-cols-2 gap-3">
                              <div
                                className={`border rounded-md p-3 cursor-pointer ${
                                  selectedOrientation === "horizontal"
                                    ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                                    : "hover:bg-gray-50 dark:hover:bg-gray-800"
                                }`}
                                onClick={() => {
                                  setSelectedOrientation("horizontal");
                                  // Update template to match orientation
                                  if (selectedTemplate.startsWith("vertical")) {
                                    const styleNumber =
                                      selectedTemplate.replace("vertical", "");
                                    setSelectedTemplate(
                                      `horizontal${styleNumber}` as TemplateType
                                    );
                                  }
                                }}
                              >
                                <div className="text-center">
                                  <div className="flex items-center justify-center mb-2">
                                    <svg
                                      xmlns="http://www.w3.org/2000/svg"
                                      width="20"
                                      height="20"
                                      viewBox="0 0 24 24"
                                      fill="none"
                                      stroke="currentColor"
                                      strokeWidth="2"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      className="mr-1.5 text-blue-600"
                                    >
                                      <rect
                                        x="3"
                                        y="3"
                                        width="18"
                                        height="18"
                                        rx="2"
                                        ry="2"
                                      />
                                      <line x1="9" y1="9" x2="15" y2="9" />
                                      <line x1="9" y1="15" x2="15" y2="15" />
                                    </svg>
                                  </div>
                                  <div className="text-xs font-medium">
                                    Horizontal (A4)
                                  </div>
                                  <div className="text-xs text-gray-500 mt-1">
                                    Landscape
                                  </div>
                                </div>
                              </div>

                              <div
                                className={`border rounded-md p-3 cursor-pointer ${
                                  selectedOrientation === "vertical"
                                    ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                                    : "hover:bg-gray-50 dark:hover:bg-gray-800"
                                }`}
                                onClick={() => {
                                  setSelectedOrientation("vertical");
                                  // Update template to match orientation
                                  if (
                                    selectedTemplate.startsWith("horizontal")
                                  ) {
                                    const styleNumber =
                                      selectedTemplate.replace(
                                        "horizontal",
                                        ""
                                      );
                                    setSelectedTemplate(
                                      `vertical${styleNumber}` as TemplateType
                                    );
                                  }
                                }}
                              >
                                <div className="text-center">
                                  <div className="flex items-center justify-center mb-2">
                                    <div className="w-4 h-6 border border-blue-600 rounded-sm flex flex-col justify-center items-center">
                                      <div className="h-1.5 bg-gray-200 w-full mb-1 rounded"></div>
                                      <div className="flex flex-col items-center">
                                        <div className="w-3/4">
                                          <div className="h-1 bg-gray-300 w-full mb-0.5 rounded"></div>
                                          <div className="h-1 bg-gray-300 w-full mb-0.5 rounded"></div>
                                        </div>
                                        <div className="w-full">
                                          <div className="h-1 bg-gray-200 w-full rounded"></div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="text-xs font-medium">
                                    Vertikal (Receipt)
                                  </div>
                                  <div className="text-xs text-gray-500 mt-1.5">
                                    Thermal Printer (58mm/80mm)
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Template Style Options */}
                          <div className="space-y-3">
                            <div className="font-medium">Gaya Template</div>
                            <Select
                              value={selectedTemplate}
                              onValueChange={(value) => {
                                setSelectedTemplate(value as TemplateType);
                              }}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Pilih gaya template" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="horizontal1">
                                  Template 1 - Standar
                                </SelectItem>
                                <SelectItem value="horizontal2">
                                  Template 2 - Modern
                                </SelectItem>
                                <SelectItem value="horizontal3">
                                  Template 3 - Minimalis
                                </SelectItem>
                                <SelectItem value="vertical1">
                                  Receipt 1 - Standar
                                </SelectItem>
                                <SelectItem value="vertical2">
                                  Receipt 2 - Kompak
                                </SelectItem>
                                <SelectItem value="vertical3">
                                  Receipt 3 - Detail
                                </SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          {/* Action Buttons */}
                          <div className="mt-auto pt-6">
                            <div className="flex flex-col gap-3">
                              <Button onClick={handlePrint} className="w-full">
                                Cetak Sekarang
                              </Button>
                              <Button
                                variant="outline"
                                onClick={() => setIsPrintDialogOpen(false)}
                                className="w-full"
                              >
                                Batal
                              </Button>
                            </div>
                          </div>
                        </div>

                        {/* Right Column - Preview */}
                        <div className="border rounded-lg p-4 bg-gray-50 dark:bg-gray-900">
                          <div className="text-center text-gray-500 dark:text-gray-400">
                            <div className="mb-4">
                              <FileText className="h-12 w-12 mx-auto mb-2 opacity-50" />
                              <p className="text-sm">Preview Template</p>
                            </div>
                            <p className="text-xs">
                              Template yang dipilih akan ditampilkan di sini
                            </p>
                          </div>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                  <Button variant="outline" asChild>
                    <Link
                      href={`/dashboard/services/management/edit/${service.serviceNumber}`}
                    >
                      Edit Servis
                    </Link>
                  </Button>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="destructive" className="gap-2">
                        <Trash className="h-4 w-4" />
                        Hapus Servis
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Konfirmasi Hapus</AlertDialogTitle>
                        <AlertDialogDescription>
                          Apakah Anda yakin ingin menghapus servis ini? Tindakan
                          ini tidak dapat dibatalkan.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Batal</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={handleDeleteService}
                          disabled={isDeleting}
                          className="bg-red-500 hover:bg-red-600"
                        >
                          {isDeleting ? "Menghapus..." : "Hapus"}
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>

                {/* Creation and Update Information */}
                <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <div className="grid grid-cols-2 gap-4 text-sm text-gray-500 dark:text-gray-400">
                    <div>
                      <span className="font-medium">Dibuat pada:</span>{" "}
                      {new Date(service.createdAt).toLocaleDateString("id-ID", {
                        day: "numeric",
                        month: "long",
                        year: "numeric",
                      })}
                    </div>
                    <div className="text-right">
                      <span className="font-medium">Terakhir diperbarui:</span>{" "}
                      {new Date(service.updatedAt).toLocaleDateString("id-ID", {
                        day: "numeric",
                        month: "long",
                        year: "numeric",
                      })}
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="history" className="space-y-4">
                <div>
                  <h3 className="text-lg font-medium mb-4">
                    Riwayat Status Servis
                  </h3>
                  <div className="space-y-3">
                    {service.serviceHistory &&
                    service.serviceHistory.length > 0 ? (
                      service.serviceHistory
                        .sort(
                          (a, b) =>
                            new Date(b.changedAt).getTime() -
                            new Date(a.changedAt).getTime()
                        )
                        .map((history) => (
                          <div
                            key={history.id}
                            className="flex items-start space-x-3 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border"
                          >
                            <div className="flex-shrink-0">
                              <div className="w-3 h-3 bg-blue-500 rounded-full mt-1"></div>
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center justify-between mb-2">
                                <div className="flex items-center gap-2">
                                  {getStatusBadge(history.status)}
                                </div>
                                <p className="text-xs text-gray-500 dark:text-gray-400">
                                  {formatDetailedDate(history.changedAt)}
                                </p>
                              </div>
                              {history.notes && (
                                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                                  {history.notes}
                                </p>
                              )}
                              <p className="text-xs text-gray-500 dark:text-gray-400">
                                Oleh: {history.changedBy}
                              </p>
                            </div>
                          </div>
                        ))
                    ) : (
                      <div className="text-center py-8">
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          Belum ada riwayat status untuk servis ini.
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default ServiceDetailPage;
