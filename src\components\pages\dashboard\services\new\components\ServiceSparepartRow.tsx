"use client";

import React, { useState } from "react";
import { ServiceFormValues, Product } from "../types";
import { Control, UseFieldArrayRemove, FieldValues, useFormContext } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { TrashIcon } from "@heroicons/react/24/outline";
import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Check, ChevronsUpDown } from "lucide-react";
import { cn } from "@/lib/utils";

interface ServiceSparepartRowProps {
  control: Control<ServiceFormValues>;
  index: number;
  field: FieldValues;
  products: Product[];
  spareParts: ServiceFormValues["spareParts"];
  remove: UseFieldArrayRemove;
  handleProductChange: (index: number, productId: string) => void;
  isPending: boolean;
  canRemove: boolean;
}

const ServiceSparepartRow: React.FC<ServiceSparepartRowProps> = ({
  control,
  index,
  field: _,
  products,
  spareParts,
  remove,
  handleProductChange,
  isPending,
  canRemove,
}) => {
  const form = useFormContext<ServiceFormValues>();
  const sparePart = spareParts[index];

  return (
    <tr className="border-b border-gray-200 dark:border-gray-700 h-20">
      {/* Product Selection */}
      <td className="py-4 pl-0 pr-2" style={{ minWidth: "300px" }}>
        <FormField
          control={control}
          name={`spareParts.${index}.productId`}
          render={({ field: formField }) => (
            <FormItem className="space-y-0">
              <div className="relative">
                <FormControl>
                  <ProductDropdown
                    value={formField.value}
                    onChange={(value) => {
                      formField.onChange(value);
                      handleProductChange(index, value);
                    }}
                    products={products}
                    disabled={isPending}
                  />
                </FormControl>
                <div className="absolute top-full left-0 w-full mt-1">
                  <FormMessage className="text-xs font-medium text-destructive bg-red-50 dark:bg-red-900/20 px-1 py-0.5 rounded-sm inline-block" />
                </div>
              </div>
            </FormItem>
          )}
        />
      </td>

      {/* Quantity */}
      <td className="py-4 px-2" style={{ minWidth: "100px" }}>
        <FormField
          control={control}
          name={`spareParts.${index}.quantity`}
          render={({ field: formField }) => (
            <FormItem className="space-y-0">
              <div className="relative">
                <FormControl>
                  <Input
                    type="number"
                    min="1"
                    step="1"
                    placeholder="1"
                    {...formField}
                    value={formField.value || ""}
                    onChange={(e) => {
                      const value = parseInt(e.target.value) || 1;
                      formField.onChange(value);
                    }}
                    disabled={isPending}
                    className="text-center"
                  />
                </FormControl>
                <div className="absolute top-full left-0 w-full mt-1">
                  <FormMessage className="text-xs font-medium text-destructive bg-red-50 dark:bg-red-900/20 px-1 py-0.5 rounded-sm inline-block" />
                </div>
              </div>
            </FormItem>
          )}
        />
      </td>

      {/* Unit */}
      <td className="py-4 px-2" style={{ minWidth: "100px" }}>
        <FormField
          control={control}
          name={`spareParts.${index}.unit`}
          render={({ field: formField }) => (
            <FormItem className="space-y-0">
              <div className="relative">
                <FormControl>
                  <Select
                    value={formField.value}
                    onValueChange={formField.onChange}
                    disabled={isPending}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Unit" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Pcs">Pcs</SelectItem>
                      <SelectItem value="Set">Set</SelectItem>
                      <SelectItem value="Unit">Unit</SelectItem>
                      <SelectItem value="Buah">Buah</SelectItem>
                      <SelectItem value="Meter">Meter</SelectItem>
                      <SelectItem value="Kg">Kg</SelectItem>
                      <SelectItem value="Liter">Liter</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <div className="absolute top-full left-0 w-full mt-1">
                  <FormMessage className="text-xs font-medium text-destructive bg-red-50 dark:bg-red-900/20 px-1 py-0.5 rounded-sm inline-block" />
                </div>
              </div>
            </FormItem>
          )}
        />
      </td>

      {/* Remove Button */}
      <td className="py-4 px-0 text-right" style={{ minWidth: "70px" }}>
        {canRemove && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => remove(index)}
            disabled={isPending}
            className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 cursor-pointer"
          >
            <TrashIcon className="h-5 w-5" />
          </Button>
        )}
      </td>
    </tr>
  );
};

// Custom Product Dropdown Component with Search
interface ProductDropdownProps {
  value: string;
  onChange: (value: string) => void;
  products: Product[];
  disabled?: boolean;
}

const ProductDropdown: React.FC<ProductDropdownProps> = ({
  value,
  onChange,
  products,
  disabled = false,
}) => {
  const [open, setOpen] = useState(false);

  const selectedProduct = products.find((product) => product.id === value);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between"
          disabled={disabled}
        >
          {selectedProduct ? (
            <div className="flex items-center gap-2 truncate">
              {selectedProduct.image && (
                <img
                  src={selectedProduct.image}
                  alt={selectedProduct.name}
                  className="w-6 h-6 rounded object-cover flex-shrink-0"
                />
              )}
              <span className="truncate">{selectedProduct.name}</span>
            </div>
          ) : (
            "Pilih produk/sparepart..."
          )}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start">
        <Command>
          <CommandInput placeholder="Cari produk..." />
          <CommandList>
            <CommandEmpty>Tidak ada produk ditemukan.</CommandEmpty>
            <CommandGroup>
              {products.map((product) => (
                <CommandItem
                  key={product.id}
                  value={product.name}
                  onSelect={() => {
                    onChange(product.id);
                    setOpen(false);
                  }}
                >
                  <div className="flex items-center gap-2 w-full">
                    {product.image && (
                      <img
                        src={product.image}
                        alt={product.name}
                        className="w-8 h-8 rounded object-cover flex-shrink-0"
                      />
                    )}
                    <div className="flex-1 min-w-0">
                      <div className="font-medium truncate">{product.name}</div>
                      {product.cost && (
                        <div className="text-sm text-muted-foreground">
                          Rp {product.cost.toLocaleString("id-ID")}
                        </div>
                      )}
                    </div>
                    <Check
                      className={cn(
                        "ml-auto h-4 w-4",
                        value === product.id ? "opacity-100" : "opacity-0"
                      )}
                    />
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

export default ServiceSparepartRow;
