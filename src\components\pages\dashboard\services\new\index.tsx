"use client";

import React, { useTransition, useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { addService } from "@/actions/entities/services";
import { getProducts } from "@/lib/get-products";

import DashboardLayout from "@/components/layout/dashboardlayout";
import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";

import { EnhancedServiceSchema } from "./types";
import { ServiceFormValues, Product } from "./types";
import CombinedServiceForm from "./components/CombinedServiceForm";
import { ArrowLeft, Check, Save } from "lucide-react";
import { DeviceType } from "../types";

const AddServicePage: React.FC = () => {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [products, setProducts] = useState<Product[]>([]);

  // Initialize the form with enhanced schema
  const form = useForm<ServiceFormValues>({
    resolver: zodResolver(EnhancedServiceSchema),
    defaultValues: {
      serviceNumber: "", // Will be auto-generated
      customerName: "",
      customerPhone: "",
      customerEmail: "",
      deviceType: DeviceType.OTHER,
      deviceBrand: "",
      deviceModel: "",
      deviceSerialNumber: "",
      problemDescription: "",
      estimatedCost: 0,
      finalCost: 0,
      estimatedCompletionDate: "",
      diagnosisNotes: "",
      repairNotes: "",
      customerAddress: "",
      warrantyPeriod: 0,
      priorityLevel: "MEDIUM",
      customerId: "",
      attachments: [],
      transactionDate: new Date(),
      dueDate: undefined,
      spareParts: [],
    },
  });

  // Field array for spare parts
  const {
    fields: sparePartFields,
    append: appendSparePart,
    remove: removeSparePart,
  } = useFieldArray({
    control: form.control,
    name: "spareParts",
  });

  // Load products on component mount
  useEffect(() => {
    const loadProducts = async () => {
      try {
        const result = await getProducts();
        setProducts(result);
      } catch (error) {
        console.error("Error loading products:", error);
      }
    };

    loadProducts();
  }, []);

  // Handle spare part product change
  const handleSparePartChange = (index: number, productId: string) => {
    const selectedProduct = products.find((p) => p.id === productId);
    if (selectedProduct) {
      form.setValue(`spareParts.${index}.unit`, selectedProduct.unit || "Pcs");
    }
  };

  const onSubmit = (values: ServiceFormValues) => {
    startTransition(async () => {
      try {
        // Show loading toast
        const toastId = toast.loading("Menyimpan data servis...");

        // Send the data to the server with isDraft = false
        const result = await addService({ ...values, isDraft: false });

        // Dismiss the loading toast
        toast.dismiss(toastId);

        if (result.success) {
          // Show success message
          toast.success(result.success);

          // Reset form and state
          form.reset({
            serviceNumber: "", // Will be auto-generated
            customerName: "",
            customerPhone: "",
            customerEmail: "",
            deviceType: DeviceType.OTHER,
            deviceBrand: "",
            deviceModel: "",
            deviceSerialNumber: "",
            problemDescription: "",
            estimatedCost: 0,
            finalCost: 0,
            estimatedCompletionDate: "",
            diagnosisNotes: "",
            repairNotes: "",
            customerAddress: "",
            warrantyPeriod: 0,
            priorityLevel: "MEDIUM",
            customerId: "",
            attachments: [],
            transactionDate: new Date(),
            dueDate: undefined,
            spareParts: [],
          });

          // Redirect to services page
          router.push("/dashboard/services/management");
        } else if (result.error) {
          toast.error(result.error);
        } else {
          toast.error("Terjadi kesalahan yang tidak diketahui.");
        }
      } catch (error) {
        console.error("Submit Error:", error);
        toast.error("Gagal menghubungi server.");
      }
    });
  };

  const onSaveDraft = (values: ServiceFormValues) => {
    startTransition(async () => {
      try {
        // Show loading toast
        const toastId = toast.loading("Menyimpan draft servis...");

        // Send the data to the server with isDraft = true
        const result = await addService({ ...values, isDraft: true });

        // Dismiss the loading toast
        toast.dismiss(toastId);

        if (result.success) {
          // Show success message
          toast.success("Draft servis berhasil disimpan!");

          // Reset form and state
          form.reset({
            serviceNumber: "", // Will be auto-generated
            customerName: "",
            customerPhone: "",
            customerEmail: "",
            deviceType: DeviceType.OTHER,
            deviceBrand: "",
            deviceModel: "",
            deviceSerialNumber: "",
            problemDescription: "",
            estimatedCost: 0,
            finalCost: 0,
            estimatedCompletionDate: "",
            diagnosisNotes: "",
            repairNotes: "",
            customerAddress: "",
            warrantyPeriod: 0,
            priorityLevel: "MEDIUM",
            customerId: "",
            attachments: [],
            transactionDate: new Date(),
            dueDate: undefined,
            spareParts: [],
          });

          // Redirect to services page with draft tab
          router.push("/dashboard/services/management?tab=drafts");
        } else if (result.error) {
          toast.error(result.error);
        } else {
          toast.error("Terjadi kesalahan yang tidak diketahui.");
        }
      } catch (error) {
        console.error("Submit Error:", error);
        toast.error("Gagal menghubungi server.");
      }
    });
  };

  return (
    <DashboardLayout>
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold">Tambah Servis Baru</h1>
            <p className="text-muted-foreground">
              Tambahkan servis baru ke sistem manajemen servis Anda
            </p>
          </div>
          <Button variant="outline" asChild className="gap-2 cursor-pointer">
            <Link
              href="/dashboard/services/management"
              className="cursor-pointer"
            >
              <ArrowLeft className="h-4 w-4" />
              <span className="hidden md:flex">Kembali</span>
            </Link>
          </Button>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="grid gap-6">
              {/* Main Form Content */}
              <div className="lg:col-span-2">
                <CombinedServiceForm
                  control={form.control}
                  isPending={isPending}
                  setValue={form.setValue}
                  trigger={form.trigger}
                  products={products}
                  spareParts={form.watch("spareParts")}
                  sparePartFields={sparePartFields}
                  appendSparePart={appendSparePart}
                  removeSparePart={removeSparePart}
                  handleSparePartChange={handleSparePartChange}
                />
              </div>
            </div>

            {/* Form Actions */}
            <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700 flex justify-end gap-4">
              <Button
                type="button"
                variant="outline"
                asChild
                disabled={isPending}
              >
                <Link
                  href="/dashboard/services/management"
                  className="cursor-pointer"
                >
                  Batal
                </Link>
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={form.handleSubmit(onSaveDraft)}
                disabled={isPending}
                className="gap-2 cursor-pointer"
              >
                {isPending ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600"></div>
                    <span>Menyimpan...</span>
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4" />
                    <span>
                      <span className="hidden md:inline">Simpan ke </span>Draft
                    </span>
                  </>
                )}
              </Button>
              <Button
                type="submit"
                disabled={isPending}
                className="gap-2 cursor-pointer"
              >
                {isPending ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Menyimpan...</span>
                  </>
                ) : (
                  <>
                    <Check className="h-4 w-4" />
                    <span>Simpan Servis</span>
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </DashboardLayout>
  );
};

export default AddServicePage;
