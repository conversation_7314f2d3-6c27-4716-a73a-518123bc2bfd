"use client";

import { useState, useEffect } from "react";
import { useTheme } from "next-themes";
import {
  <PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>lette,
  Monitor,
  Smartphone,
  Eye,
  Zap,
  Sparkles,
  Settings,
} from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  Ta<PERSON>,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs-v2";

const themeOptions = [
  {
    value: "light",
    label: "Terang",
    icon: Sun,
    description: "Tema terang untuk penggunaan di siang hari",
    preview: "bg-gradient-to-br from-white to-gray-50",
    accent: "from-yellow-400 to-orange-500",
  },
  {
    value: "dark",
    label: "Gelap",
    icon: <PERSON>,
    description: "Tema gelap untuk mengurangi ketegangan mata",
    preview: "bg-gradient-to-br from-gray-900 to-black",
    accent: "from-blue-400 to-purple-500",
  },
  {
    value: "system",
    label: "Sistem",
    icon: Laptop,
    description: "Mengikuti pengaturan sistem operasi Anda",
    preview: "bg-gradient-to-br from-gray-100 via-white to-gray-900",
    accent: "from-green-400 to-blue-500",
  },
];

const customizationOptions = [
  {
    id: "animations",
    label: "Animasi Halus",
    description: "Aktifkan transisi dan animasi yang halus",
    icon: Zap,
    defaultValue: true,
  },
  {
    id: "compactMode",
    label: "Mode Kompak",
    description: "Tampilan yang lebih padat untuk layar kecil",
    icon: Smartphone,
    defaultValue: false,
  },
  {
    id: "highContrast",
    label: "Kontras Tinggi",
    description: "Meningkatkan kontras untuk aksesibilitas",
    icon: Eye,
    defaultValue: false,
  },
  {
    id: "reducedMotion",
    label: "Kurangi Gerakan",
    description: "Mengurangi animasi untuk sensitivitas gerakan",
    icon: Monitor,
    defaultValue: false,
  },
];

export default function AppearanceSettings() {
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [customSettings, setCustomSettings] = useState<Record<string, boolean>>(
    {
      animations: true,
      compactMode: false,
      highContrast: false,
      reducedMotion: false,
    }
  );

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleSaveSettings = () => {
    setIsSaved(true);
    setTimeout(() => setIsSaved(false), 3000);
  };

  const handleCustomSettingChange = (id: string, value: boolean) => {
    setCustomSettings((prev) => ({ ...prev, [id]: value }));
    setIsSaved(false);
  };

  if (!mounted) {
    return null;
  }

  return (
    <div className="space-y-6 min-h-[100vh]">
      {/* Hero Header */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 p-8 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10">
          <div className="flex items-center gap-4 mb-4">
            <div className="h-12 w-12 rounded-xl bg-white/20 backdrop-blur-sm flex items-center justify-center">
              <Palette className="h-6 w-6" />
            </div>
            <div>
              <h1 className="text-2xl font-bold">Pengaturan Tampilan</h1>
              <p className="text-white/80">
                Personalisasi pengalaman visual Anda
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Sparkles className="h-4 w-4" />
            <span className="text-sm">
              Buat aplikasi sesuai dengan gaya Anda
            </span>
          </div>
        </div>
      </div>

      <Tabs defaultValue="theme" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="theme" className="flex items-center gap-2">
            <Palette className="h-4 w-4" />
            Tema
          </TabsTrigger>
          <TabsTrigger
            value="customization"
            className="flex items-center gap-2"
          >
            <Settings className="h-4 w-4" />
            Kustomisasi
          </TabsTrigger>
        </TabsList>

        <TabsContent value="theme" className="space-y-6">
          {/* Theme Selection */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-xl">Pilih Tema</CardTitle>
                  <CardDescription>
                    Pilih tema yang sesuai dengan preferensi Anda
                  </CardDescription>
                </div>
                <Badge
                  variant="outline"
                  className="bg-blue-50 text-blue-700 border-blue-200"
                >
                  Tema Aktif:{" "}
                  {themeOptions.find((t) => t.value === theme)?.label}
                </Badge>
              </div>
            </CardHeader>

            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {themeOptions.map((option) => (
                  <div
                    key={option.value}
                    className={`relative group cursor-pointer transition-all duration-300 ${
                      theme === option.value ? "scale-105" : "hover:scale-102"
                    }`}
                    onClick={() => {
                      setTheme(option.value);
                      setIsSaved(false);
                    }}
                  >
                    {/* Theme Preview Card */}
                    <div
                      className={`
                      relative overflow-hidden rounded-xl border-2 transition-all duration-300
                      ${
                        theme === option.value
                          ? "border-indigo-500 shadow-lg shadow-indigo-500/25"
                          : "border-gray-200 dark:border-gray-700 hover:border-indigo-300 dark:hover:border-indigo-600"
                      }
                    `}
                    >
                      {/* Preview Area */}
                      <div className={`h-32 ${option.preview} relative`}>
                        <div
                          className={`absolute inset-0 bg-gradient-to-r ${option.accent} opacity-20`}
                        ></div>
                        <div className="absolute top-3 right-3">
                          {theme === option.value && (
                            <div className="h-6 w-6 bg-indigo-500 rounded-full flex items-center justify-center animate-pulse">
                              <Check className="h-3 w-3 text-white" />
                            </div>
                          )}
                        </div>
                        <div className="absolute bottom-3 left-3">
                          <div
                            className={`
                            p-2 rounded-lg backdrop-blur-sm
                            ${
                              theme === option.value
                                ? "bg-indigo-500/20 text-indigo-700 dark:text-indigo-300"
                                : "bg-white/20 text-gray-700 dark:text-gray-300"
                            }
                          `}
                          >
                            <option.icon className="h-5 w-5" />
                          </div>
                        </div>
                      </div>

                      {/* Content Area */}
                      <div className="p-4 bg-white dark:bg-gray-800">
                        <div className="flex items-center justify-between mb-2">
                          <h3
                            className={`font-semibold ${
                              theme === option.value
                                ? "text-indigo-700 dark:text-indigo-300"
                                : "text-gray-900 dark:text-gray-100"
                            }`}
                          >
                            {option.label}
                          </h3>
                          {theme === option.value && (
                            <Badge className="bg-indigo-100 text-indigo-700 border-indigo-200">
                              Aktif
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {option.description}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="customization" className="space-y-6">
          {/* Customization Options */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="text-xl">Opsi Kustomisasi</CardTitle>
              <CardDescription>
                Sesuaikan perilaku dan tampilan aplikasi
              </CardDescription>
            </CardHeader>

            <CardContent className="space-y-6">
              {customizationOptions.map((option, index) => (
                <div key={option.id}>
                  <div className="flex items-center justify-between p-4 rounded-xl bg-gradient-to-r from-gray-50 to-white dark:from-gray-800 dark:to-gray-700 border border-gray-200 dark:border-gray-600 hover:shadow-md transition-all duration-200">
                    <div className="flex items-start gap-4">
                      <div className="mt-1 p-2 rounded-lg bg-gradient-to-br from-indigo-100 to-purple-100 dark:from-indigo-900/30 dark:to-purple-900/30">
                        <option.icon className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
                      </div>
                      <div className="flex-1">
                        <Label
                          htmlFor={option.id}
                          className="text-base font-medium cursor-pointer"
                        >
                          {option.label}
                        </Label>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                          {option.description}
                        </p>
                      </div>
                    </div>
                    <Switch
                      id={option.id}
                      checked={customSettings[option.id]}
                      onCheckedChange={(checked) =>
                        handleCustomSettingChange(option.id, checked)
                      }
                      className="cursor-pointer"
                    />
                  </div>
                  {index < customizationOptions.length - 1 && (
                    <Separator className="my-4" />
                  )}
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Preview Section */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="text-xl">Pratinjau</CardTitle>
              <CardDescription>
                Lihat bagaimana pengaturan Anda akan terlihat
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="p-6 rounded-xl bg-gradient-to-br from-gray-50 to-white dark:from-gray-800 dark:to-gray-700 border border-gray-200 dark:border-gray-600">
                <div className="flex items-center gap-3 mb-4">
                  <div className="h-8 w-8 rounded-full bg-gradient-to-r from-indigo-500 to-purple-500"></div>
                  <div>
                    <div className="h-3 w-24 bg-gray-300 dark:bg-gray-600 rounded"></div>
                    <div className="h-2 w-16 bg-gray-200 dark:bg-gray-700 rounded mt-1"></div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="h-2 w-full bg-gray-200 dark:bg-gray-700 rounded"></div>
                  <div className="h-2 w-3/4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                  <div className="h-2 w-1/2 bg-gray-200 dark:bg-gray-700 rounded"></div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Save Button */}
      <Card className="border-0 shadow-lg">
        <CardFooter className="flex justify-between items-center">
          {isSaved ? (
            <div className="flex items-center gap-2 text-green-600 dark:text-green-400">
              <Check className="h-4 w-4" />
              <span className="text-sm font-medium">
                Perubahan telah disimpan
              </span>
            </div>
          ) : (
            <div className="flex items-center gap-2 text-gray-500 dark:text-gray-400">
              <Settings className="h-4 w-4" />
              <span className="text-sm">
                Perubahan akan diterapkan secara otomatis
              </span>
            </div>
          )}
          <Button
            onClick={handleSaveSettings}
            className="bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white cursor-pointer"
          >
            <Check className="h-4 w-4 mr-2" />
            Simpan Perubahan
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
