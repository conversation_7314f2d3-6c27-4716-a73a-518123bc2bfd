"use client";

import { useState, useEffect } from "react";
import {
  CreditCard,
  Receipt,
  Clock,
  <PERSON><PERSON><PERSON>,
  TrendingUp,
  Calendar,
  DollarSign,
  CheckCircle,
  AlertCircle,
  Download,
  Eye,
  Star,
  Zap,
} from "lucide-react";

import PaymentHistory from "@/components/subscription/payment-history";
import CurrentSubscription from "@/components/subscription/current-subscription";
import { SubscriptionPlan } from "@prisma/client";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { SUBSCRIPTION_PLANS } from "@/lib/subscription";

interface BillingSettingsProps {
  initialData?: {
    plan: SubscriptionPlan;
    expiryDate: string | null;
    isActive: boolean;
  };
}

export default function BillingSettings({ initialData }: BillingSettingsProps) {
  const [subscription, setSubscription] = useState(initialData);
  const [isLoading, setIsLoading] = useState(!initialData);

  const fetchSubscription = async () => {
    if (initialData) return;

    try {
      setIsLoading(true);
      const response = await fetch("/api/subscriptions");

      if (!response.ok) {
        throw new Error("Failed to fetch subscription");
      }

      const data = await response.json();
      setSubscription(data.subscription);
    } catch (error) {
      console.error("Error fetching subscription:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchSubscription();
  }, [initialData]);

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "Tidak ada";
    return new Date(dateString).toLocaleDateString("id-ID", {
      day: "numeric",
      month: "long",
      year: "numeric",
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const currentPlan = subscription
    ? SUBSCRIPTION_PLANS[subscription.plan]
    : null;

  return (
    <div className="space-y-6 min-h-[100vh]">
      {/* Hero Header */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 p-8 text-white">
        <div className="absolute inset-0">
          <img
            src="https://images.unsplash.com/photo-1569284160951-aee94fa3c489?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHwyfHxjcmVkaXQlMjBjYXJkJTIwcGF5bWVudCUyMGludm9pY2V8ZW58MHwyfHxibHVlfDE3NTQ0NTQ5Nzl8MA&ixlib=rb-4.1.0&q=85"
            alt="Modern billing and payment illustration by La Compagnie Robinson on Unsplash"
            className="w-full h-full object-cover opacity-20"
            style={{ width: "100%", height: "100%" }}
          />
        </div>
        <div className="relative z-10">
          <div className="flex items-center gap-4 mb-4">
            <div className="h-12 w-12 rounded-xl bg-white/20 backdrop-blur-sm flex items-center justify-center">
              <CreditCard className="h-6 w-6" />
            </div>
            <div>
              <h1 className="text-2xl font-bold">Tagihan & Langganan</h1>
              <p className="text-white/80">
                Kelola pembayaran dan riwayat tagihan Anda
              </p>
            </div>
          </div>
          {subscription && (
            <div className="flex items-center gap-3">
              <Badge className="bg-white/20 text-white border-white/30">
                Paket {currentPlan?.name}
              </Badge>
              <Badge
                className={`${subscription.isActive ? "bg-green-500/20 text-green-100 border-green-300/30" : "bg-red-500/20 text-red-100 border-red-300/30"}`}
              >
                {subscription.isActive ? "Aktif" : "Tidak Aktif"}
              </Badge>
            </div>
          )}
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart className="h-4 w-4" />
            Ringkasan
          </TabsTrigger>
          <TabsTrigger value="subscription" className="flex items-center gap-2">
            <Star className="h-4 w-4" />
            Langganan
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center gap-2">
            <Receipt className="h-4 w-4" />
            Riwayat
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-green-600 dark:text-green-400">
                      Status Langganan
                    </p>
                    <p className="text-2xl font-bold text-green-700 dark:text-green-300">
                      {subscription?.isActive ? "Aktif" : "Tidak Aktif"}
                    </p>
                  </div>
                  <div className="h-12 w-12 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                    <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-blue-600 dark:text-blue-400">
                      Paket Saat Ini
                    </p>
                    <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">
                      {currentPlan?.name || "Tidak Ada"}
                    </p>
                  </div>
                  <div className="h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                    <Star className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-purple-600 dark:text-purple-400">
                      Harga Bulanan
                    </p>
                    <p className="text-2xl font-bold text-purple-700 dark:text-purple-300">
                      {currentPlan
                        ? formatCurrency(currentPlan.price)
                        : "Gratis"}
                    </p>
                  </div>
                  <div className="h-12 w-12 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center">
                    <DollarSign className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-gradient-to-br from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-orange-600 dark:text-orange-400">
                      Berakhir Pada
                    </p>
                    <p className="text-lg font-bold text-orange-700 dark:text-orange-300">
                      {subscription?.expiryDate
                        ? formatDate(subscription.expiryDate)
                        : "Selamanya"}
                    </p>
                  </div>
                  <div className="h-12 w-12 rounded-full bg-orange-100 dark:bg-orange-900/30 flex items-center justify-center">
                    <Calendar className="h-6 w-6 text-orange-600 dark:text-orange-400" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="text-xl">Aksi Cepat</CardTitle>
              <CardDescription>
                Kelola langganan dan pembayaran Anda dengan mudah
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button className="h-auto p-6 flex-col gap-3 bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white cursor-pointer">
                  <Zap className="h-6 w-6" />
                  <div className="text-center">
                    <div className="font-semibold">Upgrade Paket</div>
                    <div className="text-xs opacity-80">
                      Tingkatkan fitur Anda
                    </div>
                  </div>
                </Button>

                <Button
                  variant="outline"
                  className="h-auto p-6 flex-col gap-3 cursor-pointer"
                >
                  <Download className="h-6 w-6" />
                  <div className="text-center">
                    <div className="font-semibold">Unduh Invoice</div>
                    <div className="text-xs text-muted-foreground">
                      Riwayat pembayaran
                    </div>
                  </div>
                </Button>

                <Button
                  variant="outline"
                  className="h-auto p-6 flex-col gap-3 cursor-pointer"
                >
                  <Eye className="h-6 w-6" />
                  <div className="text-center">
                    <div className="font-semibold">Lihat Detail</div>
                    <div className="text-xs text-muted-foreground">
                      Informasi lengkap
                    </div>
                  </div>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="subscription" className="space-y-6">
          {/* Current Subscription */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-xl">Langganan Saat Ini</CardTitle>
                  <CardDescription>
                    Informasi detail tentang paket langganan Anda
                  </CardDescription>
                </div>
                {subscription?.isActive && (
                  <Badge className="bg-green-100 text-green-700 border-green-200">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Aktif
                  </Badge>
                )}
              </div>
            </CardHeader>
            <CardContent>
              <CurrentSubscription initialData={subscription} />
            </CardContent>
          </Card>

          {/* Plan Features */}
          {currentPlan && (
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="text-xl">
                  Fitur Paket {currentPlan.name}
                </CardTitle>
                <CardDescription>
                  Fitur yang tersedia dalam paket langganan Anda
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {currentPlan.features.map((feature, index) => (
                    <div
                      key={index}
                      className="flex items-center gap-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-800"
                    >
                      <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                      <span className="text-sm">{feature}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="history" className="space-y-6">
          {/* Payment History */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-xl">Riwayat Pembayaran</CardTitle>
                  <CardDescription>
                    Lihat semua transaksi dan pembayaran Anda
                  </CardDescription>
                </div>
                <Badge
                  variant="outline"
                  className="bg-blue-50 text-blue-700 border-blue-200"
                >
                  <TrendingUp className="h-3 w-3 mr-1" />
                  Transaksi Terbaru
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <PaymentHistory />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
