"use client";

import { Building2, Shield } from "lucide-react";
import ModernBusinessInfo from "./modern-business-info";
import { User } from "../profile/types";

interface BusinessSettingsProps {
  user: User;
}

export default function BusinessSettings({ user }: BusinessSettingsProps) {
  return (
    <div className="space-y-6 min-h-[100vh]">
      {/* Hero Header */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-emerald-500 via-green-500 to-teal-500 p-8 text-white">
        <div className="absolute inset-0">
          <img
            src="https://images.unsplash.com/photo-1528800557835-47d25f56a5a5?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHwyfHxvZmZpY2UlMjBidWlsZGluZyUyMGJ1c2luZXNzfGVufDB8Mnx8Z3JlZW58MTc1NDQ1NDk3OXww&ixlib=rb-4.1.0&q=85"
            alt="Business and office illustration by <PERSON> on Unsplash"
            className="w-full h-full object-cover opacity-20"
            style={{ width: "100%", height: "100%" }}
          />
        </div>
        <div className="relative z-10">
          <div className="flex items-center gap-4 mb-4">
            <div className="h-12 w-12 rounded-xl bg-white/20 backdrop-blur-sm flex items-center justify-center">
              <Building2 className="h-6 w-6" />
            </div>
            <div>
              <h1 className="text-2xl font-bold">Pengaturan Bisnis</h1>
              <p className="text-white/80">
                Kelola informasi dan konfigurasi bisnis Anda
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            <span className="text-sm">
              Data bisnis Anda aman dan terenkripsi
            </span>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <ModernBusinessInfo user={user} />
    </div>
  );
}
