"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { toast } from "sonner";
import {
  Building2,
  Building,
  Globe,
  Phone,
  Mail,
  MapPin,
  Clock,
  Bell,
  Save,
  RefreshCw,
  Briefcase,
  Users,
  Factory,
  CreditCard,
  Gift,
  Image as ImageIcon,
  Edit3,
  CheckCircle,
  AlertCircle,
  Sparkles,
  TrendingUp,
  Zap,
} from "lucide-react";
import {
  getBusinessInfo,
  updateBusinessInfo,
  type BusinessInfoData,
} from "@/actions/users/additional-info";
import { User } from "../profile/types";

interface ModernBusinessInfoProps {
  user: User;
}

export default function ModernBusinessInfo({ user }: ModernBusinessInfoProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [businessInfo, setBusinessInfo] = useState<BusinessInfoData>({});

  // Load business info on component mount
  useEffect(() => {
    const loadBusinessInfo = async () => {
      setIsLoading(true);
      try {
        const result = await getBusinessInfo();
        if (result.success && result.data) {
          console.log("Result data:", result.data);
          setBusinessInfo(result.data as BusinessInfoData);
        }
      } catch (error) {
        console.error("Error loading business info:", error);
        toast.error("Gagal memuat informasi bisnis");
      } finally {
        setIsLoading(false);
      }
    };

    loadBusinessInfo();
  }, []);

  // Handle form submission
  const handleSave = async () => {
    setIsSaving(true);
    try {
      const result = await updateBusinessInfo(businessInfo);
      if (result.success) {
        toast.success("Informasi bisnis berhasil disimpan!");
        setIsEditing(false); // Exit edit mode after successful save
      } else {
        toast.error(result.error || "Gagal menyimpan informasi bisnis");
      }
    } catch (error) {
      console.error("Error saving business info:", error);
      toast.error("Terjadi kesalahan saat menyimpan");
    } finally {
      setIsSaving(false);
    }
  };

  // Handle cancel edit
  const handleCancelEdit = () => {
    setIsEditing(false);
    // Reload data to reset any unsaved changes
    window.location.reload();
  };

  // Handle input changes
  const handleInputChange = (field: keyof BusinessInfoData, value: string) => {
    setBusinessInfo((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Calculate completion percentage
  const calculateCompletion = () => {
    const fields = [
      businessInfo.companyName,
      businessInfo.companyUsername,
      businessInfo.companyPhone,
      businessInfo.companyEmail,
      businessInfo.companyAddress,
      businessInfo.industry,
      businessInfo.position,
      businessInfo.employeeCount,
    ];
    const completedFields = fields.filter(
      (field) => field && field.trim() !== ""
    ).length;
    return Math.round((completedFields / fields.length) * 100);
  };

  const completionPercentage = calculateCompletion();

  if (isLoading) {
    return (
      <div className="space-y-8 p-6">
        <div className="max-w-7xl mx-auto">
          <Card className="border-0 shadow-2xl bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl">
            <CardContent className="flex flex-col items-center justify-center py-24">
              <div className="relative">
                <div className="w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center animate-pulse">
                  <RefreshCw className="h-8 w-8 text-white animate-spin" />
                </div>
                <div className="absolute -inset-4 bg-gradient-to-r from-indigo-500/20 to-purple-600/20 rounded-3xl blur-xl animate-pulse"></div>
              </div>
              <div className="mt-6 text-center">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  Memuat Informasi Bisnis
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Sedang mengambil data perusahaan Anda...
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8 p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Main Content Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
          {/* Company Information - Takes 2 columns on XL screens */}
          <div className="xl:col-span-2 space-y-8">
            <Card className="border-0 shadow-2xl bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl">
              <CardHeader className="pb-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-xl">
                      <Building className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                        Informasi Perusahaan
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Detail dasar tentang perusahaan Anda
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsEditing(!isEditing)}
                    className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm border-gray-200/60 dark:border-gray-700/60 hover:bg-gray-50/80 dark:hover:bg-gray-700/80 transition-all duration-200"
                  >
                    <Edit3 className="h-4 w-4 mr-2" />
                    {isEditing ? "Batal" : "Edit"}
                  </Button>
                </div>
              </CardHeader>

              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <Label className="text-sm font-medium flex items-center gap-2">
                      <Sparkles className="h-4 w-4 text-indigo-500" />
                      Nama Perusahaan
                    </Label>
                    {isEditing ? (
                      <Input
                        value={businessInfo.companyName || ""}
                        onChange={(e) =>
                          handleInputChange("companyName", e.target.value)
                        }
                        placeholder="PT. Teknologi Masa Depan"
                        className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm border-gray-200/60 dark:border-gray-700/60 focus:border-indigo-500 dark:focus:border-indigo-400 transition-all duration-200"
                      />
                    ) : (
                      <div className="bg-gray-50/60 dark:bg-gray-800/40 backdrop-blur-sm rounded-lg px-4 py-3 min-h-[42px] flex items-center text-gray-700 dark:text-gray-300 border border-gray-200/40 dark:border-gray-700/40">
                        {businessInfo.companyName || (
                          <span className="text-gray-400 dark:text-gray-500 italic">
                            Belum diisi
                          </span>
                        )}
                      </div>
                    )}
                  </div>

                  <div className="space-y-3">
                    <Label className="text-sm font-medium flex items-center gap-2">
                      <Globe className="h-4 w-4 text-indigo-500" />
                      Username Perusahaan
                    </Label>
                    {isEditing ? (
                      <Input
                        value={businessInfo.companyUsername || ""}
                        onChange={(e) =>
                          handleInputChange("companyUsername", e.target.value)
                        }
                        placeholder="teknologi-masa-depan"
                        className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm border-gray-200/60 dark:border-gray-700/60 focus:border-indigo-500 dark:focus:border-indigo-400 transition-all duration-200"
                      />
                    ) : (
                      <div className="bg-gray-50/60 dark:bg-gray-800/40 backdrop-blur-sm rounded-lg px-4 py-3 min-h-[42px] flex items-center text-gray-700 dark:text-gray-300 border border-gray-200/40 dark:border-gray-700/40">
                        {businessInfo.companyUsername || (
                          <span className="text-gray-400 dark:text-gray-500 italic">
                            Belum diisi
                          </span>
                        )}
                      </div>
                    )}
                  </div>

                  <div className="space-y-3">
                    <Label className="text-sm font-medium flex items-center gap-2">
                      <Phone className="h-4 w-4 text-indigo-500" />
                      Telepon Perusahaan
                    </Label>
                    {isEditing ? (
                      <Input
                        value={businessInfo.companyPhone || ""}
                        onChange={(e) =>
                          handleInputChange("companyPhone", e.target.value)
                        }
                        placeholder="+62 21-xxxx-xxxx"
                        className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm border-gray-200/60 dark:border-gray-700/60 focus:border-indigo-500 dark:focus:border-indigo-400 transition-all duration-200"
                      />
                    ) : (
                      <div className="bg-gray-50/60 dark:bg-gray-800/40 backdrop-blur-sm rounded-lg px-4 py-3 min-h-[42px] flex items-center text-gray-700 dark:text-gray-300 border border-gray-200/40 dark:border-gray-700/40">
                        {businessInfo.companyPhone || (
                          <span className="text-gray-400 dark:text-gray-500 italic">
                            Belum diisi
                          </span>
                        )}
                      </div>
                    )}
                  </div>

                  <div className="space-y-3">
                    <Label className="text-sm font-medium flex items-center gap-2">
                      <Mail className="h-4 w-4 text-indigo-500" />
                      Email Perusahaan
                    </Label>
                    {isEditing ? (
                      <Input
                        type="email"
                        value={businessInfo.companyEmail || ""}
                        onChange={(e) =>
                          handleInputChange("companyEmail", e.target.value)
                        }
                        placeholder="<EMAIL>"
                        className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm border-gray-200/60 dark:border-gray-700/60 focus:border-indigo-500 dark:focus:border-indigo-400 transition-all duration-200"
                      />
                    ) : (
                      <div className="bg-gray-50/60 dark:bg-gray-800/40 backdrop-blur-sm rounded-lg px-4 py-3 min-h-[42px] flex items-center text-gray-700 dark:text-gray-300 border border-gray-200/40 dark:border-gray-700/40">
                        {businessInfo.companyEmail || (
                          <span className="text-gray-400 dark:text-gray-500 italic">
                            Belum diisi
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-3">
                  <Label className="text-sm font-medium flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-indigo-500" />
                    Alamat Perusahaan
                  </Label>
                  {isEditing ? (
                    <Textarea
                      value={businessInfo.companyAddress || ""}
                      onChange={(e) =>
                        handleInputChange("companyAddress", e.target.value)
                      }
                      placeholder="Jl. Sudirman No. 123, Jakarta Selatan"
                      className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm border-gray-200/60 dark:border-gray-700/60 focus:border-indigo-500 dark:focus:border-indigo-400 transition-all duration-200 min-h-[100px] resize-none"
                    />
                  ) : (
                    <div className="bg-gray-50/60 dark:bg-gray-800/40 backdrop-blur-sm rounded-lg px-4 py-3 min-h-[100px] flex items-start text-gray-700 dark:text-gray-300 border border-gray-200/40 dark:border-gray-700/40">
                      {businessInfo.companyAddress ? (
                        <p className="whitespace-pre-wrap">
                          {businessInfo.companyAddress}
                        </p>
                      ) : (
                        <span className="text-gray-400 dark:text-gray-500 italic">
                          Belum diisi
                        </span>
                      )}
                    </div>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Kota</Label>
                    {isEditing ? (
                      <Input
                        value={businessInfo.city || ""}
                        onChange={(e) =>
                          handleInputChange("city", e.target.value)
                        }
                        placeholder="Jakarta"
                        className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm border-gray-200/60 dark:border-gray-700/60 focus:border-indigo-500 dark:focus:border-indigo-400 transition-all duration-200"
                      />
                    ) : (
                      <div className="bg-gray-50/60 dark:bg-gray-800/40 backdrop-blur-sm rounded-lg px-4 py-3 min-h-[42px] flex items-center text-gray-700 dark:text-gray-300 border border-gray-200/40 dark:border-gray-700/40">
                        {businessInfo.city || (
                          <span className="text-gray-400 dark:text-gray-500 italic">
                            Belum diisi
                          </span>
                        )}
                      </div>
                    )}
                  </div>

                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Kode Pos</Label>
                    {isEditing ? (
                      <Input
                        value={businessInfo.postalCode || ""}
                        onChange={(e) =>
                          handleInputChange("postalCode", e.target.value)
                        }
                        placeholder="12345"
                        className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm border-gray-200/60 dark:border-gray-700/60 focus:border-indigo-500 dark:focus:border-indigo-400 transition-all duration-200"
                      />
                    ) : (
                      <div className="bg-gray-50/60 dark:bg-gray-800/40 backdrop-blur-sm rounded-lg px-4 py-3 min-h-[42px] flex items-center text-gray-700 dark:text-gray-300 border border-gray-200/40 dark:border-gray-700/40">
                        {businessInfo.postalCode || (
                          <span className="text-gray-400 dark:text-gray-500 italic">
                            Belum diisi
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Business Details Sidebar */}
          <div className="space-y-8">
            <Card className="border-0 shadow-2xl bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl">
              <CardHeader className="pb-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl">
                      <Briefcase className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                        Detail Bisnis
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Informasi khusus bisnis Anda
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsEditing(!isEditing)}
                    className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm border-gray-200/60 dark:border-gray-700/60 hover:bg-gray-50/80 dark:hover:bg-gray-700/80 transition-all duration-200"
                  >
                    <Edit3 className="h-4 w-4 mr-2" />
                    {isEditing ? "Batal" : "Edit"}
                  </Button>
                </div>
              </CardHeader>

              <CardContent className="space-y-6">
                <div className="space-y-3">
                  <Label className="text-sm font-medium flex items-center gap-2">
                    <Factory className="h-4 w-4 text-purple-500" />
                    Industri
                  </Label>
                  {isEditing ? (
                    <Input
                      value={businessInfo.industry || ""}
                      onChange={(e) =>
                        handleInputChange("industry", e.target.value)
                      }
                      placeholder="Teknologi Informasi"
                      className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm border-gray-200/60 dark:border-gray-700/60 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200"
                    />
                  ) : (
                    <div className="bg-gray-50/60 dark:bg-gray-800/40 backdrop-blur-sm rounded-lg px-4 py-3 min-h-[42px] flex items-center text-gray-700 dark:text-gray-300 border border-gray-200/40 dark:border-gray-700/40">
                      {businessInfo.industry || (
                        <span className="text-gray-400 dark:text-gray-500 italic">
                          Belum diisi
                        </span>
                      )}
                    </div>
                  )}
                </div>

                <div className="space-y-3">
                  <Label className="text-sm font-medium flex items-center gap-2">
                    <Users className="h-4 w-4 text-purple-500" />
                    Posisi Anda
                  </Label>
                  {isEditing ? (
                    <Input
                      value={businessInfo.position || ""}
                      onChange={(e) =>
                        handleInputChange("position", e.target.value)
                      }
                      placeholder="Chief Executive Officer"
                      className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm border-gray-200/60 dark:border-gray-700/60 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200"
                    />
                  ) : (
                    <div className="bg-gray-50/60 dark:bg-gray-800/40 backdrop-blur-sm rounded-lg px-4 py-3 min-h-[42px] flex items-center text-gray-700 dark:text-gray-300 border border-gray-200/40 dark:border-gray-700/40">
                      {businessInfo.position || (
                        <span className="text-gray-400 dark:text-gray-500 italic">
                          Belum diisi
                        </span>
                      )}
                    </div>
                  )}
                </div>

                <div className="space-y-3">
                  <Label className="text-sm font-medium flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-purple-500" />
                    Jumlah Karyawan
                  </Label>
                  {isEditing ? (
                    <Input
                      value={businessInfo.employeeCount || ""}
                      onChange={(e) =>
                        handleInputChange("employeeCount", e.target.value)
                      }
                      placeholder="51-200 Karyawan"
                      className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm border-gray-200/60 dark:border-gray-700/60 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200"
                    />
                  ) : (
                    <div className="bg-gray-50/60 dark:bg-gray-800/40 backdrop-blur-sm rounded-lg px-4 py-3 min-h-[42px] flex items-center text-gray-700 dark:text-gray-300 border border-gray-200/40 dark:border-gray-700/40">
                      {businessInfo.employeeCount || (
                        <span className="text-gray-400 dark:text-gray-500 italic">
                          Belum diisi
                        </span>
                      )}
                    </div>
                  )}
                </div>

                <div className="space-y-3">
                  <Label className="text-sm font-medium flex items-center gap-2">
                    <Zap className="h-4 w-4 text-purple-500" />
                    Bidang Usaha
                  </Label>
                  {isEditing ? (
                    <Input
                      value={businessInfo.occupation || ""}
                      onChange={(e) =>
                        handleInputChange("occupation", e.target.value)
                      }
                      placeholder="Pengembangan Software"
                      className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm border-gray-200/60 dark:border-gray-700/60 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200"
                    />
                  ) : (
                    <div className="bg-gray-50/60 dark:bg-gray-800/40 backdrop-blur-sm rounded-lg px-4 py-3 min-h-[42px] flex items-center text-gray-700 dark:text-gray-300 border border-gray-200/40 dark:border-gray-700/40">
                      {businessInfo.occupation || (
                        <span className="text-gray-400 dark:text-gray-500 italic">
                          Belum diisi
                        </span>
                      )}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Action Buttons - Only show when editing */}
        {isEditing && (
          <Card className="border-0 shadow-2xl bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl">
            <CardContent className="p-6">
              <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl">
                    <Save className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-white">
                      Siap untuk menyimpan perubahan?
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Pastikan semua informasi sudah benar sebelum menyimpan
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Button
                    variant="outline"
                    onClick={handleCancelEdit}
                    disabled={isSaving}
                    className="px-6 py-2.5 bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm border-gray-200/60 dark:border-gray-700/60 hover:bg-gray-50/80 dark:hover:bg-gray-700/80 transition-all duration-200"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Batal
                  </Button>
                  <Button
                    onClick={handleSave}
                    disabled={isSaving}
                    className="px-6 py-2.5 bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
                  >
                    {isSaving ? (
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Save className="h-4 w-4 mr-2" />
                    )}
                    {isSaving ? "Menyimpan..." : "Simpan Perubahan"}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
