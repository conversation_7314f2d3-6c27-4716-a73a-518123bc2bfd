import React from "react";
import {
  UsersIcon,
  ShieldCheckIcon,
  UserIcon,
} from "@heroicons/react/24/outline";

interface EmployeeCounts {
  total: number;
  admin: number;
  cashier: number;
}

interface EmployeeSummaryCardsProps {
  employeeCounts: EmployeeCounts;
}

export const EmployeeSummaryCards: React.FC<EmployeeSummaryCardsProps> = ({
  employeeCounts,
}) => {
  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
      {/* Total Karyawan */}
      <div className="rounded-lg border border-gray-200 bg-blue-50 dark:border-gray-700 dark:bg-blue-900/20">
        <div className="flex justify-between items-center p-4">
          <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Total Karyawan
          </div>
          <UsersIcon className="h-5 w-5 text-blue-500 dark:text-blue-400" />
        </div>
        <div className="px-4 pb-4">
          <div className="text-xs text-gray-500 dark:text-gray-400">
            Karyawan aktif
          </div>
          <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {employeeCounts.total}
          </div>
        </div>
      </div>

      {/* Admin */}
      <div className="rounded-lg border border-gray-200 bg-purple-50 dark:border-gray-700 dark:bg-purple-900/20">
        <div className="flex justify-between items-center p-4">
          <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Admin
          </div>
          <ShieldCheckIcon className="h-5 w-5 text-purple-500 dark:text-purple-400" />
        </div>
        <div className="px-4 pb-4">
          <div className="text-xs text-gray-500 dark:text-gray-400">
            Akses penuh
          </div>
          <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {employeeCounts.admin}
          </div>
        </div>
      </div>

      {/* Kasir */}
      <div className="rounded-lg border border-gray-200 bg-green-50 dark:border-gray-700 dark:bg-green-900/20">
        <div className="flex justify-between items-center p-4">
          <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Kasir
          </div>
          <UserIcon className="h-5 w-5 text-green-500 dark:text-green-400" />
        </div>
        <div className="px-4 pb-4">
          <div className="text-xs text-gray-500 dark:text-gray-400">
            Akses terbatas
          </div>
          <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {employeeCounts.cashier}
          </div>
        </div>
      </div>
    </div>
  );
};
