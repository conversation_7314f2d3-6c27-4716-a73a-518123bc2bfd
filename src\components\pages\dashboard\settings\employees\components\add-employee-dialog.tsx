"use client";

import { useState } from "react";
import { createEmployee } from "@/actions/entities/employee";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CreateEmployeeSchema } from "@/schemas/zod";
import { toast } from "sonner";
import {
  UserPlus,
  User,
  AtSign,
  KeyRound,
  ShieldCheck,
  AlertCircle,
  CheckCircle2,
  Eye,
  EyeOff,
  Lock,
} from "lucide-react";
import { useUserLimits } from "@/hooks/useSubscriptionLimits";

interface AddEmployeeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void; // Callback to refresh the employee list
}

export function AddEmployeeDialog({
  open,
  onOpenChange,
  onSuccess,
}: AddEmployeeDialogProps) {
  const [name, setName] = useState("");
  const [employeeId, setEmployeeId] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [role, setRole] = useState<"ADMIN" | "CASHIER">("CASHIER");
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  const {
    canCreateUser,
    userMessage,
    currentUserUsage,
    userLimit,
    isLoading: limitsLoading,
  } = useUserLimits();

  const resetForm = () => {
    setName("");
    setEmployeeId("");
    setPassword("");
    setRole("CASHIER");
    setFormErrors({});
  };

  const handleCreateEmployee = async () => {
    // Check subscription limits before submission
    if (!canCreateUser) {
      toast.error(userMessage || "Batas pengguna tercapai untuk paket Anda.");
      return;
    }

    try {
      // Validate form
      const formData = { name, employeeId, password, role };
      const validationResult = CreateEmployeeSchema.safeParse(formData);

      if (!validationResult.success) {
        const errors: Record<string, string> = {};
        validationResult.error.errors.forEach((err) => {
          if (err.path[0]) {
            errors[err.path[0].toString()] = err.message;
          }
        });
        setFormErrors(errors);
        return;
      }

      // Clear previous errors
      setFormErrors({});

      // Submit form
      const result = await createEmployee(formData);

      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success(result.success);
        onOpenChange(false); // Close dialog
        resetForm();
        onSuccess(); // Trigger refresh
      }
    } catch (err) {
      toast.error("Terjadi kesalahan saat menambahkan karyawan");
      console.error(err);
    }
  };

  // Reset form when dialog closes
  const handleOpenChange = (isOpen: boolean) => {
    if (!isOpen) {
      resetForm();
    }
    onOpenChange(isOpen);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            <UserPlus className="h-5 w-5 text-primary" />
            Tambah Karyawan Baru
          </DialogTitle>
          <DialogDescription>
            Tambahkan karyawan baru untuk mengakses sistem kasir Anda
          </DialogDescription>
        </DialogHeader>

        {/* Subscription Limit Warning */}
        {userLimit && currentUserUsage !== undefined && (
          <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-100 dark:border-orange-800/30 rounded-lg p-3 mb-0">
            <div className="flex items-start gap-3">
              <AlertCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
              <div className="space-y-1">
                <p className="text-sm font-medium text-orange-800 dark:text-orange-200">
                  Batas Pengguna
                </p>
                <p className="text-sm text-orange-700 dark:text-orange-300">
                  Anda telah menggunakan {currentUserUsage} dari {userLimit}{" "}
                  pengguna yang tersedia.
                  {userLimit - currentUserUsage <= 2 && (
                    <span className="font-medium">
                      {" "}
                      Sisa {userLimit - currentUserUsage} pengguna lagi.
                    </span>
                  )}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Success Message */}
        {Object.keys(formErrors).length === 0 && (
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-100 dark:border-green-800/30 rounded-lg p-3 mb-4 hidden">
            <div className="flex items-center">
              <CheckCircle2 className="h-5 w-5 text-green-500 dark:text-green-400 mr-2" />
              <p className="text-sm text-green-700 dark:text-green-300">
                Karyawan berhasil ditambahkan
              </p>
            </div>
          </div>
        )}

        {/* Error Summary */}
        {Object.keys(formErrors).length > 0 && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-100 dark:border-red-800/30 rounded-lg p-3 mb-4">
            <div className="flex items-center">
              <AlertCircle className="h-5 w-5 text-red-500 dark:text-red-400 mr-2" />
              <p className="text-sm text-red-700 dark:text-red-300">
                Mohon perbaiki kesalahan berikut
              </p>
            </div>
          </div>
        )}

        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="name" className="flex items-center gap-1.5">
              <User className="h-3.5 w-3.5 text-gray-500 dark:text-gray-400" />
              Nama Karyawan
            </Label>
            <div className="relative">
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Masukkan nama karyawan"
                className={
                  formErrors.name
                    ? "border-red-300 dark:border-red-700 pr-10"
                    : ""
                }
              />
              {formErrors.name && (
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <AlertCircle className="h-4 w-4 text-red-500" />
                </div>
              )}
            </div>
            {formErrors.name && (
              <p className="text-sm text-red-500">{formErrors.name}</p>
            )}
          </div>

          <div className="grid gap-2">
            <Label htmlFor="employeeId" className="flex items-center gap-1.5">
              <AtSign className="h-3.5 w-3.5 text-gray-500 dark:text-gray-400" />
              ID Karyawan
            </Label>
            <div className="relative">
              <Input
                id="employeeId"
                value={employeeId}
                onChange={(e) => setEmployeeId(e.target.value)}
                placeholder="Masukkan ID karyawan untuk login"
                className={
                  formErrors.employeeId
                    ? "border-red-300 dark:border-red-700 pr-10"
                    : ""
                }
              />
              {formErrors.employeeId && (
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <AlertCircle className="h-4 w-4 text-red-500" />
                </div>
              )}
            </div>
            {formErrors.employeeId && (
              <p className="text-sm text-red-500">{formErrors.employeeId}</p>
            )}
          </div>

          <div className="grid gap-2">
            <Label htmlFor="password" className="flex items-center gap-1.5">
              <KeyRound className="h-3.5 w-3.5 text-gray-500 dark:text-gray-400" />
              Password
            </Label>
            <div className="relative">
              <Input
                id="password"
                type={showPassword ? "text" : "password"}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Masukkan password"
                className={
                  formErrors.password
                    ? "border-red-300 dark:border-red-700 pr-10"
                    : "pr-10"
                }
              />
              <button
                type="button"
                onClick={() => setShowPassword((prev) => !prev)}
                className="absolute inset-y-0 right-0 flex items-center pr-3 focus:outline-none cursor-pointer"
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                ) : (
                  <Eye className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                )}
              </button>
              {formErrors.password && (
                <div className="absolute inset-y-0 right-0 flex items-center pr-10 pointer-events-none">
                  <AlertCircle className="h-4 w-4 text-red-500" />
                </div>
              )}
            </div>
            {formErrors.password && (
              <p className="text-sm text-red-500">{formErrors.password}</p>
            )}
          </div>

          <div className="grid gap-2">
            <Label htmlFor="role" className="flex items-center gap-1.5">
              <ShieldCheck className="h-3.5 w-3.5 text-gray-500 dark:text-gray-400" />
              Peran
            </Label>
            <Select
              value={role}
              onValueChange={(value: "ADMIN" | "CASHIER") => setRole(value)}
            >
              <SelectTrigger
                className={
                  formErrors.role ? "border-red-300 dark:border-red-700" : ""
                }
              >
                <SelectValue placeholder="Pilih peran" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ADMIN" className="flex items-center gap-2">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-purple-500"></div>
                    Admin
                  </div>
                </SelectItem>
                <SelectItem value="CASHIER" className="flex items-center gap-2">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-green-500"></div>
                    Kasir
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
            {formErrors.role && (
              <p className="text-sm text-red-500">{formErrors.role}</p>
            )}
          </div>
        </div>

        <DialogFooter className="gap-2 sm:gap-0">
          <Button
            variant="outline"
            onClick={() => handleOpenChange(false)}
            className="border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
          >
            Batal
          </Button>
          <Button
            onClick={handleCreateEmployee}
            className="bg-primary hover:bg-primary/90 text-white gap-1 cursor-pointer"
          >
            <UserPlus className="h-4 w-4" />
            Simpan
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
