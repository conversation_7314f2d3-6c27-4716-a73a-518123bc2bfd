"use client";

import { deleteEmployee, getEmployees } from "@/actions/entities/employee";
import { Role } from "@prisma/client";
import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  UserPlus,
  Users,
  AlertCircle,
  Shield,
  Crown,
  User,
  TrendingUp,
  Activity,
  Clock,
  Search,
  Filter,
  MoreVertical,
  Settings,
  Sparkles,
  Target,
  Zap,
} from "lucide-react";
import { EmployeeTable } from "./components/employee-table";
import { AddEmployeeDialog } from "./components/add-employee-dialog";
import { EditEmployeeNameDialog } from "./components/edit-employee-name-dialog";
import { EditEmployeePasswordDialog } from "./components/edit-employee-password-dialog";
import { EmployeeSummaryCards } from "./components/EmployeeSummaryCards";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";

interface Employee {
  id: string;
  name: string;
  employeeId: string;
  role: Role;
  createdAt?: Date;
}

export default function EmployeeManagementSettings() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditNameDialogOpen, setIsEditNameDialogOpen] = useState(false);
  const [isEditPasswordDialogOpen, setIsEditPasswordDialogOpen] =
    useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(
    null
  );
  const [searchTerm, setSearchTerm] = useState("");

  const getCurrentTab = () => {
    if (searchParams.has("admin")) return "admin";
    if (searchParams.has("kasir")) return "cashier";
    if (searchParams.has("semua")) return "all";
    return "all";
  };

  const [activeTab, setActiveTab] = useState(getCurrentTab());

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    const currentPath = window.location.pathname;
    let newUrl = currentPath;

    if (value === "admin") {
      newUrl = `${currentPath}?admin`;
    } else if (value === "cashier") {
      newUrl = `${currentPath}?kasir`;
    } else if (value === "all") {
      newUrl = `${currentPath}?semua`;
    } else {
      newUrl = currentPath;
    }

    router.replace(newUrl, { scroll: false });
  };

  useEffect(() => {
    setActiveTab(getCurrentTab());
  }, [searchParams]);

  useEffect(() => {
    fetchEmployees();
  }, []);

  const fetchEmployees = async () => {
    setLoading(true);
    try {
      const result = await getEmployees();
      if (result.error) {
        setError(result.error);
      } else if (result.employees) {
        setEmployees(result.employees);
      }
    } catch (err) {
      setError("Terjadi kesalahan saat mengambil data karyawan");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteEmployee = async (id: string) => {
    if (confirm("Apakah Anda yakin ingin menghapus karyawan ini?")) {
      try {
        const result = await deleteEmployee(id);

        if (result.error) {
          toast.error(result.error);
        } else {
          toast.success(result.success);
          fetchEmployees();
        }
      } catch (err) {
        toast.error("Terjadi kesalahan saat menghapus karyawan");
        console.error(err);
      }
    }
  };

  const handleEditNameClick = (employee: Employee) => {
    setSelectedEmployee(employee);
    setIsEditNameDialogOpen(true);
  };

  const handleEditPasswordClick = (employee: Employee) => {
    setSelectedEmployee(employee);
    setIsEditPasswordDialogOpen(true);
  };

  // Filter employees based on search term
  const filteredEmployees = employees.filter(
    (employee) =>
      employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      employee.employeeId.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Enhanced loading state with modern skeleton
  if (loading && employees.length === 0) {
    return (
      <div className="space-y-8 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
        {/* Hero Skeleton */}
        <div className="relative overflow-hidden rounded-3xl h-48 sm:h-56 bg-gradient-to-r from-slate-200 via-slate-300 to-slate-200 dark:from-slate-800 dark:via-slate-700 dark:to-slate-800 animate-pulse">
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
        </div>

        {/* Stats Skeleton */}
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          {[1, 2, 3, 4].map((i) => (
            <div
              key={i}
              className="relative overflow-hidden h-32 bg-gradient-to-br from-slate-200 to-slate-300 dark:from-slate-800 dark:to-slate-700 rounded-2xl animate-pulse"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
            </div>
          ))}
        </div>

        {/* Main Content Skeleton */}
        <div className="relative overflow-hidden h-96 bg-slate-200 dark:bg-slate-800 rounded-2xl animate-pulse">
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
        </div>
      </div>
    );
  }

  if (error && employees.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh] space-y-6 text-red-500 px-4">
        <div className="h-16 w-16 rounded-full bg-red-50 dark:bg-red-900/20 flex items-center justify-center">
          <AlertCircle className="h-8 w-8" />
        </div>
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
            Terjadi Kesalahan
          </h3>
          <p className="text-gray-600 dark:text-gray-400">{error}</p>
        </div>
        <Button onClick={fetchEmployees} variant="outline">
          Coba Lagi
        </Button>
      </div>
    );
  }

  const adminCount = employees.filter((e) => e.role === "ADMIN").length;
  const cashierCount = employees.filter((e) => e.role === "CASHIER").length;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50 dark:from-slate-950 dark:via-slate-900 dark:to-slate-950 px-4 sm:px-6 lg:px-8 py-8">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Enhanced Hero Header */}
        <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-orange-600 via-amber-500 to-yellow-400 p-8 sm:p-12 text-white shadow-2xl">
          {/* Background Pattern */}
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_20%_80%,_white/10,_transparent)] opacity-70"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,_white/10,_transparent)] opacity-70"></div>

          {/* Floating Elements */}
          <div
            className="absolute top-4 right-4 h-24 w-24 rounded-full bg-white/10 backdrop-blur-sm animate-bounce"
            style={{ animationDelay: "1s" }}
          ></div>
          <div
            className="absolute bottom-8 right-8 h-16 w-16 rounded-full bg-white/10 backdrop-blur-sm animate-bounce"
            style={{ animationDelay: "2s" }}
          ></div>

          <div className="relative z-10">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6">
              <div className="flex items-center gap-6">
                <div className="h-16 w-16 rounded-2xl bg-white/20 backdrop-blur-md border border-white/30 flex items-center justify-center shadow-lg">
                  <Users className="h-8 w-8" />
                </div>
                <div>
                  <h1 className="text-3xl sm:text-4xl font-bold mb-2 bg-gradient-to-r from-white to-white/80 bg-clip-text">
                    Manajemen Karyawan
                  </h1>
                  <p className="text-white/90 text-lg">
                    Kelola tim dan akses karyawan Anda dengan mudah
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-4 text-sm">
                <div className="flex items-center gap-2 px-4 py-2 rounded-full bg-white/20 backdrop-blur-sm border border-white/30">
                  <Activity className="h-4 w-4" />
                  <span>{employees.length} karyawan</span>
                </div>
                <div className="flex items-center gap-2 px-4 py-2 rounded-full bg-white/20 backdrop-blur-sm border border-white/30">
                  <Shield className="h-4 w-4" />
                  <span>Akses terkontrol</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Stats Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="group relative overflow-hidden border-0 shadow-xl bg-gradient-to-br from-blue-50 via-cyan-50 to-blue-50 dark:from-blue-950/50 dark:via-cyan-950/50 dark:to-blue-950/50 hover:shadow-2xl transition-all duration-500 hover:scale-105">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-cyan-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <CardContent className="p-4 relative z-10">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs font-semibold text-blue-600 dark:text-blue-400 mb-1">
                    Total Karyawan
                  </p>
                  <p className="text-3xl font-bold text-blue-700 dark:text-blue-300 mb-1">
                    {employees.length}
                  </p>
                  <div className="flex items-center gap-2 text-xs text-blue-600/70 dark:text-blue-400/70">
                    <TrendingUp className="h-3 w-3" />
                    <span>Aktif semua</span>
                  </div>
                </div>
                <div className="h-12 w-12 rounded-xl bg-gradient-to-br from-blue-500 to-cyan-600 shadow-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <Users className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="group relative overflow-hidden border-0 shadow-xl bg-gradient-to-br from-purple-50 via-violet-50 to-purple-50 dark:from-purple-950/50 dark:via-violet-950/50 dark:to-purple-950/50 hover:shadow-2xl transition-all duration-500 hover:scale-105">
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-violet-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <CardContent className="p-4 relative z-10">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs font-semibold text-purple-600 dark:text-purple-400 mb-1">
                    Administrator
                  </p>
                  <p className="text-3xl font-bold text-purple-700 dark:text-purple-300 mb-1">
                    {adminCount}
                  </p>
                  <div className="flex items-center gap-2 text-xs text-purple-600/70 dark:text-purple-400/70">
                    <Sparkles className="h-3 w-3" />
                    <span>Akses penuh</span>
                  </div>
                </div>
                <div className="h-12 w-12 rounded-xl bg-gradient-to-br from-purple-500 to-violet-600 shadow-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <Crown className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="group relative overflow-hidden border-0 shadow-xl bg-gradient-to-br from-green-50 via-emerald-50 to-green-50 dark:from-green-950/50 dark:via-emerald-950/50 dark:to-green-950/50 hover:shadow-2xl transition-all duration-500 hover:scale-105">
            <div className="absolute inset-0 bg-gradient-to-r from-green-500/10 to-emerald-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <CardContent className="p-4 relative z-10">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs font-semibold text-green-600 dark:text-green-400 mb-1">
                    Kasir
                  </p>
                  <p className="text-3xl font-bold text-green-700 dark:text-green-300 mb-1">
                    {cashierCount}
                  </p>
                  <div className="flex items-center gap-2 text-xs text-green-600/70 dark:text-green-400/70">
                    <Target className="h-3 w-3" />
                    <span>Akses kasir</span>
                  </div>
                </div>
                <div className="h-12 w-12 rounded-xl bg-gradient-to-br from-green-500 to-emerald-600 shadow-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <User className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="group relative overflow-hidden border-0 shadow-xl bg-gradient-to-br from-orange-50 via-amber-50 to-orange-50 dark:from-orange-950/50 dark:via-amber-950/50 dark:to-orange-950/50 hover:shadow-2xl transition-all duration-500 hover:scale-105">
            <div className="absolute inset-0 bg-gradient-to-r from-orange-500/10 to-amber-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <CardContent className="p-4 relative z-10">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs font-semibold text-orange-600 dark:text-orange-400 mb-1">
                    Aktif Hari Ini
                  </p>
                  <p className="text-3xl font-bold text-orange-700 dark:text-orange-300 mb-1">
                    {employees.length}
                  </p>
                  <div className="flex items-center gap-2 text-xs text-orange-600/70 dark:text-orange-400/70">
                    <Zap className="h-3 w-3" />
                    <span>Online</span>
                  </div>
                </div>
                <div className="h-12 w-12 rounded-xl bg-gradient-to-br from-orange-500 to-amber-600 shadow-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <Activity className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Enhanced Main Content */}
        <Card className="border-0 shadow-2xl backdrop-blur-sm bg-white/80 dark:bg-slate-900/80 overflow-hidden">
          <CardHeader className="border-b border-gray-100 dark:border-gray-800 bg-gradient-to-r from-slate-50 to-white dark:from-slate-900 dark:to-slate-800 p-6 lg:p-8">
            <div className="flex flex-col xl:flex-row xl:items-center xl:justify-between gap-6">
              <div>
                <CardTitle className="text-2xl lg:text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-gray-100 dark:to-gray-400 bg-clip-text text-transparent">
                  Daftar Karyawan
                </CardTitle>
                <CardDescription className="text-base mt-2">
                  Kelola karyawan dan akses mereka ke sistem dengan mudah
                </CardDescription>
              </div>
              <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-4">
                <div className="relative">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <Input
                    placeholder="Cari nama atau ID karyawan..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-12 w-full sm:w-80 h-12 rounded-xl border-0 bg-gray-50 dark:bg-gray-800 focus:ring-2 focus:ring-orange-500 transition-all duration-300"
                  />
                </div>
                <Button
                  className="h-12 px-6 bg-gradient-to-r from-orange-500 to-amber-600 hover:from-orange-600 hover:to-amber-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                  onClick={() => setIsAddDialogOpen(true)}
                >
                  <UserPlus className="h-5 w-5 mr-2" />
                  Tambah Karyawan
                </Button>
              </div>
            </div>
          </CardHeader>

          <Tabs
            value={activeTab}
            onValueChange={handleTabChange}
            className="w-full"
          >
            <div className="px-6 lg:px-8 pt-6 border-b border-gray-100 dark:border-gray-800">
              <TabsList className="grid w-full grid-cols-3 h-12 rounded-xl bg-gray-100 dark:bg-gray-800 p-1">
                <TabsTrigger
                  value="all"
                  className={cn(
                    "flex items-center justify-center gap-2 h-10 rounded-lg font-semibold transition-all duration-300",
                    activeTab === "all"
                      ? "bg-white dark:bg-gray-700 shadow-md text-gray-900 dark:text-gray-100"
                      : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"
                  )}
                >
                  <Users className="h-4 w-4" />
                  <span className="hidden sm:inline">Semua</span>
                  <Badge
                    variant="secondary"
                    className={cn(
                      "ml-2 h-6 px-2 rounded-md font-bold transition-all duration-300",
                      activeTab === "all"
                        ? "bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-200"
                        : "bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400"
                    )}
                  >
                    {filteredEmployees.length}
                  </Badge>
                </TabsTrigger>
                <TabsTrigger
                  value="admin"
                  className={cn(
                    "flex items-center justify-center gap-2 h-10 rounded-lg font-semibold transition-all duration-300",
                    activeTab === "admin"
                      ? "bg-white dark:bg-gray-700 shadow-md text-gray-900 dark:text-gray-100"
                      : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"
                  )}
                >
                  <Crown className="h-4 w-4" />
                  <span className="hidden sm:inline">Admin</span>
                  <Badge
                    variant="secondary"
                    className={cn(
                      "ml-2 h-6 px-2 rounded-md font-bold transition-all duration-300",
                      activeTab === "admin"
                        ? "bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300"
                        : "bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400"
                    )}
                  >
                    {filteredEmployees.filter((e) => e.role === "ADMIN").length}
                  </Badge>
                </TabsTrigger>
                <TabsTrigger
                  value="cashier"
                  className={cn(
                    "flex items-center justify-center gap-2 h-10 rounded-lg font-semibold transition-all duration-300",
                    activeTab === "cashier"
                      ? "bg-white dark:bg-gray-700 shadow-md text-gray-900 dark:text-gray-100"
                      : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"
                  )}
                >
                  <User className="h-4 w-4" />
                  <span className="hidden sm:inline">Kasir</span>
                  <Badge
                    variant="secondary"
                    className={cn(
                      "ml-2 h-6 px-2 rounded-md font-bold transition-all duration-300",
                      activeTab === "cashier"
                        ? "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300"
                        : "bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400"
                    )}
                  >
                    {
                      filteredEmployees.filter((e) => e.role === "CASHIER")
                        .length
                    }
                  </Badge>
                </TabsTrigger>
              </TabsList>
            </div>

            <CardContent className="p-6 lg:p-8">
              <TabsContent value="all" className="mt-0">
                <EmployeeTable
                  employees={filteredEmployees}
                  onEditName={handleEditNameClick}
                  onEditPassword={handleEditPasswordClick}
                  onDelete={handleDeleteEmployee}
                />
              </TabsContent>

              <TabsContent value="admin" className="mt-0">
                <EmployeeTable
                  employees={filteredEmployees.filter(
                    (e) => e.role === "ADMIN"
                  )}
                  onEditName={handleEditNameClick}
                  onEditPassword={handleEditPasswordClick}
                  onDelete={handleDeleteEmployee}
                />
              </TabsContent>

              <TabsContent value="cashier" className="mt-0">
                <EmployeeTable
                  employees={filteredEmployees.filter(
                    (e) => e.role === "CASHIER"
                  )}
                  onEditName={handleEditNameClick}
                  onEditPassword={handleEditPasswordClick}
                  onDelete={handleDeleteEmployee}
                />
              </TabsContent>
            </CardContent>
          </Tabs>
        </Card>

        {/* Dialogs */}
        <AddEmployeeDialog
          open={isAddDialogOpen}
          onOpenChange={setIsAddDialogOpen}
          onSuccess={fetchEmployees}
        />

        <EditEmployeeNameDialog
          employee={selectedEmployee}
          open={isEditNameDialogOpen}
          onOpenChange={setIsEditNameDialogOpen}
          onSuccess={fetchEmployees}
        />

        <EditEmployeePasswordDialog
          employee={selectedEmployee}
          open={isEditPasswordDialogOpen}
          onOpenChange={setIsEditPasswordDialogOpen}
        />
      </div>
    </div>
  );
}
