"use client";

import { useState, useEffect } from "react";
import {
  Bell,
  AlertCircle,
  Mail,
  Smartphone,
  AlertTriangle,
  Info,
  Send,
  ShoppingCart,
  Package,
  Wrench,
  Clock,
  BarChart,
  Settings,
  Volume2,
  VolumeX,
  Zap,
  CheckCircle,
  Star,
  MessageSquare,
} from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { Skeleton } from "@/components/ui/skeleton";
import {
  getNotificationSettings,
  updateNotificationSettings,
  sendTestNotificationEmail,
  sendTestDailyReportEmail,
  type NotificationSettingsType,
} from "@/actions/notifications/settings";

export default function NotificationsSettings() {
  const [settings, setSettings] = useState<NotificationSettingsType>({
    emailEnabled: true,
    emailInfoEnabled: true,
    emailWarningEnabled: true,
    emailSuccessEnabled: true,
    emailErrorEnabled: true,
    emailPurchaseEnabled: true,
    emailProductAdditionEnabled: true,
    emailServiceAdditionEnabled: true,
    dailySummary: false,
    weeklySummary: true,
    dailyReports: false,
    dailyReportsTime: "17:00",
    operatingHoursStart: "08:00",
    operatingHoursEnd: "17:00",
    notificationHours: ["09:00", "12:00", "17:00"],
  });

  const [showError, setShowError] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(true);
  const [isSendingTest, setIsSendingTest] = useState(false);
  const [isSendingDailyTest, setIsSendingDailyTest] = useState(false);

  useEffect(() => {
    async function fetchSettings() {
      setIsFetching(true);
      try {
        const result = await getNotificationSettings();
        if (result.success && result.data) {
          setSettings(result.data);
        } else {
          setErrorMessage(result.error || "Gagal memuat pengaturan notifikasi");
          setShowError(true);
        }
      } catch (error) {
        console.error("Error fetching notification settings:", error);
        setErrorMessage("Terjadi kesalahan saat memuat pengaturan");
        setShowError(true);
      } finally {
        setIsFetching(false);
      }
    }

    fetchSettings();
  }, []);

  const handleSaveSettings = async () => {
    setIsLoading(true);
    setShowError(false);

    try {
      const result = await updateNotificationSettings(settings);

      if (result.success) {
        toast.success("Pengaturan notifikasi berhasil disimpan!");
      } else {
        setErrorMessage(
          result.error || "Terjadi kesalahan saat menyimpan pengaturan"
        );
        setShowError(true);
        toast.error("Terjadi kesalahan saat menyimpan pengaturan.");
      }
    } catch (error) {
      console.error("Error saving notification settings:", error);
      setErrorMessage("Terjadi kesalahan saat menyimpan pengaturan");
      setShowError(true);
      toast.error("Terjadi kesalahan saat menyimpan pengaturan.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendTestEmail = async () => {
    setIsSendingTest(true);

    try {
      const result = await sendTestNotificationEmail();

      if (result.success) {
        toast.success("Email uji coba berhasil dikirim!");
      } else {
        toast.error(result.error || "Gagal mengirim email uji coba");
      }
    } catch (error) {
      console.error("Error sending test email:", error);
      toast.error("Terjadi kesalahan saat mengirim email uji coba");
    } finally {
      setIsSendingTest(false);
    }
  };

  const handleSendTestDailyReport = async () => {
    setIsSendingDailyTest(true);

    try {
      const result = await sendTestDailyReportEmail();

      if (result.success) {
        toast.success("Email laporan harian uji coba berhasil dikirim!");
      } else {
        toast.error(
          result.error || "Gagal mengirim email laporan harian uji coba"
        );
      }
    } catch (error) {
      console.error("Error sending test daily report email:", error);
      toast.error(
        "Terjadi kesalahan saat mengirim email laporan harian uji coba"
      );
    } finally {
      setIsSendingDailyTest(false);
    }
  };

  const notificationTypes = [
    {
      id: "emailSuccessEnabled",
      label: "Pemberitahuan Penjualan",
      description: "Notifikasi saat ada penjualan baru",
      icon: ShoppingCart,
      color: "green",
    },
    {
      id: "emailWarningEnabled",
      label: "Peringatan Inventaris",
      description: "Notifikasi saat stok produk hampir habis",
      icon: AlertTriangle,
      color: "amber",
    },
    {
      id: "emailPurchaseEnabled",
      label: "Pemberitahuan Pembelian",
      description: "Notifikasi saat ada pembelian baru",
      icon: ShoppingCart,
      color: "blue",
    },
    {
      id: "emailProductAdditionEnabled",
      label: "Penambahan Produk",
      description: "Notifikasi saat produk baru ditambahkan",
      icon: Package,
      color: "purple",
    },
    {
      id: "emailServiceAdditionEnabled",
      label: "Penambahan Servis",
      description: "Notifikasi saat servis baru ditambahkan",
      icon: Wrench,
      color: "orange",
    },
  ];

  return (
    <div className="space-y-6 min-h-[100vh]">
      {/* Hero Header */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 p-8 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10">
          <div className="flex items-center gap-4 mb-4">
            <div className="h-12 w-12 rounded-xl bg-white/20 backdrop-blur-sm flex items-center justify-center">
              <Bell className="h-6 w-6" />
            </div>
            <div>
              <h1 className="text-2xl font-bold">Pengaturan Notifikasi</h1>
              <p className="text-white/80">
                Kelola preferensi pemberitahuan dan komunikasi
              </p>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Zap className="h-4 w-4" />
              <span className="text-sm">Notifikasi real-time</span>
            </div>
            <div className="flex items-center gap-2">
              <Mail className="h-4 w-4" />
              <span className="text-sm">Email otomatis</span>
            </div>
          </div>
        </div>
      </div>

      <Tabs defaultValue="email" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="email" className="flex items-center gap-2">
            <Mail className="h-4 w-4" />
            Email
          </TabsTrigger>
          <TabsTrigger value="summary" className="flex items-center gap-2">
            <BarChart className="h-4 w-4" />
            Ringkasan
          </TabsTrigger>
          <TabsTrigger value="schedule" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Jadwal
          </TabsTrigger>
          <TabsTrigger value="preferences" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Preferensi
          </TabsTrigger>
        </TabsList>

        <TabsContent value="email" className="space-y-6">
          {/* Master Email Toggle */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-xl">Notifikasi Email</CardTitle>
                  <CardDescription>
                    Aktifkan atau nonaktifkan semua notifikasi email
                  </CardDescription>
                </div>
                <Badge
                  variant="outline"
                  className={`${settings.emailEnabled ? "bg-green-50 text-green-700 border-green-200" : "bg-red-50 text-red-700 border-red-200"}`}
                >
                  {settings.emailEnabled ? (
                    <>
                      <Volume2 className="h-3 w-3 mr-1" />
                      Aktif
                    </>
                  ) : (
                    <>
                      <VolumeX className="h-3 w-3 mr-1" />
                      Nonaktif
                    </>
                  )}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between p-6 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200 dark:border-blue-800">
                <div className="flex items-center gap-4">
                  <div className="h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                    <Mail className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h4 className="text-base font-medium">
                      Master Notifikasi Email
                    </h4>
                    <p className="text-sm text-muted-foreground mt-1">
                      Kontrol utama untuk semua notifikasi email
                    </p>
                  </div>
                </div>
                {isFetching ? (
                  <Skeleton className="h-6 w-11" />
                ) : (
                  <Switch
                    checked={settings.emailEnabled}
                    onCheckedChange={(checked) =>
                      setSettings({ ...settings, emailEnabled: checked })
                    }
                    className="cursor-pointer"
                  />
                )}
              </div>
            </CardContent>
          </Card>

          {/* Individual Email Settings */}
          {settings.emailEnabled && !isFetching && (
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="text-xl">Jenis Notifikasi</CardTitle>
                <CardDescription>
                  Pilih jenis notifikasi yang ingin Anda terima
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {notificationTypes.map((type, index) => (
                  <div key={type.id}>
                    <div className="flex items-center justify-between p-4 rounded-xl bg-gradient-to-r from-gray-50 to-white dark:from-gray-800 dark:to-gray-700 border border-gray-200 dark:border-gray-600 hover:shadow-md transition-all duration-200">
                      <div className="flex items-start gap-4">
                        <div
                          className={`mt-0.5 p-2 rounded-lg bg-gradient-to-br from-${type.color}-100 to-${type.color}-200 dark:from-${type.color}-900/30 dark:to-${type.color}-800/30`}
                        >
                          <type.icon
                            className={`h-5 w-5 text-${type.color}-600 dark:text-${type.color}-400`}
                          />
                        </div>
                        <div className="flex-1">
                          <Label
                            htmlFor={type.id}
                            className="text-base font-medium cursor-pointer"
                          >
                            {type.label}
                          </Label>
                          <p className="text-sm text-muted-foreground mt-1">
                            {type.description}
                          </p>
                        </div>
                      </div>
                      <Switch
                        id={type.id}
                        checked={
                          settings[type.id as keyof typeof settings] as boolean
                        }
                        onCheckedChange={(checked) =>
                          setSettings({
                            ...settings,
                            [type.id]: checked,
                          })
                        }
                        className="cursor-pointer"
                      />
                    </div>
                    {index < notificationTypes.length - 1 && (
                      <Separator className="my-4" />
                    )}
                  </div>
                ))}
              </CardContent>
              <CardFooter>
                <Button
                  variant="outline"
                  onClick={handleSendTestEmail}
                  disabled={isSendingTest || !settings.emailEnabled}
                  className="cursor-pointer"
                >
                  <Send className="h-4 w-4 mr-2" />
                  {isSendingTest ? "Mengirim..." : "Kirim Email Uji Coba"}
                </Button>
              </CardFooter>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="summary" className="space-y-6">
          {/* Summary Settings */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="text-xl">Laporan Ringkasan</CardTitle>
              <CardDescription>
                Atur frekuensi laporan ringkasan aktivitas bisnis
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-center justify-between p-6 bg-gradient-to-br from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-xl border border-indigo-200 dark:border-indigo-800">
                  <div className="flex items-center gap-4">
                    <div className="h-10 w-10 rounded-full bg-indigo-100 dark:bg-indigo-900/30 flex items-center justify-center">
                      <BarChart className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
                    </div>
                    <div>
                      <h4 className="text-base font-medium">
                        Ringkasan Harian
                      </h4>
                      <p className="text-sm text-muted-foreground mt-1">
                        Laporan aktivitas setiap hari
                      </p>
                    </div>
                  </div>
                  {isFetching ? (
                    <Skeleton className="h-6 w-11" />
                  ) : (
                    <Switch
                      checked={settings.dailySummary}
                      onCheckedChange={(checked) =>
                        setSettings({ ...settings, dailySummary: checked })
                      }
                      className="cursor-pointer"
                    />
                  )}
                </div>

                <div className="flex items-center justify-between p-6 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl border border-green-200 dark:border-green-800">
                  <div className="flex items-center gap-4">
                    <div className="h-10 w-10 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                      <BarChart className="h-5 w-5 text-green-600 dark:text-green-400" />
                    </div>
                    <div>
                      <h4 className="text-base font-medium">
                        Ringkasan Mingguan
                      </h4>
                      <p className="text-sm text-muted-foreground mt-1">
                        Laporan aktivitas setiap minggu
                      </p>
                    </div>
                  </div>
                  {isFetching ? (
                    <Skeleton className="h-6 w-11" />
                  ) : (
                    <Switch
                      checked={settings.weeklySummary}
                      onCheckedChange={(checked) =>
                        setSettings({ ...settings, weeklySummary: checked })
                      }
                      className="cursor-pointer"
                    />
                  )}
                </div>
              </div>

              {/* Daily Reports */}
              <div className="p-6 bg-gradient-to-br from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20 rounded-xl border border-orange-200 dark:border-orange-800">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-4">
                    <div className="h-10 w-10 rounded-full bg-orange-100 dark:bg-orange-900/30 flex items-center justify-center">
                      <Mail className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                    </div>
                    <div>
                      <h4 className="text-base font-medium">
                        Laporan Harian Otomatis
                      </h4>
                      <p className="text-sm text-muted-foreground mt-1">
                        Terima laporan harian dengan lampiran Excel
                      </p>
                    </div>
                  </div>
                  {isFetching ? (
                    <Skeleton className="h-6 w-11" />
                  ) : (
                    <Switch
                      checked={settings.dailyReports}
                      onCheckedChange={(checked) =>
                        setSettings({ ...settings, dailyReports: checked })
                      }
                      className="cursor-pointer"
                    />
                  )}
                </div>

                {settings.dailyReports && (
                  <div className="space-y-4 pt-4 border-t border-orange-200 dark:border-orange-700">
                    <div>
                      <Label className="text-sm font-medium text-muted-foreground">
                        Waktu Pengiriman
                      </Label>
                      <select
                        value={settings.dailyReportsTime}
                        onChange={(e) =>
                          setSettings({
                            ...settings,
                            dailyReportsTime: e.target.value,
                          })
                        }
                        className="mt-1 block w-full px-3 py-2 border border-input bg-background rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
                      >
                        <option value="08:00">08:00 WIB (Pagi)</option>
                        <option value="12:00">12:00 WIB (Siang)</option>
                        <option value="17:00">17:00 WIB (Sore)</option>
                        <option value="20:00">20:00 WIB (Malam)</option>
                      </select>
                    </div>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleSendTestDailyReport}
                      disabled={isSendingDailyTest || !settings.emailEnabled}
                      className="cursor-pointer"
                    >
                      <Send className="h-4 w-4 mr-2" />
                      {isSendingDailyTest
                        ? "Mengirim..."
                        : "Kirim Laporan Uji Coba"}
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="schedule" className="space-y-6">
          {/* Schedule Settings */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Operating Hours */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="text-xl">Jam Operasional</CardTitle>
                <CardDescription>
                  Atur jam operasional bisnis Anda
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
                    Jam Buka
                  </Label>
                  <select
                    value={settings.operatingHoursStart}
                    onChange={(e) =>
                      setSettings({
                        ...settings,
                        operatingHoursStart: e.target.value,
                      })
                    }
                    className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
                  >
                    {Array.from({ length: 24 }, (_, i) => {
                      const hour = String(i).padStart(2, "0");
                      return (
                        <option key={hour} value={`${hour}:00`}>
                          {hour}:00
                        </option>
                      );
                    })}
                  </select>
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
                    Jam Tutup
                  </Label>
                  <select
                    value={settings.operatingHoursEnd}
                    onChange={(e) =>
                      setSettings({
                        ...settings,
                        operatingHoursEnd: e.target.value,
                      })
                    }
                    className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
                  >
                    {Array.from({ length: 24 }, (_, i) => {
                      const hour = String(i).padStart(2, "0");
                      return (
                        <option key={hour} value={`${hour}:00`}>
                          {hour}:00
                        </option>
                      );
                    })}
                  </select>
                </div>
              </CardContent>
            </Card>

            {/* Notification Hours */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="text-xl">Jadwal Notifikasi</CardTitle>
                <CardDescription>
                  Pilih jam untuk mengirim notifikasi otomatis
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-2">
                  {[
                    "08:00",
                    "09:00",
                    "10:00",
                    "11:00",
                    "12:00",
                    "13:00",
                    "14:00",
                    "15:00",
                    "16:00",
                    "17:00",
                    "18:00",
                    "19:00",
                  ].map((hour) => (
                    <label
                      key={hour}
                      className="flex items-center space-x-2 p-2 rounded border hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer"
                    >
                      <input
                        type="checkbox"
                        checked={settings.notificationHours.includes(hour)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSettings({
                              ...settings,
                              notificationHours: [
                                ...settings.notificationHours,
                                hour,
                              ],
                            });
                          } else {
                            setSettings({
                              ...settings,
                              notificationHours:
                                settings.notificationHours.filter(
                                  (h) => h !== hour
                                ),
                            });
                          }
                        }}
                        className="rounded border-gray-300 text-primary focus:ring-primary"
                      />
                      <span className="text-sm">{hour}</span>
                    </label>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="preferences" className="space-y-6">
          {/* Additional Preferences */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="text-xl">Preferensi Tambahan</CardTitle>
              <CardDescription>
                Pengaturan lanjutan untuk notifikasi
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="p-4 rounded-xl bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 border border-blue-200 dark:border-blue-800">
                  <div className="flex items-center gap-3 mb-3">
                    <MessageSquare className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    <h4 className="font-medium">Format Notifikasi</h4>
                  </div>
                  <p className="text-sm text-muted-foreground mb-3">
                    Pilih format notifikasi yang diinginkan
                  </p>
                  <select className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm">
                    <option>HTML (Kaya fitur)</option>
                    <option>Teks Biasa</option>
                  </select>
                </div>

                <div className="p-4 rounded-xl bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800">
                  <div className="flex items-center gap-3 mb-3">
                    <Star className="h-5 w-5 text-green-600 dark:text-green-400" />
                    <h4 className="font-medium">Prioritas Notifikasi</h4>
                  </div>
                  <p className="text-sm text-muted-foreground mb-3">
                    Atur prioritas untuk jenis notifikasi
                  </p>
                  <select className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm">
                    <option>Tinggi</option>
                    <option>Normal</option>
                    <option>Rendah</option>
                  </select>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Error Alert */}
      {showError && (
        <Card className="border-destructive border-0 shadow-lg">
          <CardContent className="pt-6">
            <div className="flex items-center gap-3 text-destructive">
              <AlertCircle className="h-5 w-5" />
              <p className="text-sm font-medium">
                {errorMessage || "Terjadi kesalahan saat menyimpan pengaturan."}
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Save Button */}
      <Card className="border-0 shadow-lg">
        <CardFooter className="flex justify-end">
          <Button
            onClick={handleSaveSettings}
            disabled={isLoading || isFetching}
            className="bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white cursor-pointer"
          >
            {isLoading ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                Menyimpan...
              </>
            ) : (
              <>
                <CheckCircle className="h-4 w-4 mr-2" />
                Simpan Perubahan
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
