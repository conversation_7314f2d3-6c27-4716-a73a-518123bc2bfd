"use client";

import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Sparkles } from "lucide-react";

interface BillingToggleProps {
  billingPeriod: "monthly" | "annual";
  onBillingPeriodChange: (period: "monthly" | "annual") => void;
}

export default function BillingToggle({
  billingPeriod,
  onBillingPeriodChange,
}: BillingToggleProps) {
  return (
    <div className="flex flex-col items-center space-y-4">
      <div className="text-center">
        <h3 className="text-lg font-semibold text-foreground mb-2">
          Choose Your Billing Cycle
        </h3>
        <p className="text-sm text-muted-foreground">
          Save more with annual billing
        </p>
      </div>

      <Tabs
        value={billingPeriod}
        onValueChange={(value) => onBillingPeriodChange(value as "monthly" | "annual")}
        className="w-full max-w-md"
      >
        <TabsList className="grid w-full grid-cols-2 h-12 p-1 bg-muted/50">
          <TabsTrigger
            value="monthly"
            className="relative font-medium data-[state=active]:bg-background data-[state=active]:shadow-sm"
          >
            Monthly
          </TabsTrigger>
          <TabsTrigger
            value="annual"
            className="relative font-medium data-[state=active]:bg-background data-[state=active]:shadow-sm"
          >
            <div className="flex items-center gap-2">
              Annual
              <Badge
                variant="secondary"
                className="bg-gradient-to-r from-green-500 to-emerald-500 text-white border-0 text-xs px-2 py-0.5"
              >
                <Sparkles className="h-3 w-3 mr-1" />
                Save 10%
              </Badge>
            </div>
          </TabsTrigger>
        </TabsList>
      </Tabs>

      {billingPeriod === "annual" && (
        <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
          <p className="text-sm text-green-700 dark:text-green-300 font-medium">
            🎉 You'll save 10% with annual billing!
          </p>
        </div>
      )}
    </div>
  );
}