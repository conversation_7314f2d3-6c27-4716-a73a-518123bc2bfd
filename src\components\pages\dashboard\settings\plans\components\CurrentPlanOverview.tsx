"use client";

import { useRouter } from "next/navigation";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Clock,
  CreditCard,
  Zap,
  TrendingUp,
  Calendar,
  Receipt,
  Info,
} from "lucide-react";

interface CurrentPlanOverviewProps {
  initialData?: {
    plan: string;
    expiryDate: string | null;
    isActive: boolean;
  };
  currentPlanDetails: {
    name: string;
    price: string;
    features: string[];
  };
  formatDate: (dateString: string | null) => string;
}

export default function CurrentPlanOverview({
  initialData,
  currentPlanDetails,
  formatDate,
}: CurrentPlanOverviewProps) {
  const router = useRouter();

  if (!initialData) {
    return null;
  }

  // Calculate days until expiry
  const daysUntilExpiry = initialData.expiryDate
    ? Math.ceil(
        (new Date(initialData.expiryDate).getTime() - new Date().getTime()) /
          (1000 * 60 * 60 * 24)
      )
    : null;

  // Mock usage data - in real app this would come from props
  const usageData = {
    storage: { used: 2.3, total: 10, unit: "GB" },
    users: { used: 3, total: 10, unit: "users" },
    transactions: { used: 150, total: 1000, unit: "transactions" },
  };

  return (
    <Card className="overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5 border-primary/20">
      <CardHeader className="pb-4">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div className="flex items-center gap-4">
            <div className="h-12 w-12 rounded-xl bg-gradient-to-br from-primary to-primary/80 flex items-center justify-center shadow-lg">
              <Zap className="h-6 w-6 text-primary-foreground" />
            </div>
            <div>
              <CardTitle className="text-xl font-bold">
                {currentPlanDetails.name} Plan
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Your current subscription
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Badge
              variant={initialData.isActive ? "default" : "destructive"}
              className="px-3 py-1"
            >
              {initialData.isActive ? "Active" : "Inactive"}
            </Badge>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <Info className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>View detailed billing information</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center gap-3 p-4 bg-background/60 rounded-xl border border-border/50">
            <div className="h-10 w-10 rounded-lg bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
              <Calendar className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <p className="text-xs text-muted-foreground">Expires</p>
              <p className="font-semibold text-sm">
                {initialData.expiryDate ? formatDate(initialData.expiryDate) : "Never"}
              </p>
              {daysUntilExpiry && daysUntilExpiry > 0 && (
                <p className="text-xs text-muted-foreground">
                  {daysUntilExpiry} days left
                </p>
              )}
            </div>
          </div>

          <div className="flex items-center gap-3 p-4 bg-background/60 rounded-xl border border-border/50">
            <div className="h-10 w-10 rounded-lg bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
              <CreditCard className="h-5 w-5 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <p className="text-xs text-muted-foreground">Price</p>
              <p className="font-semibold text-sm">{currentPlanDetails.price}</p>
            </div>
          </div>

          <div className="flex items-center gap-3 p-4 bg-background/60 rounded-xl border border-border/50">
            <div className="h-10 w-10 rounded-lg bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center">
              <TrendingUp className="h-5 w-5 text-purple-600 dark:text-purple-400" />
            </div>
            <div>
              <p className="text-xs text-muted-foreground">Status</p>
              <p className="font-semibold text-sm">
                {initialData.isActive ? "Active" : "Inactive"}
              </p>
            </div>
          </div>
        </div>

        {/* Usage Overview */}
        <div className="space-y-4">
          <h4 className="font-semibold text-sm">Usage Overview</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {Object.entries(usageData).map(([key, data]) => {
              const percentage = (data.used / data.total) * 100;
              return (
                <div key={key} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium capitalize">{key}</span>
                    <span className="text-xs text-muted-foreground">
                      {data.used} / {data.total} {data.unit}
                    </span>
                  </div>
                  <Progress value={percentage} className="h-2" />
                  <p className="text-xs text-muted-foreground">
                    {percentage.toFixed(0)}% used
                  </p>
                </div>
              );
            })}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t border-border/50">
          <Button
            variant="default"
            className="flex-1"
            onClick={() => router.push("/dashboard/settings/billing")}
          >
            <Receipt className="h-4 w-4 mr-2" />
            Manage Billing
          </Button>
          <Button
            variant="outline"
            className="flex-1"
            onClick={() => router.push("/dashboard/settings/plans")}
          >
            <TrendingUp className="h-4 w-4 mr-2" />
            Upgrade Plan
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}