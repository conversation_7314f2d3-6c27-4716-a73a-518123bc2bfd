"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Receipt,
  Sparkles,
  Info,
  HelpCircle,
  TrendingUp,
  Shield,
  Clock,
} from "lucide-react";
import { SubscriptionPlan } from "@prisma/client";
import {
  SUBSCRIPTION_PLANS,
  ANNUAL_SUBSCRIPTION_PLANS,
  isUserInTrial,
  getPlanDetails,
} from "@/lib/subscription";
import { getSubscriptionInfo } from "@/actions/users/subscription-info";
import { formatCurrency, getDiscountBadgeText } from "@/lib/discount-config";
import FullPageLoading from "@/components/ui/full-page-loading";

// Import new components
import PlanCard from "./components/PlanCard";
import BillingToggle from "./components/BillingToggle";
import CurrentPlanOverview from "./components/CurrentPlanOverview";
import PlanComparison from "./components/PlanComparison";
import { Progress } from "@/components/ui/progress";

interface PlansSettingsProps {
  initialData?: {
    plan: SubscriptionPlan;
    expiryDate: string | null;
    isActive: boolean;
    trialStartDate?: Date | null;
    trialEndDate?: Date | null;
    isTrialActive?: boolean | null;
  };
}

export default function PlansSettings({ initialData }: PlansSettingsProps) {
  const router = useRouter();
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(
    initialData?.plan || null
  );
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingPlan, setProcessingPlan] = useState<SubscriptionPlan | null>(
    null
  );
  const [billingPeriod, setBillingPeriod] = useState<"monthly" | "annual">(
    "monthly"
  );
  const [showComparison, setShowComparison] = useState(false);
  const [usageData, setUsageData] = useState<any>(null);
  const [loadingUsage, setLoadingUsage] = useState(true);

  // Fetch usage data
  useEffect(() => {
    const fetchUsageData = async () => {
      try {
        const result = await getSubscriptionInfo();
        if (result.success && result.data) {
          setUsageData(result.data);
        }
      } catch (error) {
        console.error("Error fetching usage data:", error);
      } finally {
        setLoadingUsage(false);
      }
    };

    fetchUsageData();
  }, []);

  // Format date for display
  const formatDate = (dateString: string | null) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString("id-ID", {
      day: "numeric",
      month: "long",
      year: "numeric",
    });
  };

  // Get plan details from centralized configuration
  const getPlanDetails = (
    plan: SubscriptionPlan,
    isAnnual: boolean = false
  ) => {
    const planConfig = isAnnual
      ? ANNUAL_SUBSCRIPTION_PLANS[plan]
      : SUBSCRIPTION_PLANS[plan];
    if (!planConfig) {
      return {
        name: "Unknown",
        price: "N/A",
        originalPrice: null,
        savings: null,
        discountPercentage: null,
        features: [],
        limitations: [],
      };
    }

    const price =
      planConfig.price === 0
        ? "Rp 0"
        : `Rp ${planConfig.price.toLocaleString("id-ID")}/${planConfig.period.replace("per ", "")}`;

    return {
      name: planConfig.name,
      price,
      originalPrice: (planConfig as any).originalPrice || null,
      savings: (planConfig as any).savings || null,
      discountPercentage: (planConfig as any).discountPercentage || null,
      features: planConfig.features,
      limitations: planConfig.limitations,
    };
  };

  const currentPlanDetails = initialData
    ? getPlanDetails(initialData.plan)
    : getPlanDetails("BASIC");

  // Handle subscription upgrade
  const handleSubscribe = async (
    plan: SubscriptionPlan,
    isAnnual: boolean = false
  ) => {
    if (plan === initialData?.plan && !isAnnual) {
      toast.info("You are already using this plan");
      return;
    }

    setSelectedPlan(plan);
    setProcessingPlan(plan);
    setIsProcessing(true);

    try {
      const response = await fetch("/api/subscriptions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ plan, isAnnual }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to subscribe");
      }

      if (data.snapToken) {
        console.log("🚀 [PLANS] Opening Midtrans Snap payment:", {
          plan,
          snapToken: data.snapToken ? "token_available" : "no_token",
        });

        if (typeof window !== "undefined" && (window as any).snap) {
          (window as any).snap.pay(data.snapToken, {
            onSuccess: function (result: any) {
              console.log("✅ [PLANS] Payment successful:", result);
              setIsProcessing(false);
              setProcessingPlan(null);
              toast.success(
                `Payment successful! ${getPlanDetails(plan, isAnnual).name} plan is now active.`
              );
              router.push(
                "/dashboard/settings/billing/success?payment_id=" +
                  data.payment?.id
              );
            },
            onPending: function (result: any) {
              console.log("⏳ [PLANS] Payment pending:", result);
              setIsProcessing(false);
              setProcessingPlan(null);
              toast.info("Payment is being processed...");
              router.push(
                "/dashboard/settings/billing/pending?payment_id=" +
                  data.payment?.id
              );
            },
            onError: function (result: any) {
              console.error("❌ [PLANS] Payment error:", result);
              setIsProcessing(false);
              setProcessingPlan(null);
              toast.error("Payment failed. Please try again.");
            },
            onClose: function () {
              console.log("ℹ️ [PLANS] Payment popup closed");
              setIsProcessing(false);
              setProcessingPlan(null);
              toast.info("Payment cancelled.");
            },
          });
        } else {
          console.error("❌ [PLANS] Midtrans Snap not loaded");
          setIsProcessing(false);
          setProcessingPlan(null);
          toast.error("Payment system not ready. Please refresh the page.");
        }
      } else if (data.invoiceUrl) {
        console.log("🔄 [PLANS] Redirecting to payment URL");
        window.location.href = data.invoiceUrl;
      } else {
        setIsProcessing(false);
        setProcessingPlan(null);
        toast.success(
          `Successfully selected ${getPlanDetails(plan, isAnnual).name} plan`
        );
        router.refresh();
      }
    } catch (error) {
      setIsProcessing(false);
      setProcessingPlan(null);
      console.error("Error subscribing:", error);
      toast.error("Failed to subscribe. Please try again.");
    }
  };

  return (
    <TooltipProvider>
      <div className="min-h-screen bg-gradient-to-br from-background via-muted/20 to-background">
        <div className="container mx-auto px-4 py-8 space-y-8">
          {/* Hero Header */}
          <div className="text-center space-y-4">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 rounded-full text-primary text-sm font-medium">
              <Sparkles className="h-4 w-4" />
              Paket & Penagihan
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
              Pilih Paket yang Tepat untuk Anda
            </h1>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Buka fitur-fitur canggih dan kembangkan bisnis Anda dengan paket
              harga yang fleksibel
            </p>
          </div>

          {/* Current Plan Overview */}
          {initialData && (
            <CurrentPlanOverview
              initialData={initialData}
              currentPlanDetails={currentPlanDetails}
              formatDate={formatDate}
            />
          )}

          {/* Usage Overview */}
          {usageData && (
            <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-blue-200 dark:border-blue-800">
              <CardHeader>
                <CardTitle className="text-xl font-bold flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl">
                    <TrendingUp className="h-5 w-5 text-white" />
                  </div>
                  Ringkasan Penggunaan
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {/* Products Usage */}
                  <div className="text-center p-4 bg-white dark:bg-gray-800 rounded-xl shadow-sm">
                    <div className="text-2xl font-bold text-blue-600 mb-1">
                      {usageData.usage.currentProducts}
                      {usageData.limits.maxProducts
                        ? `/${usageData.limits.maxProducts}`
                        : ""}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                      Produk
                    </div>
                    {usageData.limits.maxProducts && (
                      <Progress
                        value={
                          (usageData.usage.currentProducts /
                            usageData.limits.maxProducts) *
                          100
                        }
                        className="h-2"
                      />
                    )}
                  </div>

                  {/* Transactions Usage */}
                  <div className="text-center p-4 bg-white dark:bg-gray-800 rounded-xl shadow-sm">
                    <div className="text-2xl font-bold text-green-600 mb-1">
                      {usageData.usage.currentTransactions}
                      {usageData.limits.maxTransactionsPerMonth
                        ? `/${usageData.limits.maxTransactionsPerMonth}`
                        : ""}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                      Transaksi Bulan Ini
                    </div>
                    {usageData.limits.maxTransactionsPerMonth && (
                      <Progress
                        value={
                          (usageData.usage.currentTransactions /
                            usageData.limits.maxTransactionsPerMonth) *
                          100
                        }
                        className="h-2"
                      />
                    )}
                  </div>

                  {/* Users Usage */}
                  <div className="text-center p-4 bg-white dark:bg-gray-800 rounded-xl shadow-sm">
                    <div className="text-2xl font-bold text-purple-600 mb-1">
                      {usageData.usage.currentUsers}
                      {usageData.limits.maxUsers
                        ? `/${usageData.limits.maxUsers}`
                        : ""}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                      Pengguna
                    </div>
                    {usageData.limits.maxUsers && (
                      <Progress
                        value={
                          (usageData.usage.currentUsers /
                            usageData.limits.maxUsers) *
                          100
                        }
                        className="h-2"
                      />
                    )}
                  </div>

                  {/* Customers + Suppliers */}
                  <div className="text-center p-4 bg-white dark:bg-gray-800 rounded-xl shadow-sm">
                    <div className="text-2xl font-bold text-orange-600 mb-1">
                      {usageData.usage.currentCustomers +
                        usageData.usage.currentSuppliers}
                      {usageData.limits.maxCustomers &&
                      usageData.limits.maxSuppliers
                        ? `/${usageData.limits.maxCustomers + usageData.limits.maxSuppliers}`
                        : ""}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                      Total Kontak
                    </div>
                    {usageData.limits.maxCustomers &&
                      usageData.limits.maxSuppliers && (
                        <Progress
                          value={
                            ((usageData.usage.currentCustomers +
                              usageData.usage.currentSuppliers) /
                              (usageData.limits.maxCustomers +
                                usageData.limits.maxSuppliers)) *
                            100
                          }
                          className="h-2"
                        />
                      )}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Trial Alert */}
          {initialData && isUserInTrial(initialData) && (
            <Alert className="border-amber-200 bg-amber-50 dark:bg-amber-900/20">
              <Clock className="h-4 w-4 text-amber-600" />
              <AlertTitle className="text-amber-800 dark:text-amber-200">
                Trial Aktif
              </AlertTitle>
              <AlertDescription className="text-amber-700 dark:text-amber-300">
                Anda memiliki{" "}
                {Math.ceil(
                  (new Date(initialData.trialEndDate!).getTime() -
                    new Date().getTime()) /
                    (1000 * 60 * 60 * 24)
                )}{" "}
                hari tersisa dalam trial. Upgrade sekarang untuk terus
                menggunakan semua fitur.
              </AlertDescription>
            </Alert>
          )}

          {/* Billing Toggle */}
          <div className="flex justify-center">
            <BillingToggle
              billingPeriod={billingPeriod}
              onBillingPeriodChange={setBillingPeriod}
            />
          </div>

          {/* Plans Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
            {(["BASIC", "PRO", "ENTERPRISE"] as SubscriptionPlan[]).map(
              (planKey) => (
                <PlanCard
                  key={planKey}
                  planKey={planKey}
                  planDetails={getPlanDetails(
                    planKey,
                    billingPeriod === "annual"
                  )}
                  isCurrentPlan={initialData?.plan === planKey}
                  isAnnual={billingPeriod === "annual"}
                  isProcessing={isProcessing}
                  processingPlan={processingPlan}
                  onSubscribe={handleSubscribe}
                  formatCurrency={formatCurrency}
                  getDiscountBadgeText={getDiscountBadgeText}
                />
              )
            )}
          </div>

          {/* Plan Comparison */}
          <div className="text-center space-y-4">
            <Button
              variant="outline"
              size="lg"
              onClick={() => setShowComparison(!showComparison)}
              className="gap-2"
            >
              <TrendingUp className="h-4 w-4" />
              {showComparison ? "Sembunyikan" : "Bandingkan"} Paket
            </Button>
          </div>

          {showComparison && (
            <PlanComparison
              isVisible={showComparison}
              onClose={() => setShowComparison(false)}
            />
          )}

          {/* Features Highlight */}
          <Card className="bg-gradient-to-r from-primary/5 to-secondary/5 border-primary/20">
            <CardHeader className="text-center">
              <CardTitle className="text-2xl font-bold flex items-center justify-center gap-2">
                <Shield className="h-6 w-6 text-primary" />
                Mengapa Memilih Paket Kami?
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center space-y-2">
                  <div className="h-12 w-12 rounded-xl bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center mx-auto">
                    <Shield className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <h3 className="font-semibold">Keamanan Enterprise</h3>
                  <p className="text-sm text-muted-foreground">
                    Keamanan tingkat bank dengan enkripsi end-to-end
                  </p>
                </div>
                <div className="text-center space-y-2">
                  <div className="h-12 w-12 rounded-xl bg-green-100 dark:bg-green-900/30 flex items-center justify-center mx-auto">
                    <TrendingUp className="h-6 w-6 text-green-600 dark:text-green-400" />
                  </div>
                  <h3 className="font-semibold">Pertumbuhan Skalabel</h3>
                  <p className="text-sm text-muted-foreground">
                    Paket yang berkembang sesuai kebutuhan bisnis Anda
                  </p>
                </div>
                <div className="text-center space-y-2">
                  <div className="h-12 w-12 rounded-xl bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center mx-auto">
                    <HelpCircle className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                  </div>
                  <h3 className="font-semibold">Dukungan 24/7</h3>
                  <p className="text-sm text-muted-foreground">
                    Dukungan ahli kapan pun Anda membutuhkannya
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Help Section */}
          <Card className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <div className="h-10 w-10 rounded-lg bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center flex-shrink-0">
                  <Info className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div className="space-y-2">
                  <h4 className="font-semibold text-blue-800 dark:text-blue-300">
                    Butuh bantuan memilih paket yang tepat?
                  </h4>
                  <p className="text-sm text-blue-700 dark:text-blue-400">
                    Tim kami siap membantu Anda menemukan paket yang sempurna
                    untuk kebutuhan bisnis Anda. Hubungi kami untuk rekomendasi
                    yang dipersonalisasi.
                  </p>
                  <div className="flex gap-2 pt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-blue-700 border-blue-300 hover:bg-blue-100"
                      onClick={() => router.push("/dashboard/settings/support")}
                    >
                      Hubungi Dukungan
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-blue-700 hover:bg-blue-100"
                      onClick={() => window.open("/docs/pricing", "_blank")}
                    >
                      Lihat Dokumentasi
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Separator className="my-8" />

          {/* Footer */}
          <div className="text-center text-sm text-muted-foreground">
            <p>
              All plans include a 14-day free trial. No credit card required.{" "}
              <Button
                variant="link"
                className="p-0 h-auto text-sm"
                onClick={() => router.push("/terms")}
              >
                Terms & Conditions
              </Button>
            </p>
          </div>
        </div>

        {/* Full Page Loading Overlay */}
        <FullPageLoading
          isVisible={isProcessing}
          title="Processing Payment..."
          description="Please wait while we process your subscription request"
        />
      </div>
    </TooltipProvider>
  );
}
