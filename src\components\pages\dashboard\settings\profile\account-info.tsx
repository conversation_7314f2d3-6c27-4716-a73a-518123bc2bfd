"use client";

import { format } from "date-fns";
import { id } from "date-fns/locale";
import {
  Mail,
  Shield,
  Calendar,
  Clock,
  Building2,
  Building,
} from "lucide-react";
import { User } from "./types";

interface AccountInfoProps {
  user: User;
}

export default function AccountInfo({ user }: AccountInfoProps) {
  // Helper function to format date
  const formatDate = (date: Date | null | undefined) => {
    if (!date) return "Tidak tersedia";
    return format(new Date(date), "d MMMM yyyy", { locale: id });
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden shadow-sm">
      <div className="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-base font-medium text-gray-900 dark:text-gray-100">
          Informasi Akun
        </h3>
      </div>
      <div className="px-4 py-5 sm:p-6">
        <dl className="grid grid-cols-1 gap-x-4 gap-y-6">
          <div className="sm:col-span-1">
            <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 flex items-center">
              <Mail className="h-4 w-4 mr-2" />
              Email
            </dt>
            <dd className="mt-1 text-sm text-gray-900 dark:text-gray-100">
              {user.email || "Tidak tersedia"}
            </dd>
          </div>
          <div className="sm:col-span-1">
            <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 flex items-center">
              <Building className="h-4 w-4 mr-2" />
              ID Perusahaan
            </dt>
            <dd className="mt-1 text-sm text-gray-900 dark:text-gray-100">
              {user.companyId || "Belum tersedia"}
            </dd>
          </div>
          <div className="sm:col-span-1">
            <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 flex items-center">
              <Shield className="h-4 w-4 mr-2" />
              Role
            </dt>
            <dd className="mt-1 text-sm text-gray-900 dark:text-gray-100">
              {user.role}
            </dd>
          </div>
          <div className="sm:col-span-1">
            <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 flex items-center">
              <Calendar className="h-4 w-4 mr-2" />
              Tanggal Bergabung
            </dt>
            <dd className="mt-1 text-sm text-gray-900 dark:text-gray-100">
              {formatDate(user.createdAt)}
            </dd>
          </div>
          <div className="sm:col-span-1">
            <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 flex items-center">
              <Clock className="h-4 w-4 mr-2" />
              Login Terakhir
            </dt>
            <dd className="mt-1 text-sm text-gray-900 dark:text-gray-100">
              {user.lastLogin ? formatDate(user.lastLogin) : "Tidak tersedia"}
            </dd>
          </div>
          {user.currentPlan && (
            <div className="sm:col-span-1">
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 flex items-center">
                <Building2 className="h-4 w-4 mr-2" />
                Paket Langganan
              </dt>
              <dd className="mt-1 text-sm text-gray-900 dark:text-gray-100">
                {user.currentPlan}
                {user.subscriptionExpiry && (
                  <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">
                    (Hingga {formatDate(user.subscriptionExpiry)})
                  </span>
                )}
              </dd>
            </div>
          )}
        </dl>
      </div>
    </div>
  );
}
