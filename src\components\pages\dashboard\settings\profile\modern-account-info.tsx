"use client";

import { useState, useEffect } from "react";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import {
  Mail,
  Shield,
  Calendar,
  Clock,
  Building2,
  Building,
  Crown,
  Star,
  TrendingUp,
  Users,
  CreditCard,
  MapPin,
  Globe,
  Phone,
  Briefcase,
  Award,
  Zap,
} from "lucide-react";
import { User } from "./types";
import {
  getSubscriptionInfo,
  SubscriptionInfo,
} from "@/actions/users/subscription-info";
import { getUsagePercentage, isApproachingLimit } from "@/lib/subscription";

interface ModernAccountInfoProps {
  user: User;
}

export default function ModernAccountInfo({ user }: ModernAccountInfoProps) {
  const [subscriptionInfo, setSubscriptionInfo] =
    useState<SubscriptionInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Helper function to format date
  const formatDate = (date: Date | null | undefined) => {
    if (!date) return "Tidak tersedia";
    return format(new Date(date), "d MMMM yyyy", { locale: id });
  };

  const formatDateTime = (date: Date | null | undefined) => {
    if (!date) return "Tidak tersedia";
    return format(new Date(date), "d MMMM yyyy, HH:mm", { locale: id });
  };

  // Fetch subscription data
  useEffect(() => {
    const fetchSubscriptionData = async () => {
      setLoading(true);
      setError(null);

      try {
        const result = await getSubscriptionInfo();

        if (result.success && result.data) {
          setSubscriptionInfo(result.data);
        } else {
          setError(result.error || "Gagal memuat informasi langganan");
        }
      } catch (err) {
        console.error("Error fetching subscription info:", err);
        setError("Terjadi kesalahan saat memuat data langganan");
        toast.error("Gagal memuat informasi langganan");
      } finally {
        setLoading(false);
      }
    };

    fetchSubscriptionData();
  }, [user.id]);

  // Calculate account age in days
  const getAccountAge = () => {
    if (!user.createdAt) return 0;
    const now = new Date();
    const created = new Date(user.createdAt);
    const diffTime = Math.abs(now.getTime() - created.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  const accountAge = getAccountAge();

  return (
    <div className="space-y-6">
      {/* Account Overview Card */}
      <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900">
        <CardHeader className="pb-4">
          <CardTitle className="text-2xl font-bold flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl">
              <Shield className="h-6 w-6 text-white" />
            </div>
            Informasi Akun
          </CardTitle>
          <p className="text-gray-600 dark:text-gray-400">
            Detail lengkap tentang akun dan status keanggotaan Anda
          </p>
        </CardHeader>
        <CardContent className="space-y-8">
          {/* Primary Account Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="flex flex-col space-y-4 h-full">
              <div className="flex items-center gap-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl flex-1">
                <div className="p-3 bg-blue-500 rounded-lg">
                  <Mail className="h-5 w-5 text-white" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Email
                  </p>
                  <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    {user.email || "Tidak tersedia"}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-4 p-4 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl flex-1">
                <div className="p-3 bg-purple-500 rounded-lg">
                  <Shield className="h-5 w-5 text-white" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Role
                  </p>
                  <div className="flex items-center gap-2">
                    <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white">
                      <Crown className="h-3 w-3 mr-1" />
                      {user.role}
                    </Badge>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex flex-col space-y-4 h-full">
              <div className="flex items-center gap-4 p-4 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl flex-1">
                <div className="p-3 bg-green-500 rounded-lg">
                  <Calendar className="h-5 w-5 text-white" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Bergabung
                  </p>
                  <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    {formatDate(user.createdAt)}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {accountAge} hari yang lalu
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-4 p-4 bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-xl flex-1">
                <div className="p-3 bg-orange-500 rounded-lg">
                  <Clock className="h-5 w-5 text-white" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Login Terakhir
                  </p>
                  <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    {user.lastLogin
                      ? formatDateTime(user.lastLogin)
                      : "Belum pernah login"}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <Separator className="bg-gradient-to-r from-transparent via-gray-200 dark:via-gray-700 to-transparent" />

          {/* Company Information */}
          <div className="space-y-4">
            <div className="flex items-center gap-3 mb-4">
              <Building className="h-5 w-5 text-indigo-600" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Informasi Perusahaan
              </h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex flex-col p-4 bg-gray-50 dark:bg-gray-800/50 rounded-xl h-full">
                <div className="flex items-center gap-3 mb-2">
                  <Building2 className="h-4 w-4 text-indigo-600" />
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    ID Perusahaan
                  </span>
                </div>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex-1">
                  {user.companyId || "Belum tersedia"}
                </p>
              </div>

              <div className="flex flex-col p-4 bg-gray-50 dark:bg-gray-800/50 rounded-xl h-full">
                <div className="flex items-center gap-3 mb-2">
                  <Building className="h-4 w-4 text-indigo-600" />
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Nama Perusahaan
                  </span>
                </div>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex-1">
                  {user.companyName || "Belum diatur"}
                </p>
              </div>

              <div className="flex flex-col p-4 bg-gray-50 dark:bg-gray-800/50 rounded-xl h-full">
                <div className="flex items-center gap-3 mb-2">
                  <Globe className="h-4 w-4 text-indigo-600" />
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Username Perusahaan
                  </span>
                </div>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex-1">
                  {user.companyUsername || "Belum diatur"}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Subscription & Plan Card */}
      <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-bold flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl">
              <CreditCard className="h-5 w-5 text-white" />
            </div>
            Paket & Langganan
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {loading ? (
            // Loading state
            <div className="space-y-6">
              <div className="flex items-center justify-between p-6 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl">
                <div className="flex items-center gap-4">
                  <Skeleton className="w-12 h-12 rounded-lg" />
                  <div className="space-y-2">
                    <Skeleton className="w-32 h-6" />
                    <Skeleton className="w-48 h-4" />
                  </div>
                </div>
                <Skeleton className="w-24 h-10 rounded-md" />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div
                    key={i}
                    className="text-center p-4 bg-gray-50 dark:bg-gray-800/50 rounded-xl"
                  >
                    <Skeleton className="w-8 h-8 mx-auto mb-2" />
                    <Skeleton className="w-12 h-8 mx-auto mb-2" />
                    <Skeleton className="w-16 h-4 mx-auto" />
                  </div>
                ))}
              </div>
            </div>
          ) : error ? (
            // Error state
            <div className="text-center py-8">
              <CreditCard className="h-16 w-16 text-red-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                Gagal Memuat Informasi Langganan
              </h3>
              <p className="text-gray-500 dark:text-gray-400">{error}</p>
            </div>
          ) : subscriptionInfo ? (
            // Real data
            <>
              <div className="flex items-center justify-between p-6 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-green-500 rounded-lg">
                    <Star className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">
                      {subscriptionInfo.planName}
                    </h3>
                    <div className="flex items-center gap-2">
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {subscriptionInfo.expiryDate
                          ? `Berlaku hingga ${formatDate(subscriptionInfo.expiryDate)}`
                          : "Paket gratis tanpa batas waktu"}
                      </p>
                      {subscriptionInfo.isTrialActive && (
                        <Badge variant="outline" className="text-xs">
                          Trial
                        </Badge>
                      )}
                      {!subscriptionInfo.isActive && (
                        <Badge variant="destructive" className="text-xs">
                          Tidak Aktif
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
                <Button className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white">
                  <TrendingUp className="h-4 w-4 mr-2" />
                  {subscriptionInfo.currentPlan === "BASIC"
                    ? "Upgrade"
                    : "Kelola"}
                </Button>
              </div>

              {/* Plan Usage */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Users */}
                <div className="text-center p-4 bg-gray-50 dark:bg-gray-800/50 rounded-xl">
                  <Users className="h-8 w-8 text-indigo-600 mx-auto mb-2" />
                  <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {subscriptionInfo.usage.currentUsers}
                    {subscriptionInfo.limits.maxUsers
                      ? `/${subscriptionInfo.limits.maxUsers}`
                      : ""}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Pengguna
                  </p>
                  {subscriptionInfo.limits.maxUsers && (
                    <Progress
                      value={getUsagePercentage(
                        subscriptionInfo.usage.currentUsers,
                        subscriptionInfo.limits.maxUsers
                      )}
                      className="h-1 mt-2"
                    />
                  )}
                </div>

                {/* Products */}
                <div className="text-center p-4 bg-gray-50 dark:bg-gray-800/50 rounded-xl">
                  <Briefcase className="h-8 w-8 text-indigo-600 mx-auto mb-2" />
                  <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {subscriptionInfo.usage.currentProducts}
                    {subscriptionInfo.limits.maxProducts
                      ? `/${subscriptionInfo.limits.maxProducts}`
                      : ""}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Produk
                  </p>
                  {subscriptionInfo.limits.maxProducts && (
                    <Progress
                      value={getUsagePercentage(
                        subscriptionInfo.usage.currentProducts,
                        subscriptionInfo.limits.maxProducts
                      )}
                      className="h-1 mt-2"
                    />
                  )}
                </div>

                {/* Transactions */}
                <div className="text-center p-4 bg-gray-50 dark:bg-gray-800/50 rounded-xl">
                  <Zap className="h-8 w-8 text-indigo-600 mx-auto mb-2" />
                  <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {subscriptionInfo.usage.currentTransactions}
                    {subscriptionInfo.limits.maxTransactionsPerMonth
                      ? `/${subscriptionInfo.limits.maxTransactionsPerMonth}`
                      : ""}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Transaksi Bulan Ini
                  </p>
                  {subscriptionInfo.limits.maxTransactionsPerMonth && (
                    <Progress
                      value={getUsagePercentage(
                        subscriptionInfo.usage.currentTransactions,
                        subscriptionInfo.limits.maxTransactionsPerMonth
                      )}
                      className="h-1 mt-2"
                    />
                  )}
                </div>
              </div>

              {/* Warnings for approaching limits */}
              {subscriptionInfo.limits.maxProducts &&
                isApproachingLimit(
                  subscriptionInfo.usage.currentProducts,
                  subscriptionInfo.limits.maxProducts
                ) && (
                  <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                    <div className="flex items-center gap-2">
                      <Award className="h-4 w-4 text-yellow-600" />
                      <p className="text-sm text-yellow-800 dark:text-yellow-200">
                        Anda mendekati batas produk. Pertimbangkan untuk upgrade
                        paket.
                      </p>
                    </div>
                  </div>
                )}
            </>
          ) : null}
        </CardContent>
      </Card>
    </div>
  );
}
