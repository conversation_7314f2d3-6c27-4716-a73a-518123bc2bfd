"use client";

import { useState } from "react";
import { updateProfile } from "@/actions/users/profile";
import { useSession } from "next-auth/react";
import { User } from "./types";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { DatePicker } from "@/components/ui/date-picker";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import {
  UserCircle,
  Mail,
  Phone,
  AtSign,
  AlertCircle,
  CheckCircle,
  Calendar,
  Save,
  X,
  FileText,
  Lock,
  Building,
  Sparkles,
  Edit3,
  Loader2
} from "lucide-react";

interface ModernProfileFormProps {
  user: User;
  name: string;
  setName: (name: string) => void;
  username: string;
  setUsername: (username: string) => void;
  imageUrl: string;
  phone: string;
  setPhone: (phone: string) => void;
  bio: string;
  setBio: (bio: string) => void;
  birthday: Date | undefined;
  setBirthday: (date: Date | undefined) => void;
  handleReset: () => void;
}

export default function ModernProfileForm({
  user,
  name,
  setName,
  username,
  setUsername,
  imageUrl,
  phone,
  setPhone,
  bio,
  setBio,
  birthday,
  setBirthday,
  handleReset,
}: ModernProfileFormProps) {
  const { update } = useSession();
  const [isLoading, setIsLoading] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  // Form submission
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    setSuccess(null);
    setError(null);

    try {
      const result = await updateProfile({
        name,
        username,
        image: imageUrl,
        phone,
        bio,
        birthday: birthday ? format(birthday, "yyyy-MM-dd") : "",
      });

      if (result.error) {
        setError(result.error);
      } else {
        setSuccess("Profil berhasil diperbarui!");
        setIsEditing(false);
        await update({
          user: {
            name,
            username,
            image: imageUrl,
            phone,
            bio,
            birthday: birthday ? format(birthday, "yyyy-MM-dd") : "",
          },
        });
      }
    } catch (err) {
      setError("Terjadi kesalahan saat memperbarui profil.");
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelEdit = () => {
    handleReset();
    setIsEditing(false);
    setError(null);
    setSuccess(null);
  };

  return (
    <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900">
      <CardHeader className="pb-6">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-2xl font-bold flex items-center gap-3 mb-2">
              <div className="p-2 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl">
                <UserCircle className="h-6 w-6 text-white" />
              </div>
              {isEditing ? "Edit Informasi Profil" : "Informasi Profil"}
            </CardTitle>
            <p className="text-gray-600 dark:text-gray-400">
              {isEditing
                ? "Perbarui informasi pribadi Anda"
                : "Kelola detail informasi pribadi Anda"}
            </p>
          </div>
          {!isEditing && (
            <Button
              onClick={() => setIsEditing(true)}
              className="bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white shadow-lg transition-all duration-200 hover:shadow-xl hover:scale-105"
            >
              <Edit3 className="h-4 w-4 mr-2" />
              Edit Profil
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Notifications */}
        {success && (
          <Alert className="border-l-4 border-l-green-500 bg-green-50 dark:bg-green-900/20 shadow-md">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800 dark:text-green-200 font-medium">
              {success}
            </AlertDescription>
          </Alert>
        )}

        {error && (
          <Alert className="border-l-4 border-l-red-500 bg-red-50 dark:bg-red-900/20 shadow-md">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800 dark:text-red-200 font-medium">
              {error}
            </AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Personal Information Section */}
          <div className="space-y-6">
            <div className="flex items-center gap-3 mb-4">
              <Sparkles className="h-5 w-5 text-indigo-600" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Informasi Pribadi
              </h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Name Field */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2">
                  <UserCircle className="h-4 w-4 text-indigo-600" />
                  Nama Lengkap
                </label>
                {isEditing ? (
                  <Input
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    placeholder="Masukkan nama lengkap"
                    className="bg-white/50 dark:bg-gray-800/50 border-gray-200 dark:border-gray-700 focus:border-indigo-500 focus:ring-indigo-500/20 transition-all duration-200 shadow-sm hover:shadow-md"
                  />
                ) : (
                  <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                    <span className="text-gray-900 dark:text-gray-100 font-medium">
                      {name || "-"}
                    </span>
                  </div>
                )}
              </div>

              {/* Username Field */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2">
                  <AtSign className="h-4 w-4 text-indigo-600" />
                  Username
                </label>
                {isEditing ? (
                  <Input
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    placeholder="username"
                    className="bg-white/50 dark:bg-gray-800/50 border-gray-200 dark:border-gray-700 focus:border-indigo-500 focus:ring-indigo-500/20 transition-all duration-200 shadow-sm hover:shadow-md"
                  />
                ) : (
                  <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                    <span className="text-gray-900 dark:text-gray-100 font-medium">
                      {username ? `@${username}` : "-"}
                    </span>
                  </div>
                )}
              </div>

              {/* Phone Field */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2">
                  <Phone className="h-4 w-4 text-indigo-600" />
                  Nomor Telepon
                </label>
                {isEditing ? (
                  <Input
                    type="tel"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                    placeholder="+62 812 3456 7890"
                    className="bg-white/50 dark:bg-gray-800/50 border-gray-200 dark:border-gray-700 focus:border-indigo-500 focus:ring-indigo-500/20 transition-all duration-200 shadow-sm hover:shadow-md"
                  />
                ) : (
                  <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                    <span className="text-gray-900 dark:text-gray-100 font-medium">
                      {phone || "-"}
                    </span>
                  </div>
                )}
              </div>

              {/* Birthday Field */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-indigo-600" />
                  Tanggal Lahir
                </label>
                {isEditing ? (
                  <DatePicker
                    date={birthday}
                    setDate={setBirthday}
                    placeholder="Pilih tanggal lahir"
                    className="bg-white/50 dark:bg-gray-800/50 border-gray-200 dark:border-gray-700 focus:border-indigo-500 focus:ring-indigo-500/20 transition-all duration-200 shadow-sm hover:shadow-md"
                    fromYear={1940}
                    toYear={new Date().getFullYear()}
                  />
                ) : (
                  <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                    <span className="text-gray-900 dark:text-gray-100 font-medium">
                      {birthday ? format(birthday, "d MMMM yyyy", { locale: id }) : "-"}
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Bio Field */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2">
                <FileText className="h-4 w-4 text-indigo-600" />
                Bio
              </label>
              {isEditing ? (
                <div className="space-y-2">
                  <Textarea
                    value={bio}
                    onChange={(e) => setBio(e.target.value)}
                    placeholder="Ceritakan sedikit tentang diri Anda..."
                    className="bg-white/50 dark:bg-gray-800/50 border-gray-200 dark:border-gray-700 focus:border-indigo-500 focus:ring-indigo-500/20 transition-all duration-200 shadow-sm hover:shadow-md min-h-[120px]"
                    maxLength={500}
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {bio.length}/500 karakter
                  </p>
                </div>
              ) : (
                <div className="p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                  <p className="text-gray-700 dark:text-gray-300">
                    {bio || "Belum ada bio"}
                  </p>
                </div>
              )}
            </div>
          </div>

          <Separator className="bg-gradient-to-r from-transparent via-gray-200 dark:via-gray-700 to-transparent" />

          {/* Read-only Information Section */}
          <div className="space-y-6">
            <div className="flex items-center gap-3 mb-4">
              <Lock className="h-5 w-5 text-gray-500" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Informasi Akun
              </h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Email Field - Read Only */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-500 dark:text-gray-400 flex items-center gap-2">
                  <Mail className="h-4 w-4" />
                  Email
                  <Lock className="h-3 w-3" />
                </label>
                <div className="flex items-center gap-3 p-3 bg-gray-100 dark:bg-gray-800 rounded-lg opacity-75">
                  <span className="text-gray-700 dark:text-gray-300">
                    {user.email || "-"}
                  </span>
                </div>
              </div>

              {/* Company ID Field - Read Only */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-500 dark:text-gray-400 flex items-center gap-2">
                  <Building className="h-4 w-4" />
                  ID Perusahaan
                  <Lock className="h-3 w-3" />
                </label>
                <div className="flex items-center gap-3 p-3 bg-gray-100 dark:bg-gray-800 rounded-lg opacity-75">
                  <span className="text-gray-700 dark:text-gray-300">
                    {user.companyId || "Belum tersedia"}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          {isEditing && (
            <div className="flex justify-end gap-4 pt-6 border-t border-gray-200 dark:border-gray-700">
              <Button
                type="button"
                onClick={handleCancelEdit}
                variant="outline"
                className="border-gray-300 text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800"
              >
                <X className="h-4 w-4 mr-2" />
                Batal
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
                className="bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white shadow-lg transition-all duration-200 hover:shadow-xl"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Menyimpan...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Simpan Perubahan
                  </>
                )}
              </Button>
            </div>
          )}
        </form>
      </CardContent>
    </Card>
  );
}