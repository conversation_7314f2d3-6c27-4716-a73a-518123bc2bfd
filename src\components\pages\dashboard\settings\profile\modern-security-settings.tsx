"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Shield,
  Key,
  Smartphone,
  Eye,
  EyeOff,
  Lock,
  Unlock,
  AlertTriangle,
  CheckCircle,
  Clock,
  MapPin,
  Monitor,
  Globe,
  Wifi,
  LogOut,
  RefreshCw,
  Bell,
  Mail,
  Zap
} from "lucide-react";
import { User } from "./types";

interface ModernSecuritySettingsProps {
  user: User;
}

export default function ModernSecuritySettings({ user }: ModernSecuritySettingsProps) {
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [loginAlerts, setLoginAlerts] = useState(true);
  const [sessionTimeout, setSessionTimeout] = useState(true);
  const [showSessions, setShowSessions] = useState(false);

  // Mock active sessions data
  const activeSessions = [
    {
      id: 1,
      device: "Chrome di Windows",
      location: "Jakarta, Indonesia",
      ip: "***********",
      lastActive: "Sekarang",
      current: true
    },
    {
      id: 2,
      device: "Safari di iPhone",
      location: "Jakarta, Indonesia", 
      ip: "***********",
      lastActive: "2 jam yang lalu",
      current: false
    },
    {
      id: 3,
      device: "Firefox di Ubuntu",
      location: "Bandung, Indonesia",
      ip: "***********", 
      lastActive: "1 hari yang lalu",
      current: false
    }
  ];

  const securityScore = 85;

  return (
    <div className="space-y-6">
      {/* Security Overview */}
      <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900">
        <CardHeader className="pb-4">
          <CardTitle className="text-2xl font-bold flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl">
              <Shield className="h-6 w-6 text-white" />
            </div>
            Keamanan Akun
          </CardTitle>
          <p className="text-gray-600 dark:text-gray-400">
            Kelola pengaturan keamanan dan privasi akun Anda
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Security Score */}
          <div className="flex items-center justify-between p-6 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-green-500 rounded-lg">
                <Shield className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">
                  Skor Keamanan: {securityScore}%
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Akun Anda cukup aman, namun masih bisa ditingkatkan
                </p>
              </div>
            </div>
            <Badge className="bg-gradient-to-r from-green-500 to-emerald-500 text-white px-4 py-2">
              <CheckCircle className="h-4 w-4 mr-2" />
              Aman
            </Badge>
          </div>

          {/* Security Recommendations */}
          <Alert className="border-l-4 border-l-amber-500 bg-amber-50 dark:bg-amber-900/20">
            <AlertTriangle className="h-4 w-4 text-amber-600" />
            <AlertTitle className="text-amber-800 dark:text-amber-200">
              Rekomendasi Keamanan
            </AlertTitle>
            <AlertDescription className="text-amber-700 dark:text-amber-300">
              Aktifkan autentikasi dua faktor untuk meningkatkan keamanan akun Anda.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* Password & Authentication */}
      <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-bold flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl">
              <Key className="h-5 w-5 text-white" />
            </div>
            Kata Sandi & Autentikasi
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Password Section */}
          <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800/50 rounded-xl">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-blue-500 rounded-lg">
                <Lock className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-gray-100">Kata Sandi</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Terakhir diubah 3 bulan yang lalu
                </p>
              </div>
            </div>
            <Button className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white">
              <RefreshCw className="h-4 w-4 mr-2" />
              Ubah Kata Sandi
            </Button>
          </div>

          {/* Two-Factor Authentication */}
          <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800/50 rounded-xl">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-purple-500 rounded-lg">
                <Smartphone className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-gray-100">Autentikasi Dua Faktor</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {twoFactorEnabled ? "Aktif - Akun Anda lebih aman" : "Nonaktif - Disarankan untuk diaktifkan"}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Switch
                checked={twoFactorEnabled}
                onCheckedChange={setTwoFactorEnabled}
              />
              {twoFactorEnabled && (
                <Badge className="bg-green-500 text-white">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Aktif
                </Badge>
              )}
            </div>
          </div>

          {/* Backup Codes */}
          {twoFactorEnabled && (
            <div className="flex items-center justify-between p-4 bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-xl">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-yellow-500 rounded-lg">
                  <Key className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-gray-100">Kode Cadangan</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Simpan kode cadangan untuk akses darurat
                  </p>
                </div>
              </div>
              <Button variant="outline" className="border-yellow-300 text-yellow-700 hover:bg-yellow-50">
                <Eye className="h-4 w-4 mr-2" />
                Lihat Kode
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Login & Session Management */}
      <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-bold flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl">
              <Monitor className="h-5 w-5 text-white" />
            </div>
            Sesi & Perangkat
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Session Settings */}
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800/50 rounded-xl">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-indigo-500 rounded-lg">
                  <Clock className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-gray-100">Timeout Otomatis</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Logout otomatis setelah tidak aktif 30 menit
                  </p>
                </div>
              </div>
              <Switch
                checked={sessionTimeout}
                onCheckedChange={setSessionTimeout}
              />
            </div>

            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800/50 rounded-xl">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-green-500 rounded-lg">
                  <Bell className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-gray-100">Notifikasi Login</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Dapatkan notifikasi saat ada login baru
                  </p>
                </div>
              </div>
              <Switch
                checked={loginAlerts}
                onCheckedChange={setLoginAlerts}
              />
            </div>
          </div>

          <Separator className="bg-gradient-to-r from-transparent via-gray-200 dark:via-gray-700 to-transparent" />

          {/* Active Sessions */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Sesi Aktif ({activeSessions.length})
              </h3>
              <Button
                variant="outline"
                onClick={() => setShowSessions(!showSessions)}
                className="border-gray-300 text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800"
              >
                {showSessions ? <EyeOff className="h-4 w-4 mr-2" /> : <Eye className="h-4 w-4 mr-2" />}
                {showSessions ? "Sembunyikan" : "Tampilkan"}
              </Button>
            </div>

            {showSessions && (
              <div className="space-y-3">
                {activeSessions.map((session) => (
                  <div key={session.id} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800/50 rounded-xl">
                    <div className="flex items-center gap-4">
                      <div className="p-3 bg-gray-500 rounded-lg">
                        <Monitor className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <h4 className="font-medium text-gray-900 dark:text-gray-100">
                            {session.device}
                          </h4>
                          {session.current && (
                            <Badge className="bg-green-500 text-white text-xs">
                              Sesi Ini
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                          <span className="flex items-center gap-1">
                            <MapPin className="h-3 w-3" />
                            {session.location}
                          </span>
                          <span className="flex items-center gap-1">
                            <Globe className="h-3 w-3" />
                            {session.ip}
                          </span>
                          <span className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {session.lastActive}
                          </span>
                        </div>
                      </div>
                    </div>
                    {!session.current && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-red-200 text-red-600 hover:bg-red-50 dark:border-red-800 dark:text-red-400 dark:hover:bg-red-900/20"
                      >
                        <LogOut className="h-4 w-4 mr-2" />
                        Logout
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>

          <Button 
            variant="outline" 
            className="w-full border-red-200 text-red-600 hover:bg-red-50 dark:border-red-800 dark:text-red-400 dark:hover:bg-red-900/20"
          >
            <LogOut className="h-4 w-4 mr-2" />
            Logout Semua Perangkat Lain
          </Button>
        </CardContent>
      </Card>

      {/* Privacy Settings */}
      <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-bold flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl">
              <Eye className="h-5 w-5 text-white" />
            </div>
            Privasi & Notifikasi
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800/50 rounded-xl">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-blue-500 rounded-lg">
                <Mail className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-gray-100">Notifikasi Email</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Terima notifikasi keamanan via email
                </p>
              </div>
            </div>
            <Switch
              checked={emailNotifications}
              onCheckedChange={setEmailNotifications}
            />
          </div>

          <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800/50 rounded-xl">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-orange-500 rounded-lg">
                <Zap className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-gray-100">Aktivitas Mencurigakan</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Peringatan untuk aktivitas tidak biasa
                </p>
              </div>
            </div>
            <Switch defaultChecked />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}