"use client";

import { useState, useRef } from "react";
import Image from "next/image";
import { UserCircle, Upload } from "lucide-react";

interface ProfileImageProps {
  imageUrl: string;
  setImageUrl: (url: string) => void;
  name: string;
  username: string | null;
}

export default function ProfileImage({
  imageUrl,
  setImageUrl,
  name,
  username,
}: ProfileImageProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragging, setIsDragging] = useState(false);

  // Handle file upload
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      if (file.type.startsWith("image/")) {
        const reader = new FileReader();
        reader.onload = (event) => {
          if (event.target?.result) {
            setImageUrl(event.target.result.toString());
          }
        };
        reader.readAsDataURL(file);
      }
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          setImageUrl(event.target.result.toString());
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden shadow-sm">
      <div className="p-6 flex flex-col items-center">
        <div
          className={`relative w-32 h-32 rounded-full overflow-hidden mb-4 border-4 ${
            imageUrl
              ? "border-indigo-100 dark:border-indigo-900/30"
              : "border-gray-100 dark:border-gray-700"
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          {imageUrl ? (
            <Image
              src={imageUrl}
              alt="Profile"
              fill
              className="object-cover"
              onError={() => setImageUrl("/placeholder-user.png")}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gray-100 dark:bg-gray-700">
              <UserCircle className="h-16 w-16 text-gray-400 dark:text-gray-500" />
            </div>
          )}
        </div>

        <div className="flex flex-col items-center">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            {name || "Nama Pengguna"}
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {username ? `@${username}` : "Username"}
          </p>
        </div>

        <div className="mt-4 w-full">
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            accept="image/*"
            className="hidden"
          />
          <button
            type="button"
            onClick={triggerFileInput}
            className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 cursor-pointer transition-colors duration-200"
          >
            <Upload className="h-4 w-4 mr-2" />
            Ganti Foto
          </button>
        </div>
      </div>
    </div>
  );
}
