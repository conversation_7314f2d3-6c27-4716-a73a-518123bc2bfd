"use client";

import { useState, useEffect } from "react";
import { User } from "./types";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import {
  Clock,
  Activity,
  ChevronRight,
  LogIn,
  Monitor,
  MapPin,
  Loader2,
  UserCog,
  KeyIcon,
  LogOut,
  Save,
  X,
  Edit,
  Briefcase,
  Users,
  Building,
  CreditCard,
  Gift,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { AddressManager } from "@/components/ui/address-manager";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import {
  getUserActivities,
  UserActivity as UserActivityType,
} from "@/actions/users/activity";
import {
  updateBusinessInfo,
  getBusinessInfo,
} from "@/actions/users/additional-info";

// Options for the form fields
const positionOptions = [
  { value: "owner", label: "Owner" },
  { value: "manager", label: "Manager" },
  { value: "staff", label: "Staff" },
  { value: "admin", label: "Admin" },
];

const employeeCountOptions = [
  { value: "1-5", label: "1-5 orang" },
  { value: "6-20", label: "6-20 orang" },
  { value: "21-50", label: "21-50 orang" },
  { value: "50+", label: "50+ orang" },
];

const occupationOptions = [
  { value: "entrepreneur", label: "Pengusaha" },
  { value: "business-owner", label: "Pemilik Bisnis" },
  { value: "manager", label: "Manajer" },
  { value: "employee", label: "Karyawan" },
  { value: "freelancer", label: "Freelancer" },
  { value: "student", label: "Mahasiswa" },
  { value: "other", label: "Lainnya" },
];

const industryOptions = [
  { value: "retail", label: "Retail" },
  { value: "food-beverage", label: "Makanan & Minuman" },
  { value: "fashion", label: "Fashion" },
  { value: "electronics", label: "Elektronik" },
  { value: "automotive", label: "Otomotif" },
  { value: "health-beauty", label: "Kesehatan & Kecantikan" },
  { value: "services", label: "Jasa" },
  { value: "other", label: "Lainnya" },
];

const subscriptionPackageOptions = [
  { value: "basic", label: "Basic - Gratis" },
  { value: "pro", label: "Pro - Rp 99.000/bulan" },
  { value: "business", label: "Business - Rp 199.000/bulan" },
  { value: "enterprise", label: "Enterprise - Rp 399.000/bulan" },
];

interface EnhancedProfileInfoProps {
  user: User;
}

export default function EnhancedProfileInfo({
  user,
}: EnhancedProfileInfoProps) {
  const router = useRouter();

  // Format date for display
  const formatDate = (date: Date | null | undefined) => {
    if (!date) return "-";
    return format(new Date(date), "d MMMM yyyy, HH:mm", { locale: id });
  };

  // State for additional info form
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [position, setPosition] = useState("");
  const [employeeCount, setEmployeeCount] = useState("");
  const [occupation, setOccupation] = useState("");
  const [industry, setIndustry] = useState("");
  const [subscriptionPackage, setSubscriptionPackage] = useState("");
  const [referralCode, setReferralCode] = useState("");
  const [companyName, setCompanyName] = useState("");
  const [companyUsername, setCompanyUsername] = useState("");
  const [companyAddress, setCompanyAddress] = useState("");
  const [companyLogo, setCompanyLogo] = useState("");
  const [postalCode, setPostalCode] = useState("");
  const [city, setCity] = useState("");
  const [billingAddress, setBillingAddress] = useState(""); // Legacy field
  const [shippingAddress, setShippingAddress] = useState(""); // Legacy field
  const [billingAddresses, setBillingAddresses] = useState<string[]>([]); // New array field
  const [shippingAddresses, setShippingAddresses] = useState<string[]>([]); // New array field
  const [faxNumber, setFaxNumber] = useState("");
  const [website, setWebsite] = useState("");
  const [email, setEmail] = useState("");
  const [companyPhone, setCompanyPhone] = useState("");
  // Operating hours and notification settings
  const [operatingHoursStart, setOperatingHoursStart] = useState("");
  const [operatingHoursEnd, setOperatingHoursEnd] = useState("");
  const [notificationTime, setNotificationTime] = useState("");

  // State for user activities
  const [activities, setActivities] = useState<UserActivityType[]>([]);
  const [isLoadingActivities, setIsLoadingActivities] = useState(true);
  const [activityError, setActivityError] = useState<string | null>(null);

  // Load additional user info
  useEffect(() => {
    const loadAdditionalInfo = async () => {
      setIsLoading(true); // Set loading to true
      try {
        const result = await getBusinessInfo();
        if (result.success && result.data) {
          setPosition(result.data.position || "");
          setEmployeeCount(result.data.employeeCount || "");
          setOccupation(result.data.occupation || "");
          setIndustry(result.data.industry || "");
          setSubscriptionPackage(result.data.subscriptionPackage || "");
          setReferralCode(result.data.referralCode || "");
          setCompanyName(result.data.companyName || "");
          setCompanyUsername(result.data.companyUsername || "");
          setCompanyAddress(result.data.companyAddress || "");
          setCompanyLogo(result.data.companyLogo || "");
          setPostalCode(result.data.postalCode || "");
          setCity(result.data.city || "");
          setBillingAddress(result.data.billingAddress || ""); // Legacy field
          setShippingAddress(result.data.shippingAddress || ""); // Legacy field

          // Handle migration from legacy single address to multiple addresses
          let billingAddrs = result.data.billingAddresses || [];
          let shippingAddrs = result.data.shippingAddresses || [];

          // If new arrays are empty but legacy fields have data, migrate them
          if (billingAddrs.length === 0 && result.data.billingAddress) {
            billingAddrs = [result.data.billingAddress];
          }
          if (shippingAddrs.length === 0 && result.data.shippingAddress) {
            shippingAddrs = [result.data.shippingAddress];
          }

          setBillingAddresses(billingAddrs);
          setShippingAddresses(shippingAddrs);
          setFaxNumber(result.data.faxNumber || "");
          setWebsite(result.data.website || "");
          setEmail(result.data.companyEmail || "");
          setCompanyPhone(result.data.companyPhone || "");
          // Operating hours and notification settings
          setOperatingHoursStart(result.data.operatingHoursStart || "08:00");
          setOperatingHoursEnd(result.data.operatingHoursEnd || "17:00");
          setNotificationTime(result.data.notificationTime || "17:00");
        }
      } catch (error) {
        console.error("Error loading additional info:", error);
      } finally {
        setIsLoading(false); // Set loading to false regardless of success or failure
      }
    };

    loadAdditionalInfo();
  }, []);

  // Fetch user activities
  useEffect(() => {
    const fetchActivities = async () => {
      try {
        setIsLoadingActivities(true);
        const result = await getUserActivities(5);

        if (result.success && result.data) {
          setActivities(result.data);
          setActivityError(null);
        } else {
          setActivityError(result.error || "Gagal mengambil aktivitas");
          setActivities([]);
        }
      } catch (error) {
        console.error("Error fetching activities:", error);
        setActivityError("Terjadi kesalahan saat mengambil aktivitas");
        setActivities([]);
      } finally {
        setIsLoadingActivities(false);
      }
    };

    fetchActivities();
  }, []);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const result = await updateBusinessInfo({
        position: position || undefined,
        employeeCount: employeeCount || undefined,
        occupation: occupation || undefined,
        industry: industry || undefined,
        subscriptionPackage: subscriptionPackage || undefined,
        referralCode: referralCode || undefined,
        companyName: companyName || undefined,
        companyUsername: companyUsername || undefined,
        companyAddress: companyAddress || undefined,
        companyLogo: companyLogo || undefined,
        postalCode: postalCode || undefined,
        city: city || undefined,
        billingAddress: billingAddress || undefined, // Legacy field
        shippingAddress: shippingAddress || undefined, // Legacy field
        billingAddresses: billingAddresses.filter((addr) => addr.trim() !== ""), // New array field
        shippingAddresses: shippingAddresses.filter(
          (addr) => addr.trim() !== ""
        ), // New array field
        faxNumber: faxNumber || undefined,
        website: website || undefined,
        companyEmail: email || undefined,
        companyPhone: companyPhone || undefined,
        // Operating hours and notification settings
        operatingHoursStart: operatingHoursStart || undefined,
        operatingHoursEnd: operatingHoursEnd || undefined,
        notificationTime: notificationTime || undefined,
      });

      if (result.success) {
        toast.success(result.success);
        setIsEditing(false);
      } else {
        toast.error(result.error || "Gagal menyimpan perubahan");
      }
    } catch (error) {
      console.error("Error updating additional info:", error);
      toast.error("Terjadi kesalahan saat menyimpan");
    } finally {
      setIsLoading(false);
    }
  };

  // Handle cancel edit
  const handleCancelEdit = async () => {
    // Reset form to original values
    try {
      const result = await getBusinessInfo();
      if (result.success && result.data) {
        setPosition(result.data.position || "");
        setEmployeeCount(result.data.employeeCount || "");
        setOccupation(result.data.occupation || "");
        setIndustry(result.data.industry || "");
        setSubscriptionPackage(result.data.subscriptionPackage || "");
        setReferralCode(result.data.referralCode || "");
        setCompanyName(result.data.companyName || "");
        setCompanyUsername(result.data.companyUsername || "");
        setCompanyAddress(result.data.companyAddress || "");
        setCompanyLogo(result.data.companyLogo || "");
        setPostalCode(result.data.postalCode || "");
        setCity(result.data.city || "");
        setBillingAddress(result.data.billingAddress || "");
        setShippingAddress(result.data.shippingAddress || "");
        setFaxNumber(result.data.faxNumber || "");
        setWebsite(result.data.website || "");
        setEmail(result.data.companyEmail || "");
        setCompanyPhone(result.data.companyPhone || "");
        // Operating hours and notification settings
        setOperatingHoursStart(result.data.operatingHoursStart || "08:00");
        setOperatingHoursEnd(result.data.operatingHoursEnd || "17:00");
        setNotificationTime(result.data.notificationTime || "17:00");
      }
    } catch (error) {
      console.error("Error resetting form:", error);
    }
    setIsEditing(false);
  };

  // Get icon based on activity type
  const getActivityIcon = (type: string) => {
    switch (type) {
      case "login":
        return <LogIn className="h-4 w-4 text-green-500" />;
      case "profile_update":
        return <UserCog className="h-4 w-4 text-blue-500" />;
      case "password_change":
        return <KeyIcon className="h-4 w-4 text-yellow-500" />;
      case "session_revoked":
        return <LogOut className="h-4 w-4 text-red-500" />;
      default:
        return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Additional Information Form */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex justify-between items-center">
            <CardTitle className="text-base font-medium flex items-center gap-2">
              <UserCog className="h-4 w-4 text-indigo-500" />
              {isEditing ? "Edit Informasi Tambahan" : "Informasi Tambahan"}
            </CardTitle>
            {!isEditing && (
              <Button
                onClick={() => setIsEditing(true)}
                variant="outline"
                size="sm"
                className="cursor-pointer"
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {isLoading && !isEditing ? (
            <div className="flex flex-col items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
              <p className="text-sm text-muted-foreground">
                Memuat informasi...
              </p>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 gap-y-6 gap-x-6 sm:grid-cols-2">
                {/* Company Name Field */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                    Nama Perusahaan
                  </label>
                  {isEditing ? (
                    <Input
                      type="text"
                      value={companyName}
                      onChange={(e) => setCompanyName(e.target.value)}
                      placeholder="Masukkan nama perusahaan"
                      className="w-full"
                    />
                  ) : (
                    <div className="flex items-center space-x-3 py-2">
                      <div className="flex-shrink-0">
                        <Building className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
                      </div>
                      <div className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                        {companyName || "-"}
                      </div>
                    </div>
                  )}
                </div>

                {/* Company Username Field */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                    Username Perusahaan
                  </label>
                  {isEditing ? (
                    <Input
                      type="text"
                      value={companyUsername}
                      onChange={(e) => setCompanyUsername(e.target.value)}
                      placeholder="Masukkan username perusahaan"
                      className="w-full"
                    />
                  ) : (
                    <div className="flex items-center space-x-3 py-2">
                      <div className="flex-shrink-0">
                        <Building className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
                      </div>
                      <div className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                        {companyUsername || "-"}
                      </div>
                    </div>
                  )}
                </div>

                {/* Position Field */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                    Jabatan
                  </label>
                  {isEditing ? (
                    <Select value={position} onValueChange={setPosition}>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Pilih jabatan" />
                      </SelectTrigger>
                      <SelectContent>
                        {positionOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  ) : (
                    <div className="flex items-center space-x-3 py-2">
                      <div className="flex-shrink-0">
                        <Briefcase className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
                      </div>
                      <div className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                        {position
                          ? positionOptions.find(
                              (opt) => opt.value === position
                            )?.label
                          : "-"}
                      </div>
                    </div>
                  )}
                </div>

                {/* Employee Count Field */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                    Jumlah Karyawan
                  </label>
                  {isEditing ? (
                    <Select
                      value={employeeCount}
                      onValueChange={setEmployeeCount}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Pilih jumlah karyawan" />
                      </SelectTrigger>
                      <SelectContent>
                        {employeeCountOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  ) : (
                    <div className="flex items-center space-x-3 py-2">
                      <div className="flex-shrink-0">
                        <Users className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
                      </div>
                      <div className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                        {employeeCount
                          ? employeeCountOptions.find(
                              (opt) => opt.value === employeeCount
                            )?.label
                          : "-"}
                      </div>
                    </div>
                  )}
                </div>

                {/* Occupation Field */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                    Pekerjaan
                  </label>
                  {isEditing ? (
                    <Select value={occupation} onValueChange={setOccupation}>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Pilih pekerjaan" />
                      </SelectTrigger>
                      <SelectContent>
                        {occupationOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  ) : (
                    <div className="flex items-center space-x-3 py-2">
                      <div className="flex-shrink-0">
                        <Briefcase className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
                      </div>
                      <div className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                        {occupation
                          ? occupationOptions.find(
                              (opt) => opt.value === occupation
                            )?.label
                          : "-"}
                      </div>
                    </div>
                  )}
                </div>

                {/* Industry Field */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                    Industri
                  </label>
                  {isEditing ? (
                    <Select value={industry} onValueChange={setIndustry}>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Pilih industri" />
                      </SelectTrigger>
                      <SelectContent>
                        {industryOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  ) : (
                    <div className="flex items-center space-x-3 py-2">
                      <div className="flex-shrink-0">
                        <Building className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
                      </div>
                      <div className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                        {industry
                          ? industryOptions.find(
                              (opt) => opt.value === industry
                            )?.label
                          : "-"}
                      </div>
                    </div>
                  )}
                </div>

                {/* Subscription Package Field */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                    Paket Langganan
                  </label>
                  {isEditing ? (
                    <Select
                      value={subscriptionPackage}
                      onValueChange={setSubscriptionPackage}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Pilih paket langganan" />
                      </SelectTrigger>
                      <SelectContent>
                        {subscriptionPackageOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  ) : (
                    <div className="flex items-center space-x-3 py-2">
                      <div className="flex-shrink-0">
                        <CreditCard className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
                      </div>
                      <div className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                        {subscriptionPackage
                          ? subscriptionPackageOptions.find(
                              (opt) => opt.value === subscriptionPackage
                            )?.label
                          : "-"}
                      </div>
                    </div>
                  )}
                </div>

                {/* Referral Code Field */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                    Kode Referral
                  </label>
                  {isEditing ? (
                    <Input
                      type="text"
                      value="Coming Soon"
                      disabled
                      placeholder="Coming Soon"
                      className="w-full"
                    />
                  ) : (
                    <div className="flex items-center space-x-3 py-2">
                      <div className="flex-shrink-0">
                        <Gift className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
                      </div>
                      <div className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                        <Badge
                          variant="outline"
                          className="bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800/30"
                        >
                          Coming Soon
                        </Badge>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Company Information Section */}
              <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                  Informasi Perusahaan
                </h3>
                <div className="grid grid-cols-1 gap-y-6 gap-x-6 sm:grid-cols-2">
                  {/* Company Address Field */}
                  <div className="col-span-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                      Alamat Perusahaan
                    </label>
                    {isEditing ? (
                      <Input
                        type="text"
                        value={companyAddress}
                        onChange={(e) => setCompanyAddress(e.target.value)}
                        placeholder="Masukkan alamat perusahaan"
                        className="w-full"
                      />
                    ) : (
                      <div className="flex items-center space-x-3 py-2">
                        <div className="flex-shrink-0">
                          <MapPin className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
                        </div>
                        <div className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                          {companyAddress || "-"}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* City Field */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                      Kota
                    </label>
                    {isEditing ? (
                      <Input
                        type="text"
                        value={city}
                        onChange={(e) => setCity(e.target.value)}
                        placeholder="Masukkan kota"
                        className="w-full"
                      />
                    ) : (
                      <div className="flex items-center space-x-3 py-2">
                        <div className="flex-shrink-0">
                          <MapPin className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
                        </div>
                        <div className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                          {city || "-"}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Postal Code Field */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                      Kode Pos
                    </label>
                    {isEditing ? (
                      <Input
                        type="text"
                        value={postalCode}
                        onChange={(e) => setPostalCode(e.target.value)}
                        placeholder="Masukkan kode pos"
                        className="w-full"
                      />
                    ) : (
                      <div className="flex items-center space-x-3 py-2">
                        <div className="flex-shrink-0">
                          <MapPin className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
                        </div>
                        <div className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                          {postalCode || "-"}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Company Logo Field */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                      Logo Perusahaan (URL)
                    </label>
                    {isEditing ? (
                      <Input
                        type="url"
                        value={companyLogo}
                        onChange={(e) => setCompanyLogo(e.target.value)}
                        placeholder="https://example.com/logo.png"
                        className="w-full"
                      />
                    ) : (
                      <div className="flex items-center space-x-3 py-2">
                        <div className="flex-shrink-0">
                          <Building className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
                        </div>
                        <div className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                          {companyLogo || "-"}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Billing Addresses Field */}
                  <div className="col-span-2">
                    <AddressManager
                      label="Alamat Penagihan"
                      addresses={billingAddresses}
                      onChange={setBillingAddresses}
                      placeholder="Masukkan alamat penagihan"
                      maxAddresses={5}
                      isEditing={isEditing}
                    />
                  </div>

                  {/* Shipping Addresses Field */}
                  <div className="col-span-2">
                    <AddressManager
                      label="Alamat Pengiriman"
                      addresses={shippingAddresses}
                      onChange={setShippingAddresses}
                      placeholder="Masukkan alamat pengiriman"
                      maxAddresses={5}
                      isEditing={isEditing}
                    />
                  </div>

                  {/* Fax Number Field */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                      Nomor Fax
                    </label>
                    {isEditing ? (
                      <Input
                        type="text"
                        value={faxNumber}
                        onChange={(e) => setFaxNumber(e.target.value)}
                        placeholder="Masukkan nomor fax"
                        className="w-full"
                      />
                    ) : (
                      <div className="flex items-center space-x-3 py-2">
                        <div className="flex-shrink-0">
                          <Building className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
                        </div>
                        <div className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                          {faxNumber || "-"}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Website Field */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                      Website
                    </label>
                    {isEditing ? (
                      <Input
                        type="url"
                        value={website}
                        onChange={(e) => setWebsite(e.target.value)}
                        placeholder="https://example.com"
                        className="w-full"
                      />
                    ) : (
                      <div className="flex items-center space-x-3 py-2">
                        <div className="flex-shrink-0">
                          <Building className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
                        </div>
                        <div className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                          {website || "-"}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Email Field */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                      Email Perusahaan
                    </label>
                    {isEditing ? (
                      <Input
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder="<EMAIL>"
                        className="w-full"
                      />
                    ) : (
                      <div className="flex items-center space-x-3 py-2">
                        <div className="flex-shrink-0">
                          <Building className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
                        </div>
                        <div className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                          {email || "-"}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Company Phone Field */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                      Nomor Telepon Perusahaan
                    </label>
                    {isEditing ? (
                      <Input
                        type="tel"
                        value={companyPhone}
                        onChange={(e) => setCompanyPhone(e.target.value)}
                        placeholder="08123456789"
                        className="w-full"
                      />
                    ) : (
                      <div className="flex items-center space-x-3 py-2">
                        <div className="flex-shrink-0">
                          <Building className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
                        </div>
                        <div className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                          {companyPhone || "-"}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Operating Hours Start Field */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                      Jam Buka Operasional
                    </label>
                    {isEditing ? (
                      <Select
                        value={operatingHoursStart}
                        onValueChange={setOperatingHoursStart}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Pilih jam buka" />
                        </SelectTrigger>
                        <SelectContent>
                          {Array.from({ length: 24 }, (_, i) => {
                            const hour = String(i).padStart(2, "0");
                            return (
                              <SelectItem key={hour} value={`${hour}:00`}>
                                {hour}:00
                              </SelectItem>
                            );
                          })}
                        </SelectContent>
                      </Select>
                    ) : (
                      <div className="flex items-center space-x-3 py-2">
                        <div className="flex-shrink-0">
                          <Clock className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
                        </div>
                        <div className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                          {operatingHoursStart || "08:00"}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Operating Hours End Field */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                      Jam Tutup Operasional
                    </label>
                    {isEditing ? (
                      <Select
                        value={operatingHoursEnd}
                        onValueChange={setOperatingHoursEnd}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Pilih jam tutup" />
                        </SelectTrigger>
                        <SelectContent>
                          {Array.from({ length: 24 }, (_, i) => {
                            const hour = String(i).padStart(2, "0");
                            return (
                              <SelectItem key={hour} value={`${hour}:00`}>
                                {hour}:00
                              </SelectItem>
                            );
                          })}
                        </SelectContent>
                      </Select>
                    ) : (
                      <div className="flex items-center space-x-3 py-2">
                        <div className="flex-shrink-0">
                          <Clock className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
                        </div>
                        <div className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                          {operatingHoursEnd || "17:00"}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Notification Time Field */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                      Waktu Pengiriman Notifikasi
                    </label>
                    {isEditing ? (
                      <Select
                        value={notificationTime}
                        onValueChange={setNotificationTime}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Pilih waktu notifikasi" />
                        </SelectTrigger>
                        <SelectContent>
                          {Array.from({ length: 24 }, (_, i) => {
                            const hour = String(i).padStart(2, "0");
                            return (
                              <SelectItem key={hour} value={`${hour}:00`}>
                                {hour}:00
                              </SelectItem>
                            );
                          })}
                        </SelectContent>
                      </Select>
                    ) : (
                      <div className="flex items-center space-x-3 py-2">
                        <div className="flex-shrink-0">
                          <Clock className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
                        </div>
                        <div className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                          {notificationTime || "17:00"}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {isEditing && (
                <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <Button
                    type="button"
                    onClick={handleCancelEdit}
                    variant="outline"
                    className="cursor-pointer"
                  >
                    <X className="h-4 w-4 mr-2" />
                    Batal
                  </Button>
                  <Button
                    type="submit"
                    disabled={isLoading}
                    className="cursor-pointer"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Menyimpan...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Simpan Perubahan
                      </>
                    )}
                  </Button>
                </div>
              )}
            </form>
          )}
        </CardContent>
      </Card>

      {/* Last Login Information */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base font-medium flex items-center gap-2">
            <Clock className="h-4 w-4 text-indigo-500" />
            Informasi Login Terakhir
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="bg-indigo-50 dark:bg-indigo-900/20 p-2 rounded-full">
                  <Monitor className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {user.lastLogin ? "Login Terakhir" : "Belum ada data login"}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {user.lastLogin
                      ? formatDate(user.lastLogin)
                      : "Login pertama Anda akan tercatat di sini"}
                  </p>
                </div>
              </div>
              <Badge
                variant="outline"
                className="bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-300 dark:border-green-800/30"
              >
                Aktif
              </Badge>
            </div>

            {user.lastLogin && (
              <div className="flex items-center gap-3 mt-3">
                <div className="bg-gray-50 dark:bg-gray-800 p-2 rounded-full">
                  <MapPin className="h-4 w-4 text-gray-500" />
                </div>
                <div>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Jakarta, Indonesia • Chrome di Windows
                  </p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Activity Summary */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex justify-between items-center">
            <CardTitle className="text-base font-medium flex items-center gap-2">
              <Activity className="h-4 w-4 text-indigo-500" />
              Aktivitas Terbaru
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              className="text-xs cursor-pointer"
              onClick={() => router.push("/dashboard/activity")}
            >
              Lihat Semua
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {isLoadingActivities ? (
              <div className="flex flex-col items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
                <p className="text-sm text-muted-foreground">
                  Memuat aktivitas...
                </p>
              </div>
            ) : activityError ? (
              <div className="text-center py-6">
                <p className="text-sm text-red-500 dark:text-red-400">
                  {activityError}
                </p>
              </div>
            ) : activities.length === 0 ? (
              <div className="text-center py-6">
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Belum ada aktivitas
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {activities.map((activity, index) => (
                  <div key={activity.id}>
                    <div className="flex items-start gap-3">
                      <div className="bg-gray-100 dark:bg-gray-800 p-2 rounded-full mt-0.5">
                        {getActivityIcon(activity.type)}
                      </div>
                      <div className="flex-1">
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {activity.description}
                            </p>
                            {activity.device && (
                              <p className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
                                {activity.device}
                              </p>
                            )}
                          </div>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            {activity.timestamp}
                          </p>
                        </div>
                      </div>
                    </div>
                    {index < activities.length - 1 && (
                      <Separator className="my-4" />
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
