"use client";

import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Re<PERSON>ons<PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
} from "recharts";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

// Simplified chart color palette
export const CHART_COLORS = [
  "#6366f1", // Indigo
  "#10b981", // Emerald
  "#f59e0b", // Amber
  "#ef4444", // Red
  "#8b5cf6", // Purple
];

// Helper function to format currency
export function formatCurrency(value: number) {
  return new Intl.NumberFormat("id-ID", {
    style: "currency",
    currency: "IDR",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value);
}

export interface ChartDataPoint {
  name: string;
  [key: string]: string | number;
}

interface BarChartComponentProps {
  data: ChartDataPoint[];
  title?: string;
  description?: string;
  dataKeys: string[];
  height?: number;
  valueFormatter?: (value: number) => string;
  className?: string;
}

export function BarChartComponent({
  data,
  title,
  description,
  dataKeys,
  height = 300,
  valueFormatter = formatCurrency,
  className = "",
}: BarChartComponentProps) {
  return (
    <Card
      className={`relative overflow-hidden border-0 bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 shadow-lg hover:shadow-xl transition-all duration-300 ${className}`}
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute -right-8 -top-8 h-32 w-32 rounded-full bg-gradient-to-br from-blue-400 to-purple-500"></div>
        <div className="absolute -bottom-8 -left-8 h-24 w-24 rounded-full bg-gradient-to-br from-emerald-400 to-blue-500"></div>
      </div>

      {(title || description) && (
        <CardHeader className="relative pb-4">
          <div className="flex items-center justify-between">
            <div>
              {title && (
                <CardTitle className="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
                  {title}
                </CardTitle>
              )}
              {description && (
                <CardDescription className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {description}
                </CardDescription>
              )}
            </div>
            {/* Chart Icon */}
            <div className="flex-shrink-0 p-2 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 shadow-lg">
              <svg
                className="h-5 w-5 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                />
              </svg>
            </div>
          </div>
        </CardHeader>
      )}

      <CardContent className="relative">
        <div
          style={{ height: `${height}px` }}
          className="relative rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm p-4"
        >
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={data}
              margin={{
                top: 20,
                right: 30,
                left: 20,
                bottom: 20,
              }}
            >
              <defs>
                {dataKeys.map((key, index) => (
                  <linearGradient
                    key={key}
                    id={`gradient-${index}`}
                    x1="0"
                    y1="0"
                    x2="0"
                    y2="1"
                  >
                    <stop
                      offset="0%"
                      stopColor={CHART_COLORS[index % CHART_COLORS.length]}
                      stopOpacity={0.8}
                    />
                    <stop
                      offset="100%"
                      stopColor={CHART_COLORS[index % CHART_COLORS.length]}
                      stopOpacity={0.3}
                    />
                  </linearGradient>
                ))}
              </defs>

              <CartesianGrid
                strokeDasharray="3 3"
                opacity={0.3}
                stroke="#e0e7ff"
                className="dark:stroke-gray-600"
              />

              <XAxis
                dataKey="name"
                tick={{
                  fontSize: 12,
                  fill: "#6b7280",
                  fontWeight: 500,
                }}
                axisLine={false}
                tickLine={false}
                tickMargin={10}
              />

              <YAxis
                tickFormatter={(value) =>
                  typeof value === "number" ? valueFormatter(value) : value
                }
                tick={{
                  fontSize: 11,
                  fill: "#6b7280",
                  fontWeight: 500,
                }}
                axisLine={false}
                tickLine={false}
                width={90}
                tickMargin={10}
              />

              <RechartsTooltip
                formatter={(value: number, name: string) => [
                  typeof value === "number" ? valueFormatter(value) : value,
                  name,
                ]}
                contentStyle={{
                  backgroundColor: "rgba(255, 255, 255, 0.98)",
                  border: "1px solid rgba(226, 232, 240, 0.8)",
                  borderRadius: "12px",
                  boxShadow:
                    "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
                  fontSize: "13px",
                  fontWeight: "500",
                  backdropFilter: "blur(8px)",
                }}
                labelStyle={{
                  color: "#374151",
                  fontWeight: "600",
                  marginBottom: "4px",
                }}
                cursor={{
                  fill: "rgba(99, 102, 241, 0.1)",
                  radius: 4,
                }}
              />

              {dataKeys.map((key, index) => (
                <Bar
                  key={key}
                  dataKey={key}
                  fill={`url(#gradient-${index})`}
                  radius={[6, 6, 0, 0]}
                  stroke={CHART_COLORS[index % CHART_COLORS.length]}
                  strokeWidth={1}
                  className="hover:opacity-80 transition-opacity duration-200"
                />
              ))}
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
