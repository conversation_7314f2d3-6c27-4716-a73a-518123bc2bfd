"use client";

import React from "react";
import { RefreshCw, Download } from "lucide-react";
import { Button } from "@/components/ui/button";
import { WarehouseColumnVisibility } from "@/types/warehouse";
import { ColumnActions } from "@/components/pages/dashboard/warehouses/components/ColumnActions";

interface WarehouseActionsProps {
  columnVisibility: WarehouseColumnVisibility;
  onColumnVisibilityChange: (visibility: Record<string, boolean>) => void;
  onRefresh: () => void;
  onExport: () => void;
  loading?: boolean;
}

export const WarehouseActions: React.FC<WarehouseActionsProps> = ({
  columnVisibility,
  onColumnVisibilityChange,
  onRefresh,
  onExport,
  loading = false,
}) => {
  return (
    <div className="flex gap-2">
      {/* Column Visibility Toggle */}
      <ColumnActions
        columns={
          Object.keys(columnVisibility) as (keyof WarehouseColumnVisibility)[]
        }
        columnVisibility={
          columnVisibility as unknown as Record<string, boolean>
        }
        onColumnVisibilityChange={onColumnVisibilityChange}
        columnLabels={{
          name: "<PERSON><PERSON>",
          description: "Deskripsi",
          address: "Alamat",
          phone: "Telepon",
          email: "Email",
          contactName: "Nama Kontak",
          isActive: "Status",
          isDefault: "Default",
          createdAt: "Dibuat",
          actions: "Aksi",
        }}
      />

      {/* Refresh Button */}
      <Button
        className="cursor-pointer"
        variant="outline"
        onClick={onRefresh}
        disabled={loading}
      >
        <RefreshCw className="h-4 w-4 sm:mr-2" />
        <span className="hidden sm:inline">Refresh</span>
      </Button>

      {/* Export Button */}
      <Button className="cursor-pointer" variant="outline" onClick={onExport}>
        <Download className="h-4 w-4 mr-2" />
        Export
      </Button>
    </div>
  );
};
