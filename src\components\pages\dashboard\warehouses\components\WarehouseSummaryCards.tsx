"use client";

import React from "react";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  BuildingStorefrontIcon, 
  CheckCircleIcon, 
  StarIcon 
} from "@heroicons/react/24/outline";
import { Warehouse } from "@/types/warehouse";

interface WarehouseSummaryCardsProps {
  warehouses: Warehouse[];
}

export const WarehouseSummaryCards: React.FC<WarehouseSummaryCardsProps> = ({
  warehouses
}) => {
  const totalWarehouses = warehouses.length;
  const activeWarehouses = warehouses.filter(w => w.isActive).length;
  const defaultWarehouse = warehouses.find(w => w.isDefault);

  const summaryData = [
    {
      title: "Total Gudang",
      value: totalWarehouses,
      icon: BuildingStorefrontIcon,
      description: "Semua gudang yang terdaftar",
      color: "text-blue-600",
      bgColor: "bg-blue-50",
    },
    {
      title: "Gudang Aktif",
      value: activeWarehouses,
      icon: CheckCircleIcon,
      description: "Gudang yang sedang beroperasi",
      color: "text-green-600",
      bgColor: "bg-green-50",
    },
    {
      title: "Gudang Default",
      value: defaultWarehouse?.name || "Tidak ada",
      icon: StarIcon,
      description: "Gudang utama untuk transaksi",
      color: "text-yellow-600",
      bgColor: "bg-yellow-50",
      isText: true,
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      {summaryData.map((item, index) => (
        <Card key={index} className="relative overflow-hidden">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              {item.title}
            </CardTitle>
            <div className={`p-2 rounded-lg ${item.bgColor}`}>
              <item.icon className={`h-5 w-5 ${item.color}`} />
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                {item.isText ? (
                  <p className="text-lg font-semibold text-gray-900 truncate max-w-[150px]">
                    {item.value}
                  </p>
                ) : (
                  <p className="text-2xl font-bold text-gray-900">
                    {item.value}
                  </p>
                )}
                <p className="text-xs text-gray-500 mt-1">
                  {item.description}
                </p>
              </div>
              {item.title === "Gudang Aktif" && totalWarehouses > 0 && (
                <Badge 
                  variant={activeWarehouses === totalWarehouses ? "default" : "secondary"}
                  className="ml-2"
                >
                  {Math.round((activeWarehouses / totalWarehouses) * 100)}%
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
