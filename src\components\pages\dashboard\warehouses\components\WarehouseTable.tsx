"use client";

import React, { useState } from "react";
import { toast } from "sonner";
import {
  Edit,
  Trash2,
  Star,
  <PERSON>Off,
  MoreHorizontal,
  Eye,
  EyeOff,
} from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Warehouse, WarehouseColumnVisibility } from "@/types/warehouse";
import {
  deleteWarehouse,
  setDefaultWarehouse,
} from "@/actions/entities/warehouses";

interface WarehouseTableProps {
  warehouses: Warehouse[];
  columnVisibility: WarehouseColumnVisibility;
  onEdit: (warehouse: Warehouse) => void;
  onRefresh: () => void;
}

export const WarehouseTable: React.FC<WarehouseTableProps> = ({
  warehouses,
  columnVisibility,
  onEdit,
  onRefresh,
}) => {
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [settingDefaultId, setSettingDefaultId] = useState<string | null>(null);

  const handleDelete = async (warehouse: Warehouse) => {
    try {
      setDeletingId(warehouse.id);
      await deleteWarehouse(warehouse.id);
      toast.success("Gudang berhasil dihapus");
      onRefresh();
    } catch (error) {
      console.error("Error deleting warehouse:", error);
      toast.error(
        error instanceof Error ? error.message : "Gagal menghapus gudang"
      );
    } finally {
      setDeletingId(null);
    }
  };

  const handleSetDefault = async (warehouse: Warehouse) => {
    try {
      setSettingDefaultId(warehouse.id);
      await setDefaultWarehouse(warehouse.id);
      toast.success(`${warehouse.name} berhasil dijadikan gudang default`);
      onRefresh();
    } catch (error) {
      console.error("Error setting default warehouse:", error);
      toast.error("Gagal mengatur gudang default");
    } finally {
      setSettingDefaultId(null);
    }
  };

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString("id-ID", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  if (warehouses.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Belum ada gudang yang terdaftar</p>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            {columnVisibility.name && <TableHead>Nama Gudang</TableHead>}
            {columnVisibility.description && <TableHead>Deskripsi</TableHead>}
            {columnVisibility.address && <TableHead>Alamat</TableHead>}
            {columnVisibility.phone && <TableHead>Telepon</TableHead>}
            {columnVisibility.email && <TableHead>Email</TableHead>}
            {columnVisibility.contactName && <TableHead>Nama Kontak</TableHead>}
            {columnVisibility.isActive && <TableHead>Status</TableHead>}
            {columnVisibility.isDefault && <TableHead>Default</TableHead>}
            {columnVisibility.createdAt && <TableHead>Dibuat</TableHead>}
            {columnVisibility.actions && (
              <TableHead className="w-[100px]">Aksi</TableHead>
            )}
          </TableRow>
        </TableHeader>
        <TableBody>
          {warehouses.map((warehouse) => (
            <TableRow key={warehouse.id}>
              {columnVisibility.name && (
                <TableCell className="font-medium">
                  <div className="flex items-center gap-2">
                    {warehouse.name}
                    {warehouse.isDefault && (
                      <Star className="h-4 w-4 text-yellow-500 fill-current" />
                    )}
                  </div>
                </TableCell>
              )}
              {columnVisibility.description && (
                <TableCell>
                  <span className="text-sm text-gray-600 truncate max-w-[200px] block">
                    {warehouse.description || "-"}
                  </span>
                </TableCell>
              )}
              {columnVisibility.address && (
                <TableCell>
                  <span className="text-sm text-gray-600 truncate max-w-[200px] block">
                    {warehouse.address || "-"}
                  </span>
                </TableCell>
              )}
              {columnVisibility.phone && (
                <TableCell>{warehouse.phone || "-"}</TableCell>
              )}
              {columnVisibility.email && (
                <TableCell>{warehouse.email || "-"}</TableCell>
              )}
              {columnVisibility.contactName && (
                <TableCell>{warehouse.contactName || "-"}</TableCell>
              )}
              {columnVisibility.isActive && (
                <TableCell>
                  <Badge variant={warehouse.isActive ? "default" : "secondary"}>
                    {warehouse.isActive ? "Aktif" : "Nonaktif"}
                  </Badge>
                </TableCell>
              )}
              {columnVisibility.isDefault && (
                <TableCell>
                  {warehouse.isDefault ? (
                    <Badge
                      variant="outline"
                      className="text-yellow-600 border-yellow-600"
                    >
                      Default
                    </Badge>
                  ) : (
                    "-"
                  )}
                </TableCell>
              )}
              {columnVisibility.createdAt && (
                <TableCell className="text-sm text-gray-600">
                  {formatDate(warehouse.createdAt)}
                </TableCell>
              )}
              {columnVisibility.actions && (
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => onEdit(warehouse)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>

                      {!warehouse.isDefault && (
                        <DropdownMenuItem
                          onClick={() => handleSetDefault(warehouse)}
                          disabled={settingDefaultId === warehouse.id}
                        >
                          <Star className="mr-2 h-4 w-4" />
                          Jadikan Default
                        </DropdownMenuItem>
                      )}

                      <DropdownMenuSeparator />

                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <DropdownMenuItem
                            className="text-red-600"
                            onSelect={(e) => e.preventDefault()}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Hapus
                          </DropdownMenuItem>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Hapus Gudang</AlertDialogTitle>
                            <AlertDialogDescription>
                              Apakah Anda yakin ingin menghapus gudang &quot;
                              {warehouse.name}&quot;? Tindakan ini tidak dapat
                              dibatalkan.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Batal</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleDelete(warehouse)}
                              disabled={deletingId === warehouse.id}
                              className="bg-red-600 hover:bg-red-700"
                            >
                              {deletingId === warehouse.id
                                ? "Menghapus..."
                                : "Hapus"}
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              )}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
