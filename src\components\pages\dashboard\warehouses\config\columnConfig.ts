import { WarehouseColumnVisibility, WarehouseStockColumnVisibility, StockMovementColumnVisibility } from "@/types/warehouse";

// Warehouse table column configuration
export interface WarehouseColumnConfig {
  key: keyof WarehouseColumnVisibility;
  label: string;
  sortable: boolean;
  defaultVisible: boolean;
}

export const warehouseColumnConfig: WarehouseColumnConfig[] = [
  {
    key: "name",
    label: "Nama Gudang",
    sortable: true,
    defaultVisible: true,
  },
  {
    key: "description",
    label: "Deskripsi",
    sortable: false,
    defaultVisible: true,
  },
  {
    key: "address",
    label: "Alamat",
    sortable: false,
    defaultVisible: true,
  },
  {
    key: "phone",
    label: "Telepon",
    sortable: false,
    defaultVisible: false,
  },
  {
    key: "email",
    label: "Email",
    sortable: false,
    defaultVisible: false,
  },
  {
    key: "contactName",
    label: "Nama Kontak",
    sortable: false,
    defaultVisible: false,
  },
  {
    key: "isActive",
    label: "Status",
    sortable: true,
    defaultVisible: true,
  },
  {
    key: "isDefault",
    label: "Default",
    sortable: true,
    defaultVisible: true,
  },
  {
    key: "createdAt",
    label: "Dibuat",
    sortable: true,
    defaultVisible: false,
  },
  {
    key: "actions",
    label: "Aksi",
    sortable: false,
    defaultVisible: true,
  },
];

// Warehouse stock table column configuration
export interface WarehouseStockColumnConfig {
  key: keyof WarehouseStockColumnVisibility;
  label: string;
  sortable: boolean;
  defaultVisible: boolean;
}

export const warehouseStockColumnConfig: WarehouseStockColumnConfig[] = [
  {
    key: "productName",
    label: "Nama Produk",
    sortable: true,
    defaultVisible: true,
  },
  {
    key: "sku",
    label: "SKU",
    sortable: true,
    defaultVisible: true,
  },
  {
    key: "quantity",
    label: "Stok",
    sortable: true,
    defaultVisible: true,
  },
  {
    key: "minLevel",
    label: "Min. Stok",
    sortable: true,
    defaultVisible: true,
  },
  {
    key: "maxLevel",
    label: "Max. Stok",
    sortable: true,
    defaultVisible: false,
  },
  {
    key: "status",
    label: "Status Stok",
    sortable: false,
    defaultVisible: true,
  },
  {
    key: "lastUpdated",
    label: "Terakhir Update",
    sortable: true,
    defaultVisible: false,
  },
  {
    key: "actions",
    label: "Aksi",
    sortable: false,
    defaultVisible: true,
  },
];

// Stock movement table column configuration
export interface StockMovementColumnConfig {
  key: keyof StockMovementColumnVisibility;
  label: string;
  sortable: boolean;
  defaultVisible: boolean;
}

export const stockMovementColumnConfig: StockMovementColumnConfig[] = [
  {
    key: "type",
    label: "Jenis",
    sortable: true,
    defaultVisible: true,
  },
  {
    key: "productName",
    label: "Produk",
    sortable: true,
    defaultVisible: true,
  },
  {
    key: "quantity",
    label: "Jumlah",
    sortable: true,
    defaultVisible: true,
  },
  {
    key: "previousStock",
    label: "Stok Sebelum",
    sortable: true,
    defaultVisible: true,
  },
  {
    key: "newStock",
    label: "Stok Sesudah",
    sortable: true,
    defaultVisible: true,
  },
  {
    key: "reference",
    label: "Referensi",
    sortable: false,
    defaultVisible: false,
  },
  {
    key: "notes",
    label: "Catatan",
    sortable: false,
    defaultVisible: true,
  },
  {
    key: "createdAt",
    label: "Tanggal",
    sortable: true,
    defaultVisible: true,
  },
  {
    key: "user",
    label: "User",
    sortable: false,
    defaultVisible: false,
  },
];

// Default column visibility settings
export const defaultWarehouseColumnVisibility: WarehouseColumnVisibility = {
  name: true,
  description: true,
  address: true,
  phone: false,
  email: false,
  contactName: false,
  isActive: true,
  isDefault: true,
  createdAt: false,
  actions: true,
};

export const defaultWarehouseStockColumnVisibility: WarehouseStockColumnVisibility = {
  productName: true,
  sku: true,
  quantity: true,
  minLevel: true,
  maxLevel: false,
  status: true,
  lastUpdated: false,
  actions: true,
};

export const defaultStockMovementColumnVisibility: StockMovementColumnVisibility = {
  type: true,
  productName: true,
  quantity: true,
  previousStock: true,
  newStock: true,
  reference: false,
  notes: true,
  createdAt: true,
  user: false,
};

// Helper function to get column order
export function getWarehouseColumnOrder(): (keyof WarehouseColumnVisibility)[] {
  return warehouseColumnConfig.map(col => col.key);
}

export function getWarehouseStockColumnOrder(): (keyof WarehouseStockColumnVisibility)[] {
  return warehouseStockColumnConfig.map(col => col.key);
}

export function getStockMovementColumnOrder(): (keyof StockMovementColumnVisibility)[] {
  return stockMovementColumnConfig.map(col => col.key);
}
