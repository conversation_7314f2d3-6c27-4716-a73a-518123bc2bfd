"use client";

import React from "react";
import { Control } from "react-hook-form";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Briefcase, Building, CreditCard, Gift } from "lucide-react";
import {
  OnboardingFormData,
  occupationOptions,
  industryOptions,
  subscriptionPackageOptions,
} from "../types";

interface AccountPreferencesStepProps {
  control: Control<OnboardingFormData>;
  isPending: boolean;
}

const AccountPreferencesStep: React.FC<AccountPreferencesStepProps> = ({
  control,
  isPending,
}) => {
  return (
    <div className="space-y-4">
      {/* Occupation */}
      <FormField
        control={control}
        name="occupation"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="flex items-center gap-2 text-gray-700">
              <Briefcase className="w-4 h-4 text-purple-600" />
              Pekerjaan
              <span className="text-red-500">*</span>
            </FormLabel>
            <Select onValueChange={field.onChange} defaultValue={field.value}>
              <FormControl>
                <SelectTrigger className="w-full h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500 cursor-pointer transition-all duration-200 hover:border-blue-400 text-gray-600">
                  <SelectValue placeholder="Pilih pekerjaan Anda" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {occupationOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Industry */}
      <FormField
        control={control}
        name="industry"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="flex items-center gap-2 text-gray-700">
              <Building className="w-4 h-4 text-purple-600" />
              Industri Perusahaan
              <span className="text-red-500">*</span>
            </FormLabel>
            <Select onValueChange={field.onChange} defaultValue={field.value}>
              <FormControl>
                <SelectTrigger className="w-full h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500 cursor-pointer transition-all duration-200 hover:border-blue-400 text-gray-600">
                  <SelectValue placeholder="Pilih industri perusahaan" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {industryOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormDescription>
              Pilih industri yang paling sesuai dengan bisnis Anda
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Subscription Package */}
      <FormField
        control={control}
        name="subscriptionPackage"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="flex items-center gap-2 text-gray-700">
              <CreditCard className="w-4 h-4 text-purple-600" />
              Paket Langganan
              <span className="text-red-500">*</span>
            </FormLabel>
            <Select onValueChange={field.onChange} defaultValue={field.value}>
              <FormControl>
                <SelectTrigger className="w-full h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500 cursor-pointer transition-all duration-200 hover:border-blue-400 text-gray-600">
                  <SelectValue placeholder="Pilih paket langganan" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {subscriptionPackageOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormDescription>
              Anda dapat mengubah paket langganan kapan saja di pengaturan akun
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Referral Code */}
      <FormField
        control={control}
        name="referralCode"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="flex items-center gap-2 text-gray-700">
              <Gift className="w-4 h-4 text-purple-600" />
              Kode Referral
              <span className="text-gray-400 text-sm">(Opsional)</span>
            </FormLabel>
            <FormControl>
              <Input
                placeholder="Jika ada, masukkan kode referral"
                {...field}
                disabled={isPending}
                className="h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500 text-gray-600"
              />
            </FormControl>
            <FormDescription>
              Masukkan kode referral jika Anda memilikinya untuk mendapatkan
              bonus
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Package Benefits Info */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-medium text-blue-900 mb-2">
          💡 Tips Memilih Paket
        </h4>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>
            • <strong>Basic:</strong> Cocok untuk bisnis kecil dengan transaksi
            terbatas
          </li>
          <li>
            • <strong>Pro:</strong> Ideal untuk bisnis menengah dengan fitur
            laporan lengkap
          </li>
          <li>
            • <strong>Business:</strong> Untuk bisnis berkembang dengan
            multi-cabang
          </li>
          <li>
            • <strong>Enterprise:</strong> Solusi lengkap untuk bisnis besar
          </li>
        </ul>
      </div>
    </div>
  );
};

export default AccountPreferencesStep;
