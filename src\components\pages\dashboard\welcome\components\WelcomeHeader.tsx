"use client";

import React from "react";
import { Building2, Settings } from "lucide-react";
import { OnboardingStep } from "../types";

interface WelcomeHeaderProps {
  currentStep: OnboardingStep;
}

const WelcomeHeader: React.FC<WelcomeHeaderProps> = ({ currentStep }) => {
  const getStepTitle = (step: OnboardingStep) => {
    switch (step) {
      case 1:
        return "Page 1 - Lengkapi Profile Bisnis";
      case 2:
        return "Page 2 - Preferensi Akun";
      default:
        return "Page 1 - Lengkapi Profile Bisnis";
    }
  };

  return (
    <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
      {/* Left Side - Company Branding */}
      <div className="flex items-center gap-3">
        <div className="flex items-center justify-center w-10 h-10 bg-blue-600 rounded-lg">
          <Building2 className="w-6 h-6 text-white" />
        </div>
        <div>
          <h1 className="text-xl font-bold text-gray-900 dark:text-gray-100">
            KivaPOS
          </h1>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Sistem Point of Sale Modern
          </p>
        </div>
      </div>

      {/* Right Side - Step Information */}
      <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
        <Settings className="w-5 h-5" />
        <span className="text-sm font-medium">{getStepTitle(currentStep)}</span>
      </div>
    </div>
  );
};

export default WelcomeHeader;
