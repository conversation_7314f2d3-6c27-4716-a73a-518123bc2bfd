import { z } from "zod";

// Step 1: Business Profile Schema
export const businessProfileSchema = z.object({
  companyName: z.string().min(1, "Nama perusahaan wajib diisi"),
  companyUsername: z
    .string()
    .min(3, "Username minimal 3 karakter")
    .max(20, "Username maksimal 20 karakter")
    .regex(/^[a-zA-Z0-9_]+$/, "Username hanya boleh mengandung huruf, angka, dan underscore"),
  phoneNumber: z
    .string()
    .min(10, "Nomor handphone minimal 10 digit")
    .max(15, "Nomor handphone maksimal 15 digit")
    .regex(/^[0-9+\-\s()]+$/, "Format nomor handphone tidak valid"),
  password: z
    .string()
    .min(8, "Password minimal 8 karakter")
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, "Password harus mengandung huruf besar, huruf k<PERSON>il, dan angka"),
  position: z.string().min(1, "Jabatan wajib dipilih"),
  employeeCount: z.string().min(1, "Jum<PERSON> karyawan wajib dipilih"),
});

// Step 2: Account Preferences Schema
export const accountPreferencesSchema = z.object({
  occupation: z.string().min(1, "Pekerjaan wajib diisi"),
  industry: z.string().min(1, "Industri perusahaan wajib dipilih"),
  subscriptionPackage: z.string().min(1, "Paket langganan wajib dipilih"),
  referralCode: z.string().optional(),
});

// Combined schema for the entire onboarding process
export const onboardingSchema = businessProfileSchema.merge(accountPreferencesSchema);

// TypeScript types
export type BusinessProfileFormData = z.infer<typeof businessProfileSchema>;
export type AccountPreferencesFormData = z.infer<typeof accountPreferencesSchema>;
export type OnboardingFormData = z.infer<typeof onboardingSchema>;

// Step indicator type
export type OnboardingStep = 1 | 2;

// Position options
export const positionOptions = [
  { value: "owner", label: "Owner" },
  { value: "manager", label: "Manager" },
  { value: "staff", label: "Staff" },
  { value: "admin", label: "Admin" },
] as const;

// Employee count options
export const employeeCountOptions = [
  { value: "1-5", label: "1-5 orang" },
  { value: "6-20", label: "6-20 orang" },
  { value: "21-50", label: "21-50 orang" },
  { value: "50+", label: "50+ orang" },
] as const;

// Industry options
export const industryOptions = [
  { value: "retail", label: "Retail" },
  { value: "food-beverage", label: "Makanan & Minuman" },
  { value: "fashion", label: "Fashion" },
  { value: "electronics", label: "Elektronik" },
  { value: "automotive", label: "Otomotif" },
  { value: "health-beauty", label: "Kesehatan & Kecantikan" },
  { value: "services", label: "Jasa" },
  { value: "other", label: "Lainnya" },
] as const;

// Subscription package options
export const subscriptionPackageOptions = [
  { value: "basic", label: "Basic - Gratis" },
  { value: "pro", label: "Pro - Rp 99.000/bulan" },
  { value: "business", label: "Business - Rp 199.000/bulan" },
  { value: "enterprise", label: "Enterprise - Rp 399.000/bulan" },
] as const;

// Occupation options
export const occupationOptions = [
  { value: "entrepreneur", label: "Pengusaha" },
  { value: "business-owner", label: "Pemilik Bisnis" },
  { value: "manager", label: "Manajer" },
  { value: "employee", label: "Karyawan" },
  { value: "freelancer", label: "Freelancer" },
  { value: "student", label: "Mahasiswa" },
  { value: "other", label: "Lainnya" },
] as const;
