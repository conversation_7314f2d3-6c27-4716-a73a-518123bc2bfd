"use client";

import React from "react";
import { PublicPageLayout } from "@/components/layout/public-page-layout";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";

interface LegalPageLayoutProps {
  title: string;
  description: string;
  lastUpdated: string;
  children: React.ReactNode;
}

export const LegalPageLayout: React.FC<LegalPageLayoutProps> = ({
  title,
  description,
  lastUpdated,
  children,
}) => {
  return (
    <PublicPageLayout title={title} description={description}>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardContent className="p-8">
              {/* Header */}
              <div className="mb-8">
                <div className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                  Terakhir diperbarui: {lastUpdated}
                </div>
                <Separator className="mb-6" />
              </div>

              {/* Content */}
              <div className="prose prose-gray dark:prose-invert max-w-none">
                {children}
              </div>

              {/* Footer */}
              <Separator className="my-8" />
              <div className="text-sm text-gray-500 dark:text-gray-400">
                <p>
                  Jika Anda memiliki pertanyaan tentang kebijakan ini, silakan hubungi kami di{" "}
                  <a 
                    href="mailto:<EMAIL>" 
                    className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                  >
                    <EMAIL>
                  </a>
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </PublicPageLayout>
  );
};
