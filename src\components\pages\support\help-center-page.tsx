"use client";

import React, { useState } from "react";
import { PublicPageLayout } from "@/components/layout/public-page-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Search,
  HelpCircle,
  BookOpen,
  MessageCircle,
  Phone,
  Mail,
  Clock,
  CheckCircle,
  AlertCircle,
  Info,
} from "lucide-react";

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
  tags: string[];
}

interface HelpCategory {
  id: string;
  title: string;
  description: string;
  icon: React.ElementType;
  articles: number;
}

export const HelpCenterPage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");

  const helpCategories: HelpCategory[] = [
    {
      id: "getting-started",
      title: "Memulai",
      description: "Panduan dasar untuk pengguna baru",
      icon: BookOpen,
      articles: 8,
    },
    {
      id: "transactions",
      title: "Transaksi",
      description: "Cara membuat dan mengelola transaksi",
      icon: CheckCircle,
      articles: 12,
    },
    {
      id: "products",
      title: "Produk",
      description: "Manajemen inventori dan produk",
      icon: Info,
      articles: 6,
    },
    {
      id: "reports",
      title: "Laporan",
      description: "Memahami dan menggunakan laporan",
      icon: AlertCircle,
      articles: 5,
    },
  ];

  const faqItems: FAQItem[] = [
    {
      id: "1",
      question: "Bagaimana cara mendaftar akun KivaPOS?",
      answer: "Untuk mendaftar akun KivaPOS, kunjungi halaman pendaftaran, isi formulir dengan informasi bisnis Anda, verifikasi email, dan akun Anda akan aktif dalam beberapa menit.",
      category: "getting-started",
      tags: ["pendaftaran", "akun", "memulai"],
    },
    {
      id: "2",
      question: "Apakah KivaPOS bisa digunakan offline?",
      answer: "KivaPOS memerlukan koneksi internet untuk sinkronisasi data. Namun, beberapa fitur dasar dapat berfungsi dalam mode offline terbatas dan akan sinkron otomatis saat koneksi tersedia.",
      category: "getting-started",
      tags: ["offline", "internet", "sinkronisasi"],
    },
    {
      id: "3",
      question: "Bagaimana cara menambah produk baru?",
      answer: "Masuk ke menu Produk, klik 'Tambah Produk', isi informasi produk seperti nama, harga, stok, dan kategori, lalu klik 'Simpan'. Produk akan langsung tersedia untuk transaksi.",
      category: "products",
      tags: ["produk", "tambah", "inventori"],
    },
    {
      id: "4",
      question: "Metode pembayaran apa saja yang didukung?",
      answer: "KivaPOS mendukung berbagai metode pembayaran termasuk tunai, transfer bank, e-wallet (GoPay, OVO, DANA), kartu kredit/debit, dan sistem kredit untuk pelanggan.",
      category: "transactions",
      tags: ["pembayaran", "metode", "transaksi"],
    },
    {
      id: "5",
      question: "Bagaimana cara melihat laporan penjualan?",
      answer: "Buka menu Laporan di sidebar, pilih jenis laporan yang diinginkan (harian, bulanan, atau custom), atur periode waktu, dan klik 'Generate Laporan'. Anda juga bisa export ke PDF atau Excel.",
      category: "reports",
      tags: ["laporan", "penjualan", "analitik"],
    },
    {
      id: "6",
      question: "Apakah data saya aman di KivaPOS?",
      answer: "Ya, KivaPOS menggunakan enkripsi tingkat bank, backup otomatis, dan server yang aman. Data Anda dilindungi dengan standar keamanan internasional dan compliance dengan regulasi perlindungan data.",
      category: "getting-started",
      tags: ["keamanan", "data", "privasi"],
    },
  ];

  const filteredFAQs = faqItems.filter((faq) => {
    const matchesSearch = faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         faq.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesCategory = selectedCategory === "all" || faq.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <PublicPageLayout
      title="Pusat Bantuan"
      description="Temukan jawaban atas pertanyaan Anda tentang KivaPOS"
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Search Section */}
        <div className="max-w-2xl mx-auto mb-12">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <Input
              type="text"
              placeholder="Cari bantuan, panduan, atau FAQ..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 py-3 text-lg"
            />
          </div>
        </div>

        {/* Help Categories */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {helpCategories.map((category) => (
            <Card 
              key={category.id}
              className={`cursor-pointer transition-all hover:shadow-lg ${
                selectedCategory === category.id ? 'ring-2 ring-blue-500' : ''
              }`}
              onClick={() => setSelectedCategory(category.id)}
            >
              <CardHeader className="text-center">
                <div className="mx-auto mb-4 p-3 bg-blue-100 dark:bg-blue-900/20 rounded-full w-fit">
                  <category.icon className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                </div>
                <CardTitle className="text-lg">{category.title}</CardTitle>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  {category.description}
                </p>
              </CardHeader>
              <CardContent className="text-center">
                <Badge variant="secondary">
                  {category.articles} artikel
                </Badge>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Filter Buttons */}
        <div className="flex flex-wrap gap-2 mb-8">
          <Button
            variant={selectedCategory === "all" ? "default" : "outline"}
            onClick={() => setSelectedCategory("all")}
          >
            Semua
          </Button>
          {helpCategories.map((category) => (
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? "default" : "outline"}
              onClick={() => setSelectedCategory(category.id)}
            >
              {category.title}
            </Button>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* FAQ Section */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <HelpCircle className="h-6 w-6" />
                  Pertanyaan yang Sering Diajukan
                </CardTitle>
                <p className="text-gray-600 dark:text-gray-300">
                  {filteredFAQs.length} pertanyaan ditemukan
                </p>
              </CardHeader>
              <CardContent>
                <Accordion type="single" collapsible className="w-full">
                  {filteredFAQs.map((faq) => (
                    <AccordionItem key={faq.id} value={faq.id}>
                      <AccordionTrigger className="text-left">
                        {faq.question}
                      </AccordionTrigger>
                      <AccordionContent>
                        <p className="text-gray-600 dark:text-gray-300 mb-3">
                          {faq.answer}
                        </p>
                        <div className="flex flex-wrap gap-1">
                          {faq.tags.map((tag, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </CardContent>
            </Card>
          </div>

          {/* Contact Support */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageCircle className="h-5 w-5" />
                  Butuh Bantuan Lebih?
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <Mail className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="font-medium">Email Support</p>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      <EMAIL>
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <Phone className="h-5 w-5 text-green-600" />
                  <div>
                    <p className="font-medium">WhatsApp</p>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      +62 812 3456 7890
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <Clock className="h-5 w-5 text-orange-600" />
                  <div>
                    <p className="font-medium">Jam Operasional</p>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      Senin - Jumat: 09:00 - 18:00
                    </p>
                  </div>
                </div>
                
                <Button className="w-full" asChild>
                  <a href="/contact">
                    Hubungi Kami
                  </a>
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Dokumentasi Lengkap</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Pelajari semua fitur KivaPOS dengan panduan lengkap kami.
                </p>
                <Button variant="outline" className="w-full" asChild>
                  <a href="/docs">
                    Buka Dokumentasi
                  </a>
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </PublicPageLayout>
  );
};
