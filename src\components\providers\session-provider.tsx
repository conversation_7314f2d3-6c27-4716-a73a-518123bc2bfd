"use client";

import { SessionProvider } from "next-auth/react";
import { ReactNode } from "react";

export default function NextAuthProvider({
  children,
}: {
  children: ReactNode;
}) {
  return (
    <SessionProvider
      refetchInterval={30 * 60} // Refetch session every 30 minutes (reduced frequency)
      refetchOnWindowFocus={false} // Keep disabled to prevent navigation interference
    >
      {children}
    </SessionProvider>
  );
}
