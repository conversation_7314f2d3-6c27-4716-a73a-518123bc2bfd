import React, { useState } from "react";
import { FilterIcon, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import { PaymentStatus, SubscriptionPlan } from "@prisma/client";
import { paymentStatusFilterOptions } from "../config/columnConfig";

export interface PaymentFilterState {
  status?: PaymentStatus | "all";
  dateFrom?: Date;
  dateTo?: Date;
  planType?: SubscriptionPlan | "all";
  amountRange?: {
    min?: number;
    max?: number;
  };
}

interface PaymentFilterProps {
  filters: PaymentFilterState;
  onFilterChange: (filters: PaymentFilterState) => void;
}

export const PaymentFilter: React.FC<PaymentFilterProps> = ({
  filters,
  onFilterChange,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [localFilters, setLocalFilters] = useState<PaymentFilterState>(filters);

  // Apply filters
  const applyFilters = () => {
    onFilterChange(localFilters);
    setIsOpen(false);
  };

  // Reset filters
  const resetFilters = () => {
    const emptyFilters: PaymentFilterState = {
      status: "all",
      dateFrom: undefined,
      dateTo: undefined,
      planType: "all",
      amountRange: { min: undefined, max: undefined },
    };
    setLocalFilters(emptyFilters);
    onFilterChange(emptyFilters);
  };

  // Count active filters
  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.status && filters.status !== "all") count++;
    if (filters.dateFrom || filters.dateTo) count++;
    if (filters.planType && filters.planType !== "all") count++;
    if (filters.amountRange?.min || filters.amountRange?.max) count++;
    return count;
  };

  const activeFilterCount = getActiveFilterCount();

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm" className="flex items-center gap-2">
          <FilterIcon className="h-4 w-4" />
          Filter
          {activeFilterCount > 0 && (
            <Badge variant="destructive" className="ml-1 text-xs">
              {activeFilterCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="end">
        <div className="flex items-center justify-between p-4 border-b">
          <h4 className="font-medium">Filter Pembayaran</h4>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsOpen(false)}
            className="h-6 w-6 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        <div className="flex-1 overflow-y-auto space-y-4 p-4">
          {/* Status Filter */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Status Pembayaran</Label>
            <Select
              value={localFilters.status || "all"}
              onValueChange={(value) =>
                setLocalFilters((prev) => ({
                  ...prev,
                  status: value === "all" ? "all" : (value as PaymentStatus),
                }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Pilih status" />
              </SelectTrigger>
              <SelectContent>
                {paymentStatusFilterOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <Separator />

          {/* Plan Type Filter */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Jenis Paket</Label>
            <Select
              value={localFilters.planType || "all"}
              onValueChange={(value) =>
                setLocalFilters((prev) => ({
                  ...prev,
                  planType: value === "all" ? "all" : (value as SubscriptionPlan),
                }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Pilih paket" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Semua Paket</SelectItem>
                <SelectItem value="BASIC">Basic</SelectItem>
                <SelectItem value="PRO">Pro</SelectItem>
                <SelectItem value="ENTERPRISE">Enterprise</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Separator />

          {/* Date Range Filter */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Rentang Tanggal</Label>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label className="text-xs text-gray-500">Dari</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                      size="sm"
                    >
                      {localFilters.dateFrom ? (
                        format(localFilters.dateFrom, "dd MMM yyyy", { locale: id })
                      ) : (
                        <span className="text-gray-500">Pilih tanggal</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={localFilters.dateFrom}
                      onSelect={(date) =>
                        setLocalFilters((prev) => ({
                          ...prev,
                          dateFrom: date,
                        }))
                      }
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              <div>
                <Label className="text-xs text-gray-500">Sampai</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                      size="sm"
                    >
                      {localFilters.dateTo ? (
                        format(localFilters.dateTo, "dd MMM yyyy", { locale: id })
                      ) : (
                        <span className="text-gray-500">Pilih tanggal</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={localFilters.dateTo}
                      onSelect={(date) =>
                        setLocalFilters((prev) => ({
                          ...prev,
                          dateTo: date,
                        }))
                      }
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </div>

          <Separator />

          {/* Amount Range Filter */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Rentang Jumlah (IDR)</Label>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label className="text-xs text-gray-500">Min</Label>
                <input
                  type="number"
                  placeholder="0"
                  className="w-full px-3 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-500"
                  value={localFilters.amountRange?.min || ""}
                  onChange={(e) =>
                    setLocalFilters((prev) => ({
                      ...prev,
                      amountRange: {
                        ...prev.amountRange,
                        min: e.target.value ? Number(e.target.value) : undefined,
                      },
                    }))
                  }
                />
              </div>
              <div>
                <Label className="text-xs text-gray-500">Max</Label>
                <input
                  type="number"
                  placeholder="999999999"
                  className="w-full px-3 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-500"
                  value={localFilters.amountRange?.max || ""}
                  onChange={(e) =>
                    setLocalFilters((prev) => ({
                      ...prev,
                      amountRange: {
                        ...prev.amountRange,
                        max: e.target.value ? Number(e.target.value) : undefined,
                      },
                    }))
                  }
                />
              </div>
            </div>
          </div>
        </div>

        {/* Footer Actions */}
        <div className="flex items-center justify-between p-4 border-t bg-gray-50 dark:bg-gray-800">
          <Button variant="ghost" size="sm" onClick={resetFilters}>
            Reset
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={() => setIsOpen(false)}>
              Batal
            </Button>
            <Button size="sm" onClick={applyFilters}>
              Terapkan
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};
