"use client";

import { useState, useEffect } from "react";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  CreditCard,
  Calendar,
  DollarSign,
  FileText,
  RefreshCwIcon,
  ExternalLinkIcon,
  Building,
  User,
  Phone,
  Mail,
  MapPin,
} from "lucide-react";
import { PaymentStatus } from "@prisma/client";
import { SUBSCRIPTION_PLANS } from "@/lib/subscription";
import { toast } from "sonner";

interface Payment {
  id: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  paymentMethod: string | null;
  externalUrl: string | null;
  invoiceId: string | null;
  paymentDate: Date | null;
  expiryDate: Date | null;
  createdAt: Date;
  metadata: any;
  subscription: {
    id: string;
    plan: string;
  } | null;
}

interface PaymentDetailModalProps {
  payment: Payment | null;
  isOpen: boolean;
  onClose: () => void;
}

interface MidtransTransactionDetail {
  order_id: string;
  transaction_status: string;
  transaction_time: string;
  payment_type: string;
  gross_amount: string;
  currency: string;
  fraud_status?: string;
  status_code: string;
  status_message: string;
  merchant_id: string;
  va_numbers?: Array<{
    bank: string;
    va_number: string;
  }>;
  bca_va_number?: string;
  bill_key?: string;
  biller_code?: string;
  pdf_url?: string;
  finish_redirect_url?: string;
  custom_field1?: string;
  custom_field2?: string;
  custom_field3?: string;
}

export default function PaymentDetailModal({
  payment,
  isOpen,
  onClose,
}: PaymentDetailModalProps) {
  const [midtransDetail, setMidtransDetail] =
    useState<MidtransTransactionDetail | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Format currency
  const formatCurrency = (amount: number, currency = "IDR") => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Format date
  const formatDate = (date: Date | string | null) => {
    if (!date) return "-";
    const dateObj = typeof date === "string" ? new Date(date) : date;
    return format(dateObj, "dd MMMM yyyy, HH:mm", { locale: id });
  };

  // Get status badge
  const getStatusBadge = (status: PaymentStatus) => {
    const statusConfig = {
      PENDING: { label: "Menunggu", variant: "secondary" as const },
      COMPLETED: { label: "Berhasil", variant: "default" as const },
      FAILED: { label: "Gagal", variant: "destructive" as const },
      EXPIRED: { label: "Kedaluwarsa", variant: "outline" as const },
      REFUNDED: { label: "Dikembalikan", variant: "outline" as const },
    };

    const config = statusConfig[status] || statusConfig.PENDING;
    return (
      <Badge variant={config.variant} className="font-medium">
        {config.label}
      </Badge>
    );
  };

  // Fetch Midtrans transaction details
  const fetchMidtransDetails = async (orderId: string) => {
    setIsLoading(true);
    setError(null);

    try {
      console.log("🔍 [PAYMENT DETAIL] Fetching Midtrans details:", {
        orderId,
      });

      const response = await fetch(`/api/payments/midtrans-detail/${orderId}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch payment details: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        console.log("✅ [PAYMENT DETAIL] Midtrans details fetched:", data.data);
        setMidtransDetail(data.data);
      } else {
        throw new Error(data.error || "Failed to fetch payment details");
      }
    } catch (error) {
      console.error(
        "❌ [PAYMENT DETAIL] Error fetching Midtrans details:",
        error
      );

      // Check if it's a 500 error (payment not completed yet)
      if (
        error instanceof Error &&
        (error.message.includes("500") ||
          error.message.includes("Failed to fetch payment details: 500"))
      ) {
        setError(
          "Transaksi belum dibayar, silahkan bayar terlebih dahulu untuk melihat detail"
        );
      } else {
        setError(
          error instanceof Error ? error.message : "Unknown error occurred"
        );
      }

      toast.error("Gagal memuat detail pembayaran dari Midtrans");
    } finally {
      setIsLoading(false);
    }
  };

  // Load Midtrans details when modal opens
  useEffect(() => {
    if (isOpen && payment?.invoiceId) {
      fetchMidtransDetails(payment.invoiceId);
    } else {
      setMidtransDetail(null);
      setError(null);
    }
  }, [isOpen, payment?.invoiceId]);

  if (!payment) return null;

  const planName = payment.subscription
    ? SUBSCRIPTION_PLANS[
        payment.subscription.plan as keyof typeof SUBSCRIPTION_PLANS
      ]?.name
    : "Unknown";

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Detail Pembayaran
          </DialogTitle>
          <DialogDescription>
            Informasi lengkap tentang transaksi pembayaran Anda
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Payment Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Informasi Pembayaran
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Order ID
                  </label>
                  <p className="font-mono text-sm">
                    {payment.invoiceId || "-"}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Status
                  </label>
                  <div className="mt-1">{getStatusBadge(payment.status)}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Paket
                  </label>
                  <p className="font-medium">{planName}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Jumlah
                  </label>
                  <p className="font-semibold text-lg">
                    {formatCurrency(Number(payment.amount), payment.currency)}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Tanggal Dibuat
                  </label>
                  <p>{formatDate(payment.createdAt)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Tanggal Bayar
                  </label>
                  <p>{formatDate(payment.paymentDate)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Midtrans Transaction Details */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Building className="h-5 w-5" />
                Detail Transaksi Midtrans
                {payment.invoiceId && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => fetchMidtransDetails(payment.invoiceId!)}
                    disabled={isLoading}
                    className="ml-auto"
                  >
                    {isLoading ? (
                      <RefreshCwIcon className="h-4 w-4 animate-spin mr-2" />
                    ) : (
                      <RefreshCwIcon className="h-4 w-4 mr-2" />
                    )}
                    Refresh
                  </Button>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <RefreshCwIcon className="h-6 w-6 animate-spin mr-2" />
                  <span>Memuat detail transaksi...</span>
                </div>
              ) : error ? (
                <div className="text-center py-8">
                  <p className="text-red-600 mb-4">{error}</p>
                  <Button
                    variant="outline"
                    onClick={() => fetchMidtransDetails(payment.invoiceId!)}
                    disabled={!payment.invoiceId}
                  >
                    <RefreshCwIcon className="h-4 w-4 mr-2" />
                    Coba Lagi
                  </Button>
                </div>
              ) : midtransDetail ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Transaction Status
                      </label>
                      <p className="font-medium">
                        {midtransDetail.transaction_status}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Payment Type
                      </label>
                      <p>{midtransDetail.payment_type || "-"}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Transaction Time
                      </label>
                      <p>{formatDate(midtransDetail.transaction_time)}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Status Code
                      </label>
                      <p>{midtransDetail.status_code}</p>
                    </div>
                  </div>

                  {midtransDetail.va_numbers &&
                    midtransDetail.va_numbers.length > 0 && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">
                          Virtual Account
                        </label>
                        {midtransDetail.va_numbers.map((va, index) => (
                          <div key={index} className="mt-1">
                            <p className="font-mono">
                              {va.bank}: {va.va_number}
                            </p>
                          </div>
                        ))}
                      </div>
                    )}

                  {midtransDetail.status_message && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Status Message
                      </label>
                      <p>{midtransDetail.status_message}</p>
                    </div>
                  )}
                </div>
              ) : (
                <p className="text-muted-foreground text-center py-4">
                  Tidak ada detail transaksi Midtrans yang tersedia
                </p>
              )}
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3">
            {payment.externalUrl && payment.status === "PENDING" && (
              <Button
                variant="default"
                onClick={() => window.open(payment.externalUrl!, "_blank")}
              >
                <ExternalLinkIcon className="h-4 w-4 mr-2" />
                Bayar Sekarang
              </Button>
            )}
            <Button variant="outline" onClick={onClose}>
              Tutup
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
