import { PaymentStatus, SubscriptionPlan } from "@prisma/client";

export interface PaymentSubscription {
  id: string;
  plan: SubscriptionPlan;
}

export interface Payment {
  id: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  paymentMethod: string | null;
  externalId: string | null;
  externalUrl: string | null;
  invoiceId: string | null;
  paymentDate: Date | null;
  expiryDate: Date | null;
  metadata: any;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  subscriptionId: string | null;
  serviceId: string | null;
  subscription: PaymentSubscription | null;
}

export interface PaymentHistoryFilters {
  status?: PaymentStatus | "all";
  dateFrom?: Date;
  dateTo?: Date;
  planType?: SubscriptionPlan | "all";
  search?: string;
}

export interface PaymentHistorySortOptions {
  field: "createdAt" | "amount" | "status" | "paymentDate" | "invoiceId";
  direction: "asc" | "desc";
}

export interface PaymentStats {
  total: number;
  pending: number;
  completed: number;
  failed: number;
  expired: number;
  refunded: number;
  totalAmount: number;
  completedAmount: number;
}

export interface MidtransTransactionDetail {
  order_id: string;
  transaction_status: string;
  transaction_time: string;
  payment_type: string;
  gross_amount: string;
  currency: string;
  fraud_status?: string;
  status_code: string;
  status_message: string;
  merchant_id: string;
  va_numbers?: Array<{
    bank: string;
    va_number: string;
  }>;
  bca_va_number?: string;
  bill_key?: string;
  biller_code?: string;
  pdf_url?: string;
  finish_redirect_url?: string;
  custom_field1?: string;
  custom_field2?: string;
  custom_field3?: string;
}

export interface PaymentDetailModalProps {
  payment: Payment | null;
  isOpen: boolean;
  onClose: () => void;
}

export interface PaymentHistoryTableProps {
  payments: Payment[];
  isLoading: boolean;
  onRefresh: (paymentId: string) => void;
  onDelete: (paymentId: string) => void;
  onPaymentClick: (payment: Payment) => void;
  refreshingId: string | null;
  deletingId: string | null;
  deleteConfirmId: string | null;
  onDeleteConfirm: (paymentId: string) => void;
  onDeleteCancel: () => void;
}

// Column visibility interface matching the config
export interface PaymentColumnVisibility {
  date: boolean;
  orderId: boolean;
  package: boolean;
  amount: boolean;
  status: boolean;
  paymentDate: boolean;
  actions: boolean;
}
