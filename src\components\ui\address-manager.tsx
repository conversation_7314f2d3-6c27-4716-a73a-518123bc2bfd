"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Plus, Trash2, GripVertical } from "lucide-react";
import { cn } from "@/lib/utils";

interface AddressManagerProps {
  label: string;
  addresses: string[];
  onChange: (addresses: string[]) => void;
  placeholder?: string;
  maxAddresses?: number;
  isEditing?: boolean;
  className?: string;
}

export const AddressManager: React.FC<AddressManagerProps> = ({
  label,
  addresses,
  onChange,
  placeholder = "Masukkan alamat",
  maxAddresses = 5,
  isEditing = true,
  className,
}) => {
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);

  // Add new address
  const addAddress = () => {
    if (addresses.length < maxAddresses) {
      onChange([...addresses, ""]);
    }
  };

  // Remove address at index
  const removeAddress = (index: number) => {
    const newAddresses = addresses.filter((_, i) => i !== index);
    onChange(newAddresses);
  };

  // Update address at index
  const updateAddress = (index: number, value: string) => {
    const newAddresses = [...addresses];
    newAddresses[index] = value;
    onChange(newAddresses);
  };

  // Handle drag start
  const handleDragStart = (e: React.DragEvent, index: number) => {
    setDraggedIndex(index);
    e.dataTransfer.effectAllowed = "move";
  };

  // Handle drag over
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";
  };

  // Handle drop
  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();

    if (draggedIndex === null || draggedIndex === dropIndex) {
      setDraggedIndex(null);
      return;
    }

    const newAddresses = [...addresses];
    const draggedAddress = newAddresses[draggedIndex];

    // Remove dragged item
    newAddresses.splice(draggedIndex, 1);

    // Insert at new position
    const insertIndex = draggedIndex < dropIndex ? dropIndex - 1 : dropIndex;
    newAddresses.splice(insertIndex, 0, draggedAddress);

    onChange(newAddresses);
    setDraggedIndex(null);
  };

  // Handle drag end
  const handleDragEnd = () => {
    setDraggedIndex(null);
  };

  if (!isEditing) {
    // Read-only view
    return (
      <div className={cn("space-y-2", className)}>
        <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
          {label}
        </Label>
        {addresses.length === 0 ? (
          <div className="text-sm text-gray-500 dark:text-gray-400 py-2">
            Tidak ada alamat
          </div>
        ) : (
          <div className="space-y-2">
            {addresses.map((address, index) => (
              <div
                key={index}
                className="flex items-center space-x-3 py-2 px-3 bg-gray-50 dark:bg-gray-800 rounded-md"
              >
                <div className="flex-shrink-0 w-6 h-6 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center">
                  <span className="text-xs font-medium text-indigo-600 dark:text-indigo-400">
                    {index + 1}
                  </span>
                </div>
                <div className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                  {address || "-"}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  }

  // Editable view
  return (
    <div className={cn("space-y-3", className)}>
      <div className="flex items-center justify-between">
        <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
          {label}
        </Label>
        {addresses.length < maxAddresses && (
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={addAddress}
            className="flex items-center gap-1 text-xs"
          >
            <Plus className="h-3 w-3" />
            Tambah
          </Button>
        )}
      </div>

      {addresses.length === 0 ? (
        <div className="text-sm text-gray-500 dark:text-gray-400 py-4 text-center border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
          Belum ada alamat. Klik &quotTambah&quot untuk menambahkan alamat.
        </div>
      ) : (
        <div className="space-y-2">
          {addresses.map((address, index) => (
            <Card
              key={index}
              className={cn(
                "transition-all duration-200",
                draggedIndex === index && "opacity-50 scale-95"
              )}
              draggable={addresses.length > 1}
              onDragStart={(e) => handleDragStart(e, index)}
              onDragOver={handleDragOver}
              onDrop={(e) => handleDrop(e, index)}
              onDragEnd={handleDragEnd}
            >
              <CardContent className="p-3">
                <div className="flex items-center gap-2">
                  {addresses.length > 1 && (
                    <div className="flex-shrink-0 cursor-move">
                      <GripVertical className="h-4 w-4 text-gray-400" />
                    </div>
                  )}

                  <div className="flex-shrink-0 w-6 h-6 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center">
                    <span className="text-xs font-medium text-indigo-600 dark:text-indigo-400">
                      {index + 1}
                    </span>
                  </div>

                  <div className="flex-1">
                    <Input
                      type="text"
                      value={address}
                      onChange={(e) => updateAddress(index, e.target.value)}
                      placeholder={`${placeholder} ${index + 1}`}
                      className="w-full"
                    />
                  </div>

                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => removeAddress(index)}
                    className="flex-shrink-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {addresses.length >= maxAddresses && (
        <div className="text-xs text-gray-500 dark:text-gray-400">
          Maksimal {maxAddresses} alamat dapat ditambahkan.
        </div>
      )}
    </div>
  );
};

export default AddressManager;
