"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { AlertTriangle, RefreshCw, Database } from "lucide-react";

interface DatabaseErrorModalProps {
  isOpen: boolean;
  onRefresh: () => void;
  title?: string;
  description?: string;
  errorMessage?: string;
}

export const DatabaseErrorModal: React.FC<DatabaseErrorModalProps> = ({
  isOpen,
  onRefresh,
  title = "Koneksi Database Bermasalah",
  description = "Terjadi masalah saat mengakses database. Hal ini mungkin disebabkan oleh gangguan koneksi sementara.",
  errorMessage,
}) => {
  const handleRefresh = () => {
    // Refresh the page
    window.location.reload();
  };

  return (
    <Dialog open={isOpen} onOpenChange={() => {}}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto [&>button]:hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3 text-red-600 dark:text-red-400">
            <div className="flex items-center justify-center w-12 h-12 rounded-full bg-red-100 dark:bg-red-900/30">
              <Database className="h-6 w-6" />
            </div>
            {title}
          </DialogTitle>
          <DialogDescription className="text-gray-600 dark:text-gray-400 mt-4">
            {description}
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <AlertTriangle className="h-5 w-5 text-red-500 dark:text-red-400 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <h4 className="text-sm font-medium text-red-800 dark:text-red-200 mb-2">
                  Kemungkinan Penyebab:
                </h4>
                <ul className="text-sm text-red-700 dark:text-red-300 space-y-1">
                  <li>• Koneksi internet tidak stabil</li>
                  <li>• Server database sedang mengalami gangguan</li>
                  <li>• Terlalu banyak permintaan dalam waktu bersamaan</li>
                </ul>

                {errorMessage && (
                  <div className="mt-3 pt-3 border-t border-red-200 dark:border-red-700">
                    <p className="text-xs text-red-600 dark:text-red-400 font-mono">
                      Detail Error: {errorMessage}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="flex flex-col sm:flex-row gap-3">
          <div className="flex-1 text-center sm:text-left">
            <p className="text-xs text-muted-foreground">
              Jika masalah berlanjut, silakan hubungi administrator sistem
            </p>
          </div>
          <Button
            onClick={handleRefresh}
            className="bg-blue-600 hover:bg-blue-700 text-white gap-2 min-w-[120px]"
          >
            <RefreshCw className="h-4 w-4" />
            Refresh Halaman
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
