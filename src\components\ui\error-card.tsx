import React from "react";
import {
  <PERSON>,
  CardContent,
  Card<PERSON>es<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AlertCircle, RefreshCw, ShoppingCart, Plus } from "lucide-react";
import Link from "next/link";

interface ErrorCardProps {
  title: string;
  description: string;
  retryLink?: string;
  retryLabel?: string;
  onRetry?: () => void;
  showPurchaseButton?: boolean;
  showAddProductButton?: boolean;
}

export const ErrorCard: React.FC<ErrorCardProps> = ({
  title,
  description,
  retryLink,
  retryLabel = "Coba Lagi",
  onRetry,
  showPurchaseButton = false,
  showAddProductButton = false,
}) => {
  return (
    <Card className="w-full max-w-2xl mx-auto mt-8 border-red-200 dark:border-red-900">
      <CardHeader className="pb-4">
        <div className="flex items-center gap-2">
          <AlertCircle className="h-6 w-6 text-red-500 dark:text-red-400" />
          <CardTitle className="text-red-600 dark:text-red-400">
            {title}
          </CardTitle>
        </div>
        <CardDescription className="text-gray-600 dark:text-gray-400">
          {description}
        </CardDescription>
      </CardHeader>
      <CardContent className="pb-4">
        <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-md text-sm text-red-600 dark:text-red-400">
          <p>
            {showPurchaseButton
              ? "Untuk menambah stok produk yang sudah ada, buat pembelian baru. Untuk produk baru, tambahkan produk terlebih dahulu."
              : "Jika masalah berlanjut, silakan hubungi administrator sistem atau coba lagi nanti."}
          </p>
        </div>
      </CardContent>
      <CardFooter className="flex flex-wrap gap-2 justify-end">
        {onRetry && (
          <Button
            variant="outline"
            onClick={onRetry}
            className="gap-2 text-red-600 dark:text-red-400 border-red-200 dark:border-red-800 hover:bg-red-50 dark:hover:bg-red-900/20"
          >
            <RefreshCw className="h-4 w-4" />
            {retryLabel}
          </Button>
        )}

        {showAddProductButton && (
          <Button
            variant="outline"
            asChild
            className="gap-2 text-blue-600 dark:text-blue-400 border-blue-200 dark:border-blue-800 hover:bg-blue-50 dark:hover:bg-blue-900/20"
          >
            <Link href="/dashboard/products/new">
              <Plus className="h-4 w-4" />
              Tambah Produk
            </Link>
          </Button>
        )}

        {showPurchaseButton && (
          <Button
            variant="default"
            asChild
            className="gap-2 bg-green-600 hover:bg-green-700 text-white"
          >
            <Link href="/dashboard/purchases/new">
              <ShoppingCart className="h-4 w-4" />
              Buat Pembelian
            </Link>
          </Button>
        )}

        {retryLink && (
          <Button
            variant="outline"
            asChild
            className="gap-2 text-red-600 dark:text-red-400 border-red-200 dark:border-red-800 hover:bg-red-50 dark:hover:bg-red-900/20"
          >
            <Link href={retryLink}>
              <RefreshCw className="h-4 w-4" />
              {retryLabel}
            </Link>
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};
