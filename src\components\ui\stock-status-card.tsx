import React from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  Card<PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  AlertTriangle,
  Package,
  ShoppingCart,
  Plus,
  ExternalLink,
  CheckCircle,
  XCircle,
} from "lucide-react";
import Link from "next/link";

interface StockStatusCardProps {
  availableCount: number;
  outOfStockCount: number;
  totalProducts: number;
}

export const StockStatusCard: React.FC<StockStatusCardProps> = ({
  availableCount,
  outOfStockCount,
  totalProducts,
}) => {
  const hasOutOfStock = outOfStockCount > 0;
  const hasAvailable = availableCount > 0;

  return (
    <Card className="w-full max-w-2xl mx-auto mt-4 mb-6">
      <CardHeader className="pb-4">
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/20">
            <Package className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <CardTitle className="text-lg">Status Stok Produk</CardTitle>
            <CardDescription>
              Ringkasan ketersediaan produk untuk penjualan
            </CardDescription>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Stock Summary */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          {/* Total Products */}
          <div className="flex items-center justify-between p-3 rounded-lg border bg-gray-50 dark:bg-gray-800/50">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Produk
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {totalProducts}
              </p>
            </div>
            <Package className="h-8 w-8 text-gray-400" />
          </div>

          {/* Available Products */}
          <div className="flex items-center justify-between p-3 rounded-lg border bg-green-50 dark:bg-green-900/20">
            <div>
              <p className="text-sm font-medium text-green-600 dark:text-green-400">
                Tersedia
              </p>
              <p className="text-2xl font-bold text-green-700 dark:text-green-300">
                {availableCount}
              </p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-500" />
          </div>

          {/* Out of Stock Products */}
          <div className="flex items-center justify-between p-3 rounded-lg border bg-red-50 dark:bg-red-900/20">
            <div>
              <p className="text-sm font-medium text-red-600 dark:text-red-400">
                Habis Stok
              </p>
              <p className="text-2xl font-bold text-red-700 dark:text-red-300">
                {outOfStockCount}
              </p>
            </div>
            <XCircle className="h-8 w-8 text-red-500" />
          </div>
        </div>

        {/* Status Messages */}
        {hasOutOfStock && (
          <div className="p-4 rounded-lg bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800">
            <div className="flex items-start gap-3">
              <AlertTriangle className="h-5 w-5 text-amber-600 dark:text-amber-400 mt-0.5" />
              <div>
                <h4 className="font-medium text-amber-800 dark:text-amber-200">
                  Perhatian: Produk Habis Stok
                </h4>
                <p className="text-sm text-amber-700 dark:text-amber-300 mt-1">
                  {outOfStockCount} produk sedang habis stok dan tidak dapat
                  dijual. Produk ini akan ditampilkan dalam daftar tetapi tidak
                  dapat dipilih.
                </p>
              </div>
            </div>
          </div>
        )}

        {hasAvailable && (
          <div className="p-4 rounded-lg bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800">
            <div className="flex items-start gap-3">
              <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400 mt-0.5" />
              <div>
                <h4 className="font-medium text-green-800 dark:text-green-200">
                  Siap untuk Penjualan
                </h4>
                <p className="text-sm text-green-700 dark:text-green-300 mt-1">
                  {availableCount} produk tersedia dan siap untuk dijual.
                </p>
              </div>
            </div>
          </div>
        )}
      </CardContent>

      <CardFooter className="flex flex-wrap gap-2 pt-4">
        <Button asChild variant="default" size="sm">
          <Link href="/dashboard/products" className="flex items-center gap-2">
            <Package className="h-4 w-4" />
            Kelola Produk
          </Link>
        </Button>

        <Button asChild variant="outline" size="sm">
          <Link
            href="/dashboard/products/new"
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Tambah Produk
          </Link>
        </Button>

        {hasOutOfStock && (
          <Button
            asChild
            variant="default"
            size="sm"
            className="bg-green-600 hover:bg-green-700"
          >
            <Link
              href="/dashboard/purchases/new"
              className="flex items-center gap-2"
            >
              <ShoppingCart className="h-4 w-4" />
              Buat Pembelian
            </Link>
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};
