"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { AlertTriangle } from "lucide-react";

interface SubscriptionExpiryDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const SubscriptionExpiryDialog: React.FC<
  SubscriptionExpiryDialogProps
> = ({ open, onOpenChange }) => {
  const router = useRouter();

  const handleUpgrade = () => {
    onOpenChange(false);
    router.push("/dashboard/settings/plans");
  };

  const handleClose = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md p-0 overflow-hidden bg-white dark:bg-gray-900 rounded-xl shadow-2xl border dark:border-gray-800">
        <div className="p-6">
          <DialogHeader className="flex flex-row items-start gap-4">
            <div className="flex-shrink-0 flex h-12 w-12 items-center justify-center rounded-lg bg-orange-100 dark:bg-orange-900/20">
              <AlertTriangle className="h-6 w-6 text-orange-600 dark:text-orange-400" />
            </div>
            <div>
              <DialogTitle className="text-xl font-bold text-gray-900 dark:text-gray-50">
                Langganan Anda Telah Berakhir
              </DialogTitle>
              <DialogDescription className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Perpanjang langganan untuk dapat kembali membuat transaksi dan
                mengakses semua fitur.
              </DialogDescription>
            </div>
          </DialogHeader>
        </div>

        <DialogFooter className="flex flex-row-reverse gap-3 bg-gray-50 dark:bg-black/20 px-6 py-4">
          <Button
            onClick={handleUpgrade}
            className="w-full sm:w-auto bg-gradient-to-r from-blue-600 to-blue-500 text-white font-semibold shadow hover:shadow-md hover:opacity-95 transition-all duration-200 cursor-pointer"
          >
            Perpanjang Sekarang
          </Button>
          <Button
            variant="outline"
            onClick={handleClose}
            className="w-full sm:w-auto bg-transparent dark:border-gray-700 dark:hover:bg-gray-800 cursor-pointer"
          >
            Nanti Saja
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

// Hook to manage subscription expiry dialog state
export const useSubscriptionExpiryDialog = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { data: session } = useSession();

  useEffect(() => {
    const checkSubscriptionExpiry = async () => {
      if (
        !session?.user?.id ||
        sessionStorage.getItem("subscriptionExpiryDialogShown")
      ) {
        return;
      }

      try {
        const response = await fetch("/api/subscription/expiry");
        if (response.ok) {
          const data = await response.json();
          if (data.expired) {
            setIsOpen(true);
            sessionStorage.setItem("subscriptionExpiryDialogShown", "true");
          }
        }
      } catch (error) {
        console.error("Error checking subscription expiry:", error);
      }
    };

    if (session?.user?.id) {
      checkSubscriptionExpiry();
    }
  }, [session?.user?.id]);

  return {
    isOpen,
    setIsOpen,
  };
};
