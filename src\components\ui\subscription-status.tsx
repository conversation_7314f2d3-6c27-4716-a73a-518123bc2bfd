"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import {
  AlertTriangle,
  CheckCircle,
  Crown,
  Lock,
  Zap,
  Users,
  Package,
  Receipt,
  Building,
} from "lucide-react";
import { useSubscriptionLimits } from "@/hooks/useSubscriptionLimits";
import Link from "next/link";

interface SubscriptionStatusProps {
  showDetails?: boolean;
  compact?: boolean;
  className?: string;
}

export const SubscriptionStatus: React.FC<SubscriptionStatusProps> = ({
  showDetails = false,
  compact = false,
  className = "",
}) => {
  const { products, contacts, transactions, users, isLoading, error } =
    useSubscriptionLimits();

  if (isLoading) {
    return (
      <Card className={`animate-pulse ${className}`}>
        <CardContent className="p-4">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
          <div className="h-3 bg-gray-200 rounded w-1/2"></div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={`border-red-200 bg-red-50 ${className}`}>
        <CardContent className="p-4">
          <div className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-4 w-4" />
            <span className="text-sm">Gagal memuat status langganan</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const limits = [
    {
      name: "Produk",
      icon: Package,
      current: products.currentUsage || 0,
      limit: products.limit || 0,
      allowed: products.allowed,
      message: products.message,
    },
    {
      name: "Kontak",
      icon: Users,
      current: contacts.currentUsage || 0,
      limit: contacts.limit || 0,
      allowed: contacts.allowed,
      message: contacts.message,
    },
    {
      name: "Transaksi",
      icon: Receipt,
      current: transactions.currentUsage || 0,
      limit: transactions.limit || 0,
      allowed: transactions.allowed,
      message: transactions.message,
    },
    {
      name: "Pengguna",
      icon: Building,
      current: users.currentUsage || 0,
      limit: users.limit || 0,
      allowed: users.allowed,
      message: users.message,
    },
  ];

  const getStatusColor = (current: number, limit: number, allowed: boolean) => {
    if (!allowed) return "text-red-500";
    const percentage = (current / limit) * 100;
    if (percentage >= 90) return "text-red-500";
    if (percentage >= 75) return "text-yellow-500";
    return "text-green-500";
  };

  const getProgressColor = (current: number, limit: number) => {
    const percentage = (current / limit) * 100;
    if (percentage >= 90) return "bg-red-500";
    if (percentage >= 75) return "bg-yellow-500";
    return "bg-green-500";
  };

  if (compact) {
    const criticalLimits = limits.filter(
      (limit) => !limit.allowed || (limit.current / limit.limit) * 100 >= 75
    );

    if (criticalLimits.length === 0) {
      return (
        <div className={`flex items-center gap-2 text-green-600 ${className}`}>
          <CheckCircle className="h-4 w-4" />
          <span className="text-sm">Langganan aktif</span>
        </div>
      );
    }

    return (
      <Card className={`border-yellow-200 bg-yellow-50 ${className}`}>
        <CardContent className="p-3">
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-4 w-4 text-yellow-600" />
            <span className="text-sm text-yellow-800">
              {criticalLimits.length} batas mendekati limit
            </span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Crown className="h-5 w-5 text-yellow-500" />
          Status Langganan
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {limits.map((limit) => {
          const percentage = limit.limit > 0 ? (limit.current / limit.limit) * 100 : 0;
          const Icon = limit.icon;
          const statusColor = getStatusColor(limit.current, limit.limit, limit.allowed);

          return (
            <div key={limit.name} className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Icon className={`h-4 w-4 ${statusColor}`} />
                  <span className="text-sm font-medium">{limit.name}</span>
                  {!limit.allowed && (
                    <Badge variant="destructive" className="text-xs">
                      <Lock className="h-3 w-3 mr-1" />
                      Limit
                    </Badge>
                  )}
                </div>
                <span className={`text-sm font-medium ${statusColor}`}>
                  {limit.current} / {limit.limit}
                </span>
              </div>

              {showDetails && (
                <div className="space-y-1">
                  <Progress
                    value={percentage}
                    className="h-2"
                    style={{
                      background: `linear-gradient(to right, ${getProgressColor(
                        limit.current,
                        limit.limit
                      )} 0%, ${getProgressColor(limit.current, limit.limit)} ${percentage}%, #e5e7eb ${percentage}%, #e5e7eb 100%)`,
                    }}
                  />
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>{percentage.toFixed(1)}% terpakai</span>
                    {percentage >= 75 && (
                      <span className="text-yellow-600">
                        Mendekati batas
                      </span>
                    )}
                    {!limit.allowed && (
                      <span className="text-red-600">Batas tercapai</span>
                    )}
                  </div>
                  {limit.message && (
                    <p className="text-xs text-gray-600">{limit.message}</p>
                  )}
                </div>
              )}
            </div>
          );
        })}

        {showDetails && (
          <div className="pt-3 border-t">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4 text-blue-500" />
                <span className="text-sm font-medium">Upgrade Paket</span>
              </div>
              <Link href="/dashboard/subscription">
                <Button size="sm" variant="outline">
                  Lihat Paket
                </Button>
              </Link>
            </div>
            <p className="text-xs text-gray-600 mt-1">
              Tingkatkan paket untuk mendapatkan batas yang lebih tinggi
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Specific warning component for when limits are reached
export const SubscriptionLimitWarning: React.FC<{
  type: "products" | "contacts" | "transactions" | "users";
  className?: string;
}> = ({ type, className = "" }) => {
  const limits = useSubscriptionLimits();
  const limitData = limits[type];

  if (limitData.allowed) return null;

  const typeLabels = {
    products: "produk",
    contacts: "kontak",
    transactions: "transaksi",
    users: "pengguna",
  };

  return (
    <Card className={`border-red-200 bg-red-50 ${className}`}>
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          <AlertTriangle className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
          <div className="space-y-1">
            <p className="text-sm font-medium text-red-800">
              Batas {typeLabels[type]} tercapai
            </p>
            <p className="text-sm text-red-700">
              {limitData.message || `Anda telah mencapai batas maksimal ${typeLabels[type]} untuk paket saat ini.`}
            </p>
            <Link href="/dashboard/subscription">
              <Button size="sm" variant="outline" className="mt-2">
                Upgrade Paket
              </Button>
            </Link>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
