"use client";

import { useEffect, useRef } from "react";

interface UseNotificationPollingOptions {
  onPoll: () => void | Promise<void>;
  interval?: number; // in milliseconds
  enabled?: boolean;
  pollOnFocus?: boolean;
  dependencies?: any[];
}

/**
 * Custom hook for polling notifications or any other data
 * Provides automatic polling with configurable interval and focus-based polling
 */
export const useNotificationPolling = ({
  onPoll,
  interval = 30000, // Default 30 seconds
  enabled = true,
  pollOnFocus = true,
  dependencies = [],
}: UseNotificationPollingOptions) => {
  const pollIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const isPollingRef = useRef(false);

  // Set up periodic polling
  useEffect(() => {
    if (!enabled) return;

    const startPolling = () => {
      if (pollIntervalRef.current) {
        clearInterval(pollIntervalRef.current);
      }

      pollIntervalRef.current = setInterval(async () => {
        if (!isPollingRef.current) {
          isPollingRef.current = true;
          try {
            await onPoll();
          } catch (error) {
            console.error("Polling error:", error);
          } finally {
            isPollingRef.current = false;
          }
        }
      }, interval);
    };

    startPolling();

    return () => {
      if (pollIntervalRef.current) {
        clearInterval(pollIntervalRef.current);
        pollIntervalRef.current = null;
      }
    };
  }, [enabled, interval, onPoll, ...dependencies]);

  // Set up focus-based polling
  useEffect(() => {
    if (!enabled || !pollOnFocus) return;

    const handleFocus = async () => {
      if (!isPollingRef.current) {
        isPollingRef.current = true;
        try {
          await onPoll();
        } catch (error) {
          console.error("Focus polling error:", error);
        } finally {
          isPollingRef.current = false;
        }
      }
    };

    window.addEventListener("focus", handleFocus);
    return () => window.removeEventListener("focus", handleFocus);
  }, [enabled, pollOnFocus, onPoll, ...dependencies]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (pollIntervalRef.current) {
        clearInterval(pollIntervalRef.current);
      }
    };
  }, []);
};

export default useNotificationPolling;
