"use client";

import { useState, useEffect } from "react";
import { SubscriptionPlan } from "@prisma/client";

interface LimitCheckResult {
  allowed: boolean;
  message?: string;
  currentUsage?: number;
  limit?: number;
}

interface SubscriptionLimits {
  products: LimitCheckResult;
  contacts: LimitCheckResult;
  suppliers: LimitCheckResult;
  customers: LimitCheckResult;
  transactions: LimitCheckResult;
  users: LimitCheckResult;
  isLoading: boolean;
  error: string | null;
}

export function useSubscriptionLimits(): SubscriptionLimits {
  const [limits, setLimits] = useState<SubscriptionLimits>({
    products: { allowed: true },
    contacts: { allowed: true },
    suppliers: { allowed: true },
    customers: { allowed: true },
    transactions: { allowed: true },
    users: { allowed: true },
    isLoading: true,
    error: null,
  });

  useEffect(() => {
    const fetchLimits = async () => {
      try {
        setLimits((prev) => ({ ...prev, isLoading: true, error: null }));

        const response = await fetch("/api/subscription/limits");

        if (!response.ok) {
          throw new Error("Failed to fetch subscription limits");
        }

        const data = await response.json();

        setLimits({
          products: data.products || { allowed: true },
          contacts: data.contacts || { allowed: true },
          suppliers: data.suppliers || { allowed: true },
          customers: data.customers || { allowed: true },
          transactions: data.transactions || { allowed: true },
          users: data.users || { allowed: true },
          isLoading: false,
          error: null,
        });
      } catch (error) {
        console.error("Error fetching subscription limits:", error);
        setLimits((prev) => ({
          ...prev,
          isLoading: false,
          error: error instanceof Error ? error.message : "Unknown error",
        }));
      }
    };

    fetchLimits();
  }, []);

  return limits;
}

// Hook specifically for contact limits
export function useContactLimits() {
  const { contacts, isLoading, error } = useSubscriptionLimits();

  return {
    canCreateContact: contacts.allowed,
    contactMessage: contacts.message,
    currentContactUsage: contacts.currentUsage,
    contactLimit: contacts.limit,
    isLoading,
    error,
  };
}

// Hook specifically for product limits
export function useProductLimits() {
  const { products, isLoading, error } = useSubscriptionLimits();

  return {
    canCreateProduct: products.allowed,
    productMessage: products.message,
    currentProductUsage: products.currentUsage,
    productLimit: products.limit,
    isLoading,
    error,
  };
}

// Hook specifically for transaction limits
export function useTransactionLimits() {
  const { transactions, isLoading, error } = useSubscriptionLimits();

  return {
    canCreateTransaction: transactions.allowed,
    transactionMessage: transactions.message,
    currentTransactionUsage: transactions.currentUsage,
    transactionLimit: transactions.limit,
    isLoading,
    error,
  };
}

// Hook specifically for user limits
export function useUserLimits() {
  const { users, isLoading, error } = useSubscriptionLimits();

  return {
    canCreateUser: users.allowed,
    userMessage: users.message,
    currentUserUsage: users.currentUsage,
    userLimit: users.limit,
    isLoading,
    error,
  };
}
