import { useRouter } from "next/navigation";
import { usePathname } from "next/navigation";
import { useCallback, useEffect, useState, useRef } from "react";

/**
 * Hook to warn users about unsaved changes when navigating away
 * @param hasUnsavedChang<PERSON> Boolean indicating if there are unsaved changes
 * @returns Object with methods and state for handling navigation
 */
export function useUnsavedChangesWarning(hasUnsavedChanges: boolean) {
  const router = useRouter();
  const pathname = usePathname();
  const [showExitDialog, setShowExitDialog] = useState(false);
  const [pendingNavigation, setPendingNavigation] = useState<string | null>(
    null
  );
  const currentPathRef = useRef(pathname);

  // Handle browser navigation (refresh, close tab, etc.)
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        // Standard way to show a confirmation dialog before leaving
        e.preventDefault();
        // This is deprecated but still needed for browser compatibility
        e.returnValue = "";
        return "";
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => window.removeEventListener("beforeunload", handleBeforeUnload);
  }, [hasUnsavedChanges]);

  // Update currentPathRef when pathname changes
  useEffect(() => {
    currentPathRef.current = pathname;
  }, [pathname]);

  // Intercept all link clicks
  useEffect(() => {
    if (!hasUnsavedChanges) return;

    const handleLinkClick = (e: MouseEvent) => {
      // Check if the click is on an anchor tag or its child
      const target = e.target as HTMLElement;
      const anchor = target.closest("a");

      if (!anchor) return;

      // Get the href attribute
      const href = anchor.getAttribute("href");

      // Only intercept internal links (not external or anchor links)
      if (
        !href ||
        href.startsWith("http") ||
        href.startsWith("#") ||
        href.startsWith("mailto:") ||
        href.startsWith("tel:")
      ) {
        return;
      }

      // Check if the href is a dashboard link or other internal link
      // We want to intercept all internal navigation

      // Prevent default navigation
      e.preventDefault();
      e.stopPropagation();

      // Store the pending navigation and show dialog
      setPendingNavigation(href);
      setShowExitDialog(true);
    };

    // Add event listener to the document
    document.addEventListener("click", handleLinkClick, true);

    // Clean up
    return () => {
      document.removeEventListener("click", handleLinkClick, true);
    };
  }, [hasUnsavedChanges]);

  // Handle Next.js navigation events
  useEffect(() => {
    if (!hasUnsavedChanges) return;

    // This will run when the pathname changes
    if (pathname !== currentPathRef.current) {
      // We've already navigated, so we can't prevent it
      // But we can update our ref for next time
      currentPathRef.current = pathname;
    }

    // We can't directly intercept Next.js navigation events
    // But we've added click handlers for links, which should cover most cases

    // For sidebar links and other navigation elements, we need to ensure they use our handleNavigation
    // function instead of direct Next.js navigation
  }, [pathname, hasUnsavedChanges]);

  // Handle programmatic navigation
  const handleNavigation = useCallback(
    (url: string) => {
      if (hasUnsavedChanges) {
        // Store the pending navigation and show dialog
        setPendingNavigation(url);
        setShowExitDialog(true);
      } else {
        // No unsaved changes, navigate directly
        router.push(url);
      }
    },
    [hasUnsavedChanges, router]
  );

  // Confirm navigation and discard changes
  const confirmNavigation = useCallback(() => {
    if (pendingNavigation) {
      router.push(pendingNavigation);
    }
    setShowExitDialog(false);
  }, [pendingNavigation, router]);

  // Cancel navigation and stay on page
  const cancelNavigation = useCallback(() => {
    setPendingNavigation(null);
    setShowExitDialog(false);
  }, []);

  return {
    showExitDialog,
    setShowExitDialog,
    pendingNavigation,
    handleNavigation,
    confirmNavigation,
    cancelNavigation,
  };
}
