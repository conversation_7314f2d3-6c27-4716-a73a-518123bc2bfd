import NextAuth from "next-auth";
import Resend from "next-auth/providers/resend";
import { PrismaAdapter } from "@auth/prisma-adapter";
import authConfig from "./auth.config";
import { db as database } from "./prisma";
import { Role } from "@prisma/client";
import { hasPermission } from "./rbac";
import { generateNextCompanyId } from "@/actions/users/company-id";
import { uploadToS3 } from "./s3";

const combinedProviders = [
  ...authConfig.providers,
  Resend({
    from: "updates.example.com",
  }),
];

// 2 hours in seconds - more secure session duration
const TWO_HOURS = 2 * 60 * 60;

export const { handlers, signIn, signOut, auth } = NextAuth({
  trustHost: true,
  providers: combinedProviders,
  adapter: PrismaAdapter(database),
  session: {
    strategy: "jwt",
    maxAge: TWO_HOURS, // 2 hours in seconds - more secure
    updateAge: 30 * 60, // 30 minutes - how often to update the session
  },
  jwt: {
    maxAge: TWO_HOURS, // 2 hours in seconds - more secure
  },
  pages: { signIn: "/login" },
  // Callbacks untuk pengganti middleware
  callbacks: {
    authorized({ auth, request: { nextUrl } }) {
      const isLoggedIn = !!auth?.user;
      const pathname = nextUrl.pathname;
      const hostname = nextUrl.hostname;
      const isAuthRoute = [
        "/login",
        "/register",
        "/send-verification",
        "/verification-email",
      ].includes(pathname);
      const protectedRoutes = ["/dashboard", "/user"];

      // Helper function to construct dashboard URL
      const getDashboardUrl = (path: string = "/summaries") => {
        const protocol = nextUrl.protocol;
        const isDevelopment = hostname.includes("localhost");
        const baseDomain = isDevelopment ? "localhost:3000" : "kivapos.com";
        const dashboardHost = `dashboard.${baseDomain}`;
        return `${protocol}//${dashboardHost}${path}`;
      };

      // Redirect authenticated users away from auth routes
      if (isLoggedIn && isAuthRoute) {
        return Response.redirect(new URL(getDashboardUrl()));
      }

      // Redirect unauthenticated users to login
      if (
        !isLoggedIn &&
        protectedRoutes.some((route) => pathname.startsWith(route))
      ) {
        return Response.redirect(
          new URL("/login", nextUrl.toString().replace(/"/g, ""))
        );
      }

      // Check role-based permissions for authenticated users
      if (isLoggedIn && pathname.startsWith("/dashboard")) {
        const userRole = auth.user?.role as Role;

        // If user doesn't have permission for this page, redirect to dashboard
        if (!hasPermission(userRole, pathname)) {
          return Response.redirect(new URL(getDashboardUrl()));
        }

        // Check onboarding status for dashboard routes (except welcome page)
        // Skip onboarding checks for employees - they don't need to complete onboarding
        const isEmployee = auth.user?.isEmployee;

        console.log("🔍 AUTH MIDDLEWARE: Checking onboarding for user:", {
          isEmployee,
          pathname,
          hasCompletedOnboarding: auth.user?.hasCompletedOnboarding,
          userRole: auth.user?.role,
        });

        if (
          !isEmployee && // Only check onboarding for non-employees
          pathname !== "/dashboard/welcome" &&
          !pathname.startsWith("/dashboard/welcome")
        ) {
          const hasCompletedOnboarding = auth.user?.hasCompletedOnboarding;

          // If onboarding is not completed, redirect to welcome page
          if (!hasCompletedOnboarding) {
            console.log(
              "🔄 AUTH MIDDLEWARE: Redirecting to welcome page (onboarding not completed)"
            );
            return Response.redirect(new URL(getDashboardUrl("/welcome")));
          }
        }

        // If user has completed onboarding and tries to access welcome page, redirect to dashboard
        // Also redirect employees away from welcome page since they don't need onboarding
        if (
          pathname === "/dashboard/welcome" ||
          pathname.startsWith("/dashboard/welcome")
        ) {
          const hasCompletedOnboarding = auth.user?.hasCompletedOnboarding;

          // Redirect employees away from welcome page
          if (isEmployee) {
            console.log(
              "🔄 AUTH MIDDLEWARE: Redirecting employee away from welcome page"
            );
            return Response.redirect(new URL(getDashboardUrl("/summaries")));
          }

          // Redirect owners who have completed onboarding away from welcome page
          if (hasCompletedOnboarding) {
            console.log(
              "🔄 AUTH MIDDLEWARE: Redirecting completed owner away from welcome page"
            );
            return Response.redirect(new URL(getDashboardUrl("/summaries")));
          }
        }
      }

      return true;
    },

    // Add user ID and role to the JWT
    async jwt({ token, user, account, trigger }) {
      // Only log when there's meaningful activity (not routine session checks)
      if (user || account || trigger === "update") {
        console.log("🔄 JWT Callback triggered:", {
          trigger,
          hasUser: !!user,
          hasAccount: !!account,
        });
      }

      // Ensure user and user.id exist before assigning
      if (user?.id) {
        token.sub = user.id; // 'sub' is the standard JWT claim for subject (user ID)

        // Add role if it exists on the user object, otherwise default to OWNER
        // This ensures Google OAuth users always have a role
        token.role = user.role || Role.OWNER;

        // Add login timestamp
        token.loginTimestamp = Date.now();

        // Update lastLogin in the database
        try {
          await database.user.update({
            where: { id: user.id },
            data: {
              lastLogin: new Date(),
            },
          });
        } catch (error) {
          console.error("Error updating lastLogin:", error);
        }

        // For Google OAuth users, ensure role and company ID are set in the database
        if (account?.provider === "google") {
          try {
            // Get current user data to check what needs to be updated
            const currentUser = await database.user.findUnique({
              where: { id: user.id },
              select: {
                role: true,
                provider: true,
                image: true,
                businessInfo: {
                  select: {
                    companyId: true,
                  },
                },
              },
            });

            const updateData: any = {};

            // Set role if not already set
            if (!currentUser?.role) {
              updateData.role = Role.OWNER;
            }

            // Set provider if not already set
            if (!currentUser?.provider) {
              updateData.provider = "google";
            }

            // Handle profile picture upload
            if (user.image && !currentUser?.image?.includes("s3")) {
              const response = await fetch(user.image);
              if (response.ok) {
                const blob = await response.blob();
                const file = new File([blob], "profile-picture.jpg", {
                  type: blob.type,
                });

                const uploadResult = await uploadToS3(
                  file,
                  user.id,
                  "profile-pictures"
                );

                if (uploadResult.success && uploadResult.url) {
                  updateData.image = uploadResult.url;
                  token.picture = uploadResult.url;
                }
              }
            }

            // Update user if there's anything to update
            if (Object.keys(updateData).length > 0) {
              await database.user.update({
                where: { id: user.id },
                data: updateData,
              });
            }

            // Generate and assign company ID if user doesn't have one and is an OWNER
            if (
              !currentUser?.businessInfo?.companyId &&
              (currentUser?.role === Role.OWNER || !currentUser?.role)
            ) {
              const companyId = await generateNextCompanyId();

              // Create or update BusinessInfo with company ID
              await database.businessInfo.upsert({
                where: { userId: user.id },
                create: {
                  userId: user.id,
                  companyId: companyId,
                },
                update: {
                  companyId: companyId,
                },
              });

              console.log(
                `🏢 Assigned company ID ${companyId} to Google OAuth user ${user.id}`
              );
            }
          } catch (error) {
            console.error("Error updating Google OAuth user:", error);
            // Even if the update fails, we'll still set the role in the token
          }
        }

        // Add employee-specific data if this is an employee login
        if (user.isEmployee) {
          token.isEmployee = true;
          token.ownerId = user.ownerId;
          token.employeeId = user.employeeId;
        }
      }

      // If this is a token update (not initial sign-in)
      if (trigger === "update" && token.sub) {
        console.log(
          "🔄 JWT Update trigger - refreshing user data for:",
          token.sub
        );
        try {
          // Determine if this is an employee or regular user
          if (token.isEmployee) {
            // For employees, fetch from employee table
            console.log("🔄 Refreshing employee data...");
            const employee = await database.employee.findUnique({
              where: { id: token.sub },
            });

            if (employee) {
              token.role = employee.role;
              token.isEmployee = true;
              token.ownerId = employee.ownerId;
              token.employeeId = employee.employeeId;
              console.log("✅ Employee data refreshed:", {
                role: employee.role,
                employeeId: employee.employeeId,
              });
            }
          } else {
            // For regular users, fetch from user table
            console.log("🔄 Refreshing user data...");
            const latestUser = await database.user.findUnique({
              where: { id: token.sub },
              select: {
                role: true,
                hasCompletedOnboarding: true,
              },
            });

            // Update the token with the latest role and onboarding status if available
            if (latestUser?.role) {
              token.role = latestUser.role;
            }
            if (latestUser?.hasCompletedOnboarding !== undefined) {
              token.hasCompletedOnboarding = latestUser.hasCompletedOnboarding;
            }
            console.log("✅ User data refreshed:", {
              role: latestUser?.role,
              hasCompletedOnboarding: latestUser?.hasCompletedOnboarding,
            });
          }
        } catch (error) {
          console.error("❌ Error updating token data:", error);
        }
      }

      // Always fetch onboarding status for authenticated users (but not for employees)
      if (token.sub && !token.hasCompletedOnboarding && !token.isEmployee) {
        try {
          const userData = await database.user.findUnique({
            where: { id: token.sub },
            select: { hasCompletedOnboarding: true },
          });

          if (userData?.hasCompletedOnboarding !== undefined) {
            token.hasCompletedOnboarding = userData.hasCompletedOnboarding;
          }
        } catch (error) {
          console.error("Error fetching onboarding status:", error);
        }
      }

      // For employees, always set hasCompletedOnboarding to true since they don't need onboarding
      if (token.isEmployee) {
        token.hasCompletedOnboarding = true;
      }

      return token;
    },

    // Add user ID and role to the session object from the JWT
    session({ session, token }) {
      if (token.sub && session.user) {
        session.user.id = token.sub; // Add ID to session.user
      }
      if (token.role && session.user) {
        session.user.role = token.role as Role; // Add role to session.user
      }
      if (token.loginTimestamp && session.user) {
        session.user.loginTimestamp = token.loginTimestamp; // Add login timestamp to session.user
      }
      if (token.hasCompletedOnboarding !== undefined && session.user) {
        session.user.hasCompletedOnboarding =
          token.hasCompletedOnboarding as boolean; // Add onboarding status to session.user
      }

      // Add employee-specific data if this is an employee login
      if (token.isEmployee && session.user) {
        session.user.isEmployee = true;
        session.user.ownerId = token.ownerId as string;
        session.user.employeeId = token.employeeId as string;
      }

      return session;
    },

    // Callback untuk menambahkan logika tambahan setelah autentikasi berhasil
    async signIn({ user, account }) {
      // For existing users with credentials provider, verify their email if they sign in with Google
      if (account?.provider === "google" && user.email) {
        const existingUser = await database.user.findUnique({
          where: { email: user.email },
        });

        // If user exists but was created with credentials and email isn't verified
        if (
          existingUser &&
          existingUser.provider === "credentials" &&
          !existingUser.emailVerified
        ) {
          try {
            await database.user.update({
              where: { id: existingUser.id },
              data: {
                emailVerified: new Date(),
              },
            });
          } catch (error) {
            console.error("Error updating email verification status:", error);
          }
        }
      }
      return true;
    },
  },
});
