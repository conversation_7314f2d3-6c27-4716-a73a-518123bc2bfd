"use server";

import { db } from "./prisma";
import bcrypt from "bcryptjs";

export async function debugUserByEmail(email: string) {
  try {
    console.log("🔍 DEBUG: Looking up user by email:", email);

    const user = await db.user.findUnique({
      where: { email },
      select: {
        id: true,
        name: true,
        email: true,
        emailVerified: true,
        password: true,
        role: true,
        provider: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!user) {
      console.log("❌ DEBUG: No user found with email:", email);
      return { success: false, message: "User not found" };
    }

    console.log("✅ DEBUG: User found:", {
      id: user.id,
      name: user.name,
      email: user.email,
      emailVerified: user.emailVerified,
      hasPassword: !!user.password,
      passwordLength: user.password?.length,
      role: user.role,
      provider: user.provider,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    });

    return {
      success: true,
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        emailVerified: user.emailVerified,
        hasPassword: !!user.password,
        passwordLength: user.password?.length,
        role: user.role,
        provider: user.provider,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      },
    };
  } catch (error) {
    console.error("❌ DEBUG: Error looking up user:", error);
    return { success: false, message: "Database error", error };
  }
}

export async function debugPasswordCheck(email: string, password: string) {
  try {
    console.log("🔑 DEBUG: Checking password for email:", email);

    const user = await db.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        password: true,
      },
    });

    if (!user || !user.password) {
      console.log("❌ DEBUG: User not found or no password");
      return { success: false, message: "User not found or no password" };
    }

    const isValid = await bcrypt.compare(password, user.password);
    console.log("🔑 DEBUG: Password comparison result:", isValid);

    return {
      success: true,
      passwordValid: isValid,
      hasPassword: !!user.password,
    };
  } catch (error) {
    console.error("❌ DEBUG: Error checking password:", error);
    return { success: false, message: "Password check error", error };
  }
}

export async function debugAllUsers() {
  try {
    console.log("👥 DEBUG: Fetching all users");

    const users = await db.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        emailVerified: true,
        role: true,
        provider: true,
        createdAt: true,
      },
      take: 10, // Limit to first 10 users
    });

    console.log("👥 DEBUG: Found users:", users.length);
    users.forEach((user, index) => {
      console.log(`User ${index + 1}:`, {
        id: user.id,
        name: user.name,
        email: user.email,
        emailVerified: user.emailVerified,
        role: user.role,
        provider: user.provider,
        createdAt: user.createdAt,
      });
    });

    return { success: true, users, count: users.length };
  } catch (error) {
    console.error("❌ DEBUG: Error fetching users:", error);
    return { success: false, message: "Database error", error };
  }
}
