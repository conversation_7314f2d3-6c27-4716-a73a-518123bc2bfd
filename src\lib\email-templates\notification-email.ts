interface NotificationEmailProps {
  title: string;
  message: string;
  type: "info" | "warning" | "success" | "error";
  appName?: string;
  recipientName?: string;
}

export function generateNotificationEmail({
  title,
  message,
  type,
  appName = "KivaPOS",
  recipientName = "Pengguna",
}: NotificationEmailProps): string {
  // Define colors based on notification type
  const colors = {
    info: {
      primary: "#3b82f6", // blue-500
      background: "#eff6ff", // blue-50
      border: "#bfdbfe", // blue-200
    },
    warning: {
      primary: "#f59e0b", // amber-500
      background: "#fffbeb", // amber-50
      border: "#fde68a", // amber-200
    },
    success: {
      primary: "#10b981", // emerald-500
      background: "#ecfdf5", // emerald-50
      border: "#a7f3d0", // emerald-200
    },
    error: {
      primary: "#ef4444", // red-500
      background: "#fef2f2", // red-50
      border: "#fecaca", // red-200
    },
  };

  const typeColor = colors[type];

  // Get icon based on notification type
  const getIcon = () => {
    switch (type) {
      case "info":
        return `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="${typeColor.primary}" width="24" height="24">
                  <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm8.706-1.442c1.146-.573 2.437.463 2.126 1.706l-.709 2.836.042-.02a.75.75 0 01.67 1.34l-.04.022c-1.147.573-2.438-.463-2.127-1.706l.71-2.836-.042.02a.75.75 0 11-.671-1.34l.041-.022zM12 9a.75.75 0 100-********* 0 000 1.5z" clip-rule="evenodd" />
                </svg>`;
      case "warning":
        return `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="${typeColor.primary}" width="24" height="24">
                  <path fill-rule="evenodd" d="M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z" clip-rule="evenodd" />
                </svg>`;
      case "success":
        return `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="${typeColor.primary}" width="24" height="24">
                  <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z" clip-rule="evenodd" />
                </svg>`;
      case "error":
        return `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="${typeColor.primary}" width="24" height="24">
                  <path fill-rule="evenodd" d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25zm-1.72 6.97a.75.75 0 10-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 101.06 1.06L12 13.06l1.72 1.72a.75.75 0 101.06-1.06L13.06 12l1.72-1.72a.75.75 0 10-1.06-1.06L12 10.94l-1.72-1.72z" clip-rule="evenodd" />
                </svg>`;
      default:
        return "";
    }
  };

  return `
    <!DOCTYPE html>
    <html lang="id">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${title}</title>
      <style>
        body {
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          line-height: 1.6;
          color: #333;
          margin: 0;
          padding: 0;
          background-color: #f9fafb;
        }
        .container {
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
        }
        .header {
          text-align: center;
          padding: 20px 0;
        }
        .logo {
          font-size: 24px;
          font-weight: bold;
          color: #333;
        }
        .notification-card {
          background-color: #ffffff;
          border-radius: 8px;
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
          margin: 20px 0;
          overflow: hidden;
        }
        .notification-header {
          background-color: ${typeColor.background};
          border-bottom: 1px solid ${typeColor.border};
          padding: 15px 20px;
          display: flex;
          align-items: center;
        }
        .notification-icon {
          margin-right: 12px;
        }
        .notification-title {
          font-size: 18px;
          font-weight: 600;
          color: #111827;
          margin: 0;
        }
        .notification-body {
          padding: 20px;
        }
        .notification-message {
          margin: 0 0 15px 0;
          color: #4b5563;
        }
        .footer {
          text-align: center;
          padding: 20px 0;
          font-size: 12px;
          color: #6b7280;
        }
        .button {
          display: inline-block;
          background-color: ${typeColor.primary};
          color: white;
          text-decoration: none;
          padding: 10px 20px;
          border-radius: 4px;
          font-weight: 500;
          margin-top: 15px;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="logo">${appName}</div>
        </div>
        
        <div class="notification-card">
          <div class="notification-header">
            <div class="notification-icon">
              ${getIcon()}
            </div>
            <h1 class="notification-title">${title}</h1>
          </div>
          
          <div class="notification-body">
            <p class="notification-message">Halo ${recipientName},</p>
            <p class="notification-message">${message}</p>
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/dashboard/notifications" class="button">Lihat Notifikasi</a>
          </div>
        </div>
        
        <div class="footer">
          <p>© ${new Date().getFullYear()} ${appName}. Semua hak dilindungi.</p>
          <p>Email ini dikirim sesuai dengan pengaturan notifikasi Anda.</p>
        </div>
      </div>
    </body>
    </html>
  `;
}
