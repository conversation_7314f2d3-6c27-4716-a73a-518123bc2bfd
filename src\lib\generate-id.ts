/**
 * Utility functions for generating custom IDs
 */

import { db } from "@/lib/prisma";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";

/**
 * Generates a custom product ID with the format PRD-{idCompany}-{year}-{sequentialNumber}
 * where {idCompany} is the company ID of the user (e.g., IP000001),
 * {year} is the current year (e.g., 2025), and
 * {sequentialNumber} is a sequential 5-digit number starting from 00001 for each company per year
 *
 * This function looks at the last existing ID to determine the next sequential number
 *
 * @param userId The ID of the user creating the product
 * @param tx Optional transaction context for bulk operations
 * @returns A custom product ID string (e.g., PRD-IP000001-2025-00001)
 */
export async function generateProductId(
  userId: string,
  tx?: Parameters<Parameters<typeof db.$transaction>[0]>[0]
): Promise<string> {
  // Get the company ID for the user
  const dbContext = tx || db;
  const user = await dbContext.user.findUnique({
    where: { id: userId },
    select: {
      businessInfo: {
        select: {
          companyId: true,
        },
      },
    },
  });

  if (!user?.businessInfo?.companyId) {
    throw new Error("User does not have a company ID");
  }

  const companyId = user.businessInfo.companyId;
  const currentYear = new Date().getFullYear();
  const maxRetries = 50; // Allow for more retries during bulk import
  let attempt = 0;

  while (attempt < maxRetries) {
    try {
      // Get the last product for this company and year to find the highest sequential number
      const lastProduct = await dbContext.product.findFirst({
        where: {
          userId,
          id: {
            startsWith: `PRD-${companyId}-${currentYear}-`,
          },
        },
        select: { id: true },
        orderBy: {
          id: "desc", // Order by ID descending to get the highest sequential number
        },
      });

      let maxSequentialNumber = 0; // Start from 0

      // Extract sequential number from the last product ID
      if (lastProduct) {
        // Format: PRD-IP000001-2025-000001
        const idParts = lastProduct.id.split("-");
        if (idParts.length >= 4) {
          const sequentialPart = idParts[3]; // The last part should be the sequential number
          const sequentialNumber = parseInt(sequentialPart, 10);
          if (!isNaN(sequentialNumber)) {
            maxSequentialNumber = sequentialNumber;
          }
        }
      }

      // Calculate the next sequential number
      const nextSequentialNumber = maxSequentialNumber + 1 + attempt;

      // Format the number as a 6-digit string with leading zeros (matching your format: PRD-IP000001-2025-000001)
      const formattedNumber = String(nextSequentialNumber).padStart(6, "0");

      // Combine to create the product ID with company ID and year
      const newId = `PRD-${companyId}-${currentYear}-${formattedNumber}`;

      // Check if this ID already exists
      const existingProduct = await dbContext.product.findUnique({
        where: { id: newId },
        select: { id: true },
      });

      if (!existingProduct) {
        return newId;
      }

      // If ID exists, increment attempt and try again
      attempt++;
    } catch (error) {
      console.error("Error generating product ID:", error);
      attempt++;
    }
  }

  // If we've exhausted all attempts, use a timestamp-based approach as fallback
  const timestamp = Date.now().toString().slice(-6); // Use 6 digits to match format
  return `PRD-${companyId}-${currentYear}-${timestamp}`;
}

/**
 * Generates multiple product IDs in bulk for import operations
 * This is more efficient than generating IDs one by one and reduces race conditions
 *
 * @param userId The ID of the user creating the products
 * @param count Number of IDs to generate
 * @param tx Optional transaction context for bulk operations
 * @returns Array of unique product ID strings
 */
export async function generateBulkProductIds(
  userId: string,
  count: number,
  tx?: Parameters<Parameters<typeof db.$transaction>[0]>[0]
): Promise<string[]> {
  if (count <= 0) return [];

  // Get the company ID for the user
  const dbContext = tx || db;
  const user = await dbContext.user.findUnique({
    where: { id: userId },
    select: {
      businessInfo: {
        select: {
          companyId: true,
        },
      },
    },
  });

  if (!user?.businessInfo?.companyId) {
    throw new Error("User does not have a company ID");
  }

  const companyId = user.businessInfo.companyId;
  const currentYear = new Date().getFullYear();

  // Get the last product for this company and year to find the highest sequential number
  const lastProduct = await dbContext.product.findFirst({
    where: {
      userId,
      id: {
        startsWith: `PRD-${companyId}-${currentYear}-`,
      },
    },
    select: { id: true },
    orderBy: {
      id: "desc", // Order by ID descending to get the highest sequential number
    },
  });

  let startingSequentialNumber = 1; // Default start from 1

  // Extract sequential number from the last product ID
  if (lastProduct) {
    // Format: PRD-IP000001-2025-000001
    const idParts = lastProduct.id.split("-");
    if (idParts.length >= 4) {
      const sequentialPart = idParts[3]; // The last part should be the sequential number
      const sequentialNumber = parseInt(sequentialPart, 10);
      if (!isNaN(sequentialNumber)) {
        startingSequentialNumber = sequentialNumber + 1;
      }
    }
  }

  // Generate the required number of IDs
  const productIds: string[] = [];
  for (let i = 0; i < count; i++) {
    const sequentialNumber = startingSequentialNumber + i;
    const formattedNumber = String(sequentialNumber).padStart(6, "0");
    const newId = `PRD-${companyId}-${currentYear}-${formattedNumber}`;
    productIds.push(newId);
  }

  console.log(
    `Generated ${count} product IDs starting from ${startingSequentialNumber} for company ${companyId}`
  );
  console.log(
    `First ID: ${productIds[0]}, Last ID: ${productIds[productIds.length - 1]}`
  );

  return productIds;
}

/**
 * Generates product IDs with concurrent safety using database-level atomic operations
 * This function handles multiple users importing simultaneously by using database locks
 * and retry mechanisms to prevent ID collisions
 *
 * @param userId The ID of the user creating the products
 * @param count Number of IDs to generate
 * @param tx Transaction context (required for atomic operations)
 * @returns Array of unique product ID strings
 */
export async function generateConcurrentSafeProductIds(
  userId: string,
  count: number,
  tx: Parameters<Parameters<typeof db.$transaction>[0]>[0]
): Promise<string[]> {
  if (count <= 0) return [];

  // Get the company ID for the user
  const user = await tx.user.findUnique({
    where: { id: userId },
    select: {
      businessInfo: {
        select: {
          companyId: true,
        },
      },
    },
  });

  if (!user?.businessInfo?.companyId) {
    throw new Error("User does not have a company ID");
  }

  const companyId = user.businessInfo.companyId;
  const currentYear = new Date().getFullYear();
  const maxRetries = 3; // Reduce retries to avoid timeout
  let attempt = 0;

  while (attempt < maxRetries) {
    try {
      // Use a simpler, faster query without FOR UPDATE for bulk operations
      // FOR UPDATE can cause deadlocks in high concurrency scenarios
      const lastProduct = await tx.product.findFirst({
        where: {
          userId,
          id: {
            startsWith: `PRD-${companyId}-${currentYear}-`,
          },
        },
        select: { id: true },
        orderBy: { id: "desc" },
      });

      let startingSequentialNumber = 1; // Default start from 1

      // Extract sequential number from the last product ID
      if (lastProduct) {
        // Format: PRD-IP000001-2025-000001
        const idParts = lastProduct.id.split("-");
        if (idParts.length >= 4) {
          const sequentialPart = idParts[3]; // The last part should be the sequential number
          const sequentialNumber = parseInt(sequentialPart, 10);
          if (!isNaN(sequentialNumber)) {
            startingSequentialNumber = sequentialNumber + 1;
          }
        }
      }

      // Generate the required number of IDs
      const productIds: string[] = [];
      for (let i = 0; i < count; i++) {
        const sequentialNumber = startingSequentialNumber + i;
        const formattedNumber = String(sequentialNumber).padStart(6, "0");
        const newId = `PRD-${companyId}-${currentYear}-${formattedNumber}`;
        productIds.push(newId);
      }

      // For bulk operations, skip the existence check to improve performance
      // The batch processing will handle any conflicts
      console.log(
        `[CONCURRENT-SAFE] Generated ${count} product IDs starting from ${startingSequentialNumber} for company ${companyId}`
      );
      console.log(
        `[CONCURRENT-SAFE] First ID: ${productIds[0]}, Last ID: ${productIds[productIds.length - 1]}`
      );
      return productIds;
    } catch (error) {
      console.error(
        `[CONCURRENT-SAFE] Error on attempt ${attempt + 1}:`,
        error
      );
      attempt++;

      // Reduce backoff time to avoid timeout
      if (attempt < maxRetries) {
        await new Promise((resolve) => setTimeout(resolve, 100 * attempt));
      }
    }
  }

  // If all retries failed, fall back to timestamp-based IDs
  console.warn(
    `[CONCURRENT-SAFE] All retries failed, using timestamp-based fallback`
  );
  const productIds: string[] = [];
  for (let i = 0; i < count; i++) {
    const timestamp = Date.now().toString().slice(-6);
    const randomSuffix = Math.floor(Math.random() * 1000)
      .toString()
      .padStart(3, "0");
    const fallbackId =
      `PRD-${companyId}-${currentYear}-${timestamp}${randomSuffix}`.slice(
        0,
        25
      );
    productIds.push(fallbackId);

    // Small delay to ensure different timestamps
    await new Promise((resolve) => setTimeout(resolve, 1));
  }

  return productIds;
}

/**
 * Generates a custom purchase ID with the format PUR-{idCompany}-{year}-{sequentialNumber}
 * where {idCompany} is the company ID of the user,
 * {year} is the current year (e.g., 2024), and
 * {sequentialNumber} is a sequential 6-digit number starting from 000001 for each company per year
 *
 * @param userId The ID of the user creating the purchase
 * @param tx Optional transaction context for concurrent operations
 * @returns A custom purchase ID string
 */
export async function generatePurchaseId(
  userId: string,
  tx?: Parameters<Parameters<typeof db.$transaction>[0]>[0]
): Promise<string> {
  // Get the company ID for the user
  const dbContext = tx || db;
  const user = await dbContext.user.findUnique({
    where: { id: userId },
    select: {
      businessInfo: {
        select: {
          companyId: true,
        },
      },
    },
  });

  if (!user?.businessInfo?.companyId) {
    throw new Error("User does not have a company ID");
  }

  const companyId = user.businessInfo.companyId;
  const currentYear = new Date().getFullYear();
  const maxRetries = 10;
  let attempt = 0;

  while (attempt < maxRetries) {
    try {
      // Get the last purchase for this company and year to find the highest sequential number
      const lastPurchase = await dbContext.purchase.findFirst({
        where: {
          userId,
          id: {
            startsWith: `PUR-${companyId}-${currentYear}-`,
          },
        },
        select: { id: true },
        orderBy: {
          id: "desc", // Order by ID descending to get the highest sequential number
        },
      });

      let maxSequentialNumber = 0;
      if (lastPurchase?.id) {
        // Extract the sequential number from the ID (e.g., PUR-IP000001-2024-000005 -> 5)
        const match = lastPurchase.id.match(
          new RegExp(`PUR-${companyId}-${currentYear}-(\\d+)$`)
        );
        if (match && match[1]) {
          maxSequentialNumber = parseInt(match[1], 10);
        }
      }

      // Calculate the next sequential number
      const nextSequentialNumber = maxSequentialNumber + 1 + attempt;

      // Format the number as a 6-digit string with leading zeros
      const formattedNumber = String(nextSequentialNumber).padStart(6, "0");

      // Combine to create the purchase ID with company ID and year
      const newId = `PUR-${companyId}-${currentYear}-${formattedNumber}`;

      // Check if this ID already exists
      const existingPurchase = await dbContext.purchase.findUnique({
        where: { id: newId },
        select: { id: true },
      });

      if (!existingPurchase) {
        return newId;
      }

      // If ID exists, increment attempt and try again
      attempt++;
    } catch (error) {
      console.error("Error generating purchase ID:", error);
      attempt++;
    }
  }

  // If we've exhausted all attempts, use a timestamp-based approach as fallback
  const timestamp = Date.now().toString().slice(-6);
  const randomSuffix = Math.floor(Math.random() * 1000)
    .toString()
    .padStart(3, "0");
  return `PUR-${companyId}-${currentYear}-${timestamp}${randomSuffix}`.slice(
    0,
    25
  );
}

/**
 * Generates a custom sales ID with the format SAL-{idCompany}-{year}-{sequentialNumber}
 * where {idCompany} is the company ID of the user,
 * {year} is the current year (e.g., 2024), and
 * {sequentialNumber} is a sequential 6-digit number starting from 000001 for each company per year
 *
 * @param userId The ID of the user creating the sale
 * @param tx Optional transaction context for concurrent operations
 * @returns A custom sales ID string
 */
export async function generateSalesId(
  userId: string,
  tx?: Parameters<Parameters<typeof db.$transaction>[0]>[0]
): Promise<string> {
  // Get the company ID for the user
  const dbContext = tx || db;
  const user = await dbContext.user.findUnique({
    where: { id: userId },
    select: {
      businessInfo: {
        select: {
          companyId: true,
        },
      },
    },
  });

  if (!user?.businessInfo?.companyId) {
    throw new Error("User does not have a company ID");
  }

  const companyId = user.businessInfo.companyId;
  const currentYear = new Date().getFullYear();
  const maxRetries = 10;
  let attempt = 0;

  while (attempt < maxRetries) {
    try {
      // Get the last sale for this company and year to find the highest sequential number
      const lastSale = await dbContext.sale.findFirst({
        where: {
          userId,
          id: {
            startsWith: `SAL-${companyId}-${currentYear}-`,
          },
        },
        select: { id: true },
        orderBy: {
          id: "desc", // Order by ID descending to get the highest sequential number
        },
      });

      let maxSequentialNumber = 0;
      if (lastSale?.id) {
        // Extract the sequential number from the ID (e.g., SAL-IP000001-2024-000005 -> 5)
        const match = lastSale.id.match(
          new RegExp(`SAL-${companyId}-${currentYear}-(\\d+)$`)
        );
        if (match && match[1]) {
          maxSequentialNumber = parseInt(match[1], 10);
        }
      }

      // Calculate the next sequential number
      const nextSequentialNumber = maxSequentialNumber + 1 + attempt;

      // Format the number as a 6-digit string with leading zeros
      const formattedNumber = String(nextSequentialNumber).padStart(6, "0");

      // Combine to create the sales ID with company ID and year
      const newId = `SAL-${companyId}-${currentYear}-${formattedNumber}`;

      // Check if this ID already exists
      const existingSale = await dbContext.sale.findUnique({
        where: { id: newId },
        select: { id: true },
      });

      if (!existingSale) {
        return newId;
      }

      // If ID exists, increment attempt and try again
      attempt++;
    } catch (error) {
      console.error("Error generating sales ID:", error);
      attempt++;
    }
  }

  // If we've exhausted all attempts, use a timestamp-based approach as fallback
  const timestamp = Date.now().toString().slice(-6);
  const randomSuffix = Math.floor(Math.random() * 1000)
    .toString()
    .padStart(3, "0");
  return `SAL-${companyId}-${currentYear}-${timestamp}${randomSuffix}`.slice(
    0,
    25
  );
}

/**
 * Generate a unique customer ID in the format CUS-000001
 * @returns Promise<string> - The generated customer ID
 */
export async function generateCustomerId(): Promise<string> {
  // Get the effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    throw new Error("User not authenticated");
  }

  const maxRetries = 10;
  let attempt = 0;

  while (attempt < maxRetries) {
    try {
      // Get the count of existing customers for this user
      const customerCount = await db.customer.count({
        where: {
          userId: effectiveUserId,
        },
      });

      // Calculate the next number (add 1 to the current count plus attempt number for uniqueness)
      const nextNumber = customerCount + 1 + attempt;

      // Format the number as a 6-digit string with leading zeros
      const formattedNumber = String(nextNumber).padStart(6, "0");

      // Combine to create the customer ID
      const newId = `CUS-${formattedNumber}`;

      // Check if this ID already exists
      const existingCustomer = await db.customer.findUnique({
        where: { id: newId },
        select: { id: true },
      });

      if (!existingCustomer) {
        return newId;
      }

      // If ID exists, increment attempt and try again
      attempt++;
    } catch (error) {
      console.error("Error generating customer ID:", error);
      throw new Error("Failed to generate customer ID");
    }
  }

  // If we've exhausted all attempts, use a timestamp-based approach
  const timestamp = Date.now().toString().slice(-6);
  const randomSuffix = Math.floor(Math.random() * 1000)
    .toString()
    .padStart(3, "0");
  return `CUS-${timestamp}${randomSuffix}`.slice(0, 10); // Ensure it's still 10 characters total
}

/**
 * Generate a unique supplier ID in the format SUP-000001
 * @returns Promise<string> - The generated supplier ID
 */
export async function generateSupplierId(): Promise<string> {
  // Get the effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    throw new Error("User not authenticated");
  }

  const maxRetries = 10;
  let attempt = 0;

  while (attempt < maxRetries) {
    try {
      // Get the count of existing suppliers for this user
      const supplierCount = await db.supplier.count({
        where: {
          userId: effectiveUserId,
        },
      });

      // Calculate the next number (add 1 to the current count plus attempt number for uniqueness)
      const nextNumber = supplierCount + 1 + attempt;

      // Format the number as a 6-digit string with leading zeros
      const formattedNumber = String(nextNumber).padStart(6, "0");

      // Combine to create the supplier ID
      const newId = `SUP-${formattedNumber}`;

      // Check if this ID already exists
      const existingSupplier = await db.supplier.findUnique({
        where: { id: newId },
        select: { id: true },
      });

      if (!existingSupplier) {
        return newId;
      }

      // If ID exists, increment attempt and try again
      attempt++;
    } catch (error) {
      console.error("Error generating supplier ID:", error);
      throw new Error("Failed to generate supplier ID");
    }
  }

  // If we've exhausted all attempts, use a timestamp-based approach
  const timestamp = Date.now().toString().slice(-6);
  const randomSuffix = Math.floor(Math.random() * 1000)
    .toString()
    .padStart(3, "0");
  return `SUP-${timestamp}${randomSuffix}`.slice(0, 10); // Ensure it's still 10 characters total
}

/**
 * Generates a custom service ID with the format SRV-{idCompany}-{year}-{sequentialNumber}
 * where {idCompany} is the company ID of the user,
 * {year} is the current year (e.g., 2024), and
 * {sequentialNumber} is a sequential 6-digit number starting from 000001 for each company per year
 *
 * @param userId The ID of the user creating the service
 * @param tx Optional transaction context for concurrent operations
 * @returns A custom service ID string
 */
export async function generateServiceId(
  userId: string,
  tx?: Parameters<Parameters<typeof db.$transaction>[0]>[0]
): Promise<string> {
  // Get the company ID for the user
  const dbContext = tx || db;
  const user = await dbContext.user.findUnique({
    where: { id: userId },
    select: {
      businessInfo: {
        select: {
          companyId: true,
        },
      },
    },
  });

  if (!user?.businessInfo?.companyId) {
    throw new Error("User does not have a company ID");
  }

  const companyId = user.businessInfo.companyId;
  const currentYear = new Date().getFullYear();
  const maxRetries = 10;
  let attempt = 0;

  while (attempt < maxRetries) {
    try {
      // Get the last service for this company and year to find the highest sequential number
      const lastService = await dbContext.service.findFirst({
        where: {
          userId,
          id: {
            startsWith: `SRV-${companyId}-${currentYear}-`,
          },
        },
        select: { id: true },
        orderBy: {
          id: "desc", // Order by ID descending to get the highest sequential number
        },
      });

      let maxSequentialNumber = 0;
      if (lastService?.id) {
        // Extract the sequential number from the ID (e.g., SRV-IP000001-2024-000005 -> 5)
        const match = lastService.id.match(
          new RegExp(`SRV-${companyId}-${currentYear}-(\\d+)$`)
        );
        if (match && match[1]) {
          maxSequentialNumber = parseInt(match[1], 10);
        }
      }

      // Calculate the next sequential number
      const nextSequentialNumber = maxSequentialNumber + 1 + attempt;

      // Format the number as a 6-digit string with leading zeros
      const formattedNumber = String(nextSequentialNumber).padStart(6, "0");

      // Combine to create the service ID with company ID and year
      const newId = `SRV-${companyId}-${currentYear}-${formattedNumber}`;

      // Check if this ID already exists
      const existingService = await dbContext.service.findUnique({
        where: { id: newId },
        select: { id: true },
      });

      if (!existingService) {
        return newId;
      }

      // If ID exists, increment attempt and try again
      attempt++;
    } catch (error) {
      console.error("Error generating service ID:", error);
      attempt++;
    }
  }

  // If we've exhausted all attempts, use a timestamp-based approach as fallback
  const timestamp = Date.now().toString().slice(-6);
  const randomSuffix = Math.floor(Math.random() * 1000)
    .toString()
    .padStart(3, "0");
  return `SRV-${companyId}-${currentYear}-${timestamp}${randomSuffix}`.slice(
    0,
    25
  );
}
