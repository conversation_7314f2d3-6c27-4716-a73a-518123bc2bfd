"use server";

import { db } from "@/lib/prisma";
import { getEffectiveUserId } from "./get-effective-user-id";

/**
 * Fetches products for the current user or their owner if they're an employee
 * @param options Optional query options
 * @returns Array of serialized products
 */
export async function getProducts(options?: {
  includeOutOfStock?: boolean;
  orderBy?: "name" | "createdAt" | "price" | "stock";
  orderDirection?: "asc" | "desc";
  limit?: number;
  needsApproval?: boolean;
  lowStock?: boolean;
  categoryId?: string;
  excludeDrafts?: boolean;
}) {
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    console.error("User ID not found in session on protected route.");
    return [];
  }

  // Build the query
  const where: any = {
    userId: effectiveUserId,
  };

  // Handle stock filtering
  if (!options?.includeOutOfStock) {
    where.stock = { gt: 0 };
  }

  // Handle low stock filtering (products with stock <= 5)
  if (options?.lowStock) {
    where.stock = { gt: 0, lte: 5 };
  }

  // Handle needs approval filtering (placeholder - you might need to add a field to the schema)
  if (options?.needsApproval) {
    // This is a placeholder. In a real implementation, you would have a field like 'needsApproval'
    // where.needsApproval = true;
  }

  // Filter by category if provided
  if (options?.categoryId) {
    where.categoryId = options.categoryId;
  }

  // Exclude draft products if requested
  if (options?.excludeDrafts) {
    where.isDraft = false;
  }

  // Set up ordering
  const orderField = options?.orderBy || "createdAt";
  const orderDirection = options?.orderDirection || "desc";

  // Execute the query
  const products = await db.product.findMany({
    where,
    orderBy: {
      [orderField]: orderDirection,
    },
    include: {
      category: true, // Include category information
      variants: true, // Include variants information
    },
    ...(options?.limit ? { take: options.limit } : {}),
  });

  // Serialize the products for client components
  return products.map((product) => {
    // Process variants if they exist
    const processedVariants = product.variants
      ? product.variants.map((variant) => ({
          ...variant,
          price: variant.price ? variant.price.toNumber() : null,
        }))
      : [];

    return {
      ...product,
      price: product.price.toNumber(),
      wholesalePrice: product.wholesalePrice
        ? product.wholesalePrice.toNumber()
        : null,
      cost: product.cost ? product.cost.toNumber() : null,
      barcode: product.barcode, // Explicitly include barcode field
      // Individual tax rates for each price type
      salePriceTaxRate: product.salePriceTaxRate
        ? product.salePriceTaxRate.toNumber()
        : null,
      wholesalePriceTaxRate: product.wholesalePriceTaxRate
        ? product.wholesalePriceTaxRate.toNumber()
        : null,
      costPriceTaxRate: product.costPriceTaxRate
        ? product.costPriceTaxRate.toNumber()
        : null,
      // Format individual dimension fields
      length: product.length ? product.length.toNumber() : null,
      width: product.width ? product.width.toNumber() : null,
      height: product.height ? product.height.toNumber() : null,
      unit: product.unit || "Pcs", // Include unit field with default
      // Create a dimensions object for the shipping tab
      dimensions: {
        length: product.length ? product.length.toNumber() : 0,
        width: product.width ? product.width.toNumber() : 0,
        height: product.height ? product.height.toNumber() : 0,
      },
      // Include processed variants
      variants: processedVariants,
    };
  });
}

/**
 * Fetches a single product by ID
 * @param id Product ID
 * @returns Serialized product or null if not found
 */
export async function getProductById(id: string) {
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    console.error("User ID not found in session on protected route.");
    return null;
  }

  // First try to find by ID with case-insensitive match
  let product = null;

  // Check if the ID starts with "prd-" or "PRD-" (case-insensitive)
  if (id.toLowerCase().startsWith("prd-")) {
    // Try to find by ID with case-insensitive match
    product = await db.product.findFirst({
      where: {
        id: {
          mode: "insensitive",
          equals: id,
        },
        userId: effectiveUserId,
      },
      include: {
        category: true,
        variants: true, // Include variants
      },
    });
  }

  // If not found, try to find by exact ID (for non-PRD IDs or as fallback)
  if (!product) {
    product = await db.product.findUnique({
      where: {
        id,
        userId: effectiveUserId,
      },
      include: {
        category: true,
        variants: true, // Include variants
      },
    });
  }

  if (!product) {
    return null;
  }

  // Process variants if they exist
  const processedVariants = product.variants
    ? product.variants.map((variant) => ({
        ...variant,
        price: variant.price ? variant.price.toNumber() : null,
      }))
    : [];

  return {
    ...product,
    price: product.price.toNumber(),
    wholesalePrice: product.wholesalePrice
      ? product.wholesalePrice.toNumber()
      : null,
    cost: product.cost ? product.cost.toNumber() : null,
    barcode: product.barcode, // Explicitly include barcode field
    // Individual tax rates for each price type
    salePriceTaxRate: product.salePriceTaxRate
      ? product.salePriceTaxRate.toNumber()
      : null,
    wholesalePriceTaxRate: product.wholesalePriceTaxRate
      ? product.wholesalePriceTaxRate.toNumber()
      : null,
    costPriceTaxRate: product.costPriceTaxRate
      ? product.costPriceTaxRate.toNumber()
      : null,
    // Format individual dimension fields
    length: product.length ? product.length.toNumber() : null,
    width: product.width ? product.width.toNumber() : null,
    height: product.height ? product.height.toNumber() : null,
    unit: product.unit || "Pcs", // Include unit field with default
    // Create a dimensions object for the shipping tab
    dimensions: {
      length: product.length ? product.length.toNumber() : 0,
      width: product.width ? product.width.toNumber() : 0,
      height: product.height ? product.height.toNumber() : 0,
    },
    // Include processed variants
    variants: processedVariants,
  };
}

/**
 * Gets product counts by stock status
 * @returns Object with counts for available, low, and out of stock products
 */
export async function getProductStockCounts() {
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    console.error("User ID not found in session on protected route.");
    return { available: 0, low: 0, outOfStock: 0, needsApproval: 0, drafts: 0 };
  }

  // Count products with stock > 5 (available)
  const availableCount = await db.product.count({
    where: {
      userId: effectiveUserId,
      stock: { gt: 5 },
    },
  });

  // Count products with 0 < stock <= 5 (low stock)
  const lowStockCount = await db.product.count({
    where: {
      userId: effectiveUserId,
      stock: { gt: 0, lte: 5 },
    },
  });

  // Count products with stock = 0 (out of stock)
  const outOfStockCount = await db.product.count({
    where: {
      userId: effectiveUserId,
      stock: 0,
    },
  });

  // For needs approval, this is a placeholder
  // In a real implementation, you would have a field like 'needsApproval'
  const needsApprovalCount = 0;

  // Count draft products
  const draftsCount = await db.product.count({
    where: {
      userId: effectiveUserId,
      isDraft: true,
    },
  });

  return {
    available: availableCount,
    low: lowStockCount,
    outOfStock: outOfStockCount,
    needsApproval: needsApprovalCount,
    drafts: draftsCount,
  };
}
