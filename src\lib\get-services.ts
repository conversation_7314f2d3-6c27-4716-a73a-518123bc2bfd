"use server";

import { db } from "@/lib/prisma";
import { getEffectiveUserId } from "./get-effective-user-id";
import {
  ServiceStatus as PrismaServiceStatus,
  DeviceType as PrismaDeviceType,
} from "@prisma/client";
import {
  Service,
  ServiceCounts,
  ServiceStatus,
  DeviceType,
} from "@/components/pages/dashboard/services/types";

// Helper function to get user name by ID
async function getUserNameById(userId: string): Promise<string> {
  try {
    // First try to find as a User
    const user = await db.user.findUnique({
      where: { id: userId },
      select: { name: true },
    });

    if (user?.name) {
      return user.name;
    }

    // If not found as User, try to find as Employee
    const employee = await db.employee.findUnique({
      where: { id: userId },
      select: { name: true },
    });

    if (employee?.name) {
      return employee.name;
    }

    // If neither found, return the ID
    return userId;
  } catch (error) {
    console.error("Error fetching user name:", error);
    return userId;
  }
}

/**
 * Maps Prisma DeviceType to frontend DeviceType
 */
function mapPrismaDeviceType(deviceType: PrismaDeviceType): DeviceType {
  switch (deviceType) {
    case PrismaDeviceType.LAPTOP:
      return DeviceType.LAPTOP;
    case PrismaDeviceType.DESKTOP:
      return DeviceType.DESKTOP;
    case PrismaDeviceType.PHONE:
      return DeviceType.PHONE;
    case PrismaDeviceType.TABLET:
      return DeviceType.TABLET;
    case PrismaDeviceType.PRINTER:
      return DeviceType.PRINTER;
    case PrismaDeviceType.OTHER:
    default:
      return DeviceType.OTHER;
  }
}

/**
 * Maps Prisma ServiceStatus to frontend ServiceStatus
 */
function mapPrismaServiceStatus(status: PrismaServiceStatus): ServiceStatus {
  switch (status) {
    case PrismaServiceStatus.DITERIMA:
      return ServiceStatus.DITERIMA;
    case PrismaServiceStatus.PROSES_MENUNGGU_SPAREPART:
      return ServiceStatus.PROSES_MENUNGGU_SPAREPART;
    case PrismaServiceStatus.SELESAI_BELUM_DIAMBIL:
      return ServiceStatus.SELESAI_BELUM_DIAMBIL;
    case PrismaServiceStatus.SELESAI_SUDAH_DIAMBIL:
      return ServiceStatus.SELESAI_SUDAH_DIAMBIL;
    default:
      return ServiceStatus.DITERIMA;
  }
}

/**
 * Fetches services for the current user or their owner if they're an employee
 * @returns Array of services or empty array if not authenticated
 */
export async function getServices(effectiveUserId: string): Promise<Service[]> {
  try {
    const dbServices = await db.service.findMany({
      where: {
        userId: effectiveUserId,
      },
      include: {
        serviceHistory: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Convert database model to frontend Service type
    const services: Service[] = dbServices.map((service) => ({
      id: service.id,
      serviceNumber: service.serviceNumber,
      customerName: service.customerName,
      customerPhone: service.customerPhone,
      customerEmail: service.customerEmail || undefined,
      deviceType: mapPrismaDeviceType(service.deviceType),
      deviceBrand: service.deviceBrand,
      deviceModel: service.deviceModel,
      deviceSerialNumber: service.deviceSerialNumber || undefined,
      problemDescription: service.problemDescription,
      diagnosisNotes: service.diagnosisNotes || undefined,
      repairNotes: service.repairNotes || undefined,
      estimatedCost: service.estimatedCost
        ? service.estimatedCost.toNumber()
        : undefined,
      finalCost: service.finalCost ? service.finalCost.toNumber() : undefined,
      warrantyPeriod: service.warrantyPeriod || 0,
      status: mapPrismaServiceStatus(service.status),
      receivedDate: service.receivedDate.toISOString(),
      estimatedCompletionDate: service.estimatedCompletionDate
        ? service.estimatedCompletionDate.toISOString()
        : undefined,
      completedDate: service.completedDate
        ? service.completedDate.toISOString()
        : undefined,
      deliveredDate: service.deliveredDate
        ? service.deliveredDate.toISOString()
        : undefined,
      createdAt: service.createdAt.toISOString(),
      updatedAt: service.updatedAt.toISOString(),
      userId: service.userId,
      customerId: service.customerId || undefined,
      isDraft: service.isDraft,
      lampiran: (service.lampiran as { url: string; filename: string }[]) || [],
      serviceHistory: service.serviceHistory.map((history) => ({
        id: history.id,
        status: mapPrismaServiceStatus(history.status),
        notes: history.notes || undefined,
        changedAt: history.changedAt.toISOString(),
        changedBy: history.changedBy,
        serviceId: history.serviceId,
      })),
    }));

    return services;
  } catch (error) {
    console.error("Error fetching services:", error);
    return [];
  }
}

/**
 * Calculates service counts by status
 * @param services Array of services
 * @returns Object with counts for each status
 */
export async function calculateServiceCounts(
  services: Service[]
): Promise<ServiceCounts> {
  return {
    diterima: services.filter((s) => s.status === ServiceStatus.DITERIMA)
      .length,
    prosesMenungguSparepart: services.filter(
      (s) => s.status === ServiceStatus.PROSES_MENUNGGU_SPAREPART
    ).length,
    selesaiBelumDiambil: services.filter(
      (s) => s.status === ServiceStatus.SELESAI_BELUM_DIAMBIL
    ).length,
    selesaiSudahDiambil: services.filter(
      (s) => s.status === ServiceStatus.SELESAI_SUDAH_DIAMBIL
    ).length,
    total: services.length,
    drafts: services.filter((s) => s.isDraft).length,
  };
}

/**
 * Fetches a single service by ID
 * @param id Service ID
 * @returns Service object or null if not found
 */
export async function getServiceById(
  id: string,
  effectiveUserId: string
): Promise<Service | null> {
  try {
    const service = await db.service.findUnique({
      where: {
        id,
        userId: effectiveUserId,
      },
      include: {
        serviceHistory: true,
        spareParts: true,
      },
    });

    if (!service) {
      return null;
    }

    // Fetch customer data if customerId exists
    let customerAddress: string | undefined = undefined;
    if (service.customerId) {
      const customer = await db.customer.findUnique({
        where: {
          id: service.customerId,
          userId: effectiveUserId,
        },
        select: {
          address: true,
        },
      });
      customerAddress = customer?.address || undefined;
    }

    // Resolve user names for service history
    const serviceHistoryWithNames = await Promise.all(
      service.serviceHistory.map(async (history) => ({
        id: history.id,
        status: mapPrismaServiceStatus(history.status),
        notes: history.notes || undefined,
        changedAt: history.changedAt.toISOString(),
        changedBy: await getUserNameById(history.changedBy),
        serviceId: history.serviceId,
      }))
    );

    // Debug logging
    console.log("Raw service lampiran from DB:", service.lampiran);
    console.log(
      "Processed lampiran:",
      (service.lampiran as { url: string; filename: string }[]) || []
    );

    // Convert database model to frontend Service type
    return {
      id: service.id,
      serviceNumber: service.serviceNumber,
      customerName: service.customerName,
      customerPhone: service.customerPhone,
      customerEmail: service.customerEmail || undefined,
      customerAddress: customerAddress,
      deviceType: mapPrismaDeviceType(service.deviceType),
      deviceBrand: service.deviceBrand,
      deviceModel: service.deviceModel,
      deviceSerialNumber: service.deviceSerialNumber || undefined,
      problemDescription: service.problemDescription,
      diagnosisNotes: service.diagnosisNotes || undefined,
      repairNotes: service.repairNotes || undefined,
      estimatedCost: service.estimatedCost
        ? service.estimatedCost.toNumber()
        : undefined,
      finalCost: service.finalCost ? service.finalCost.toNumber() : undefined,
      warrantyPeriod: service.warrantyPeriod || 0,
      status: mapPrismaServiceStatus(service.status),
      receivedDate: service.receivedDate.toISOString(),
      estimatedCompletionDate: service.estimatedCompletionDate
        ? service.estimatedCompletionDate.toISOString()
        : undefined,
      completedDate: service.completedDate
        ? service.completedDate.toISOString()
        : undefined,
      deliveredDate: service.deliveredDate
        ? service.deliveredDate.toISOString()
        : undefined,
      createdAt: service.createdAt.toISOString(),
      updatedAt: service.updatedAt.toISOString(),
      userId: service.userId,
      customerId: service.customerId || undefined,
      isDraft: service.isDraft,
      lampiran: (service.lampiran as { url: string; filename: string }[]) || [],
      serviceHistory: serviceHistoryWithNames,
    };
  } catch (error) {
    console.error("Error fetching service:", error);
    return null;
  }
}

/**
 * Fetches a single service by service number
 * @param serviceNumber Service number (e.g., SRV-25S000001)
 * @param effectiveUserId User ID
 * @returns Service object or null if not found
 */
export async function getServiceByServiceNumber(
  serviceNumber: string,
  effectiveUserId: string
): Promise<Service | null> {
  try {
    const service = await db.service.findFirst({
      where: {
        serviceNumber,
        userId: effectiveUserId,
      },
      include: {
        serviceHistory: true,
      },
    });

    if (!service) {
      return null;
    }

    // Fetch customer data if customerId exists
    let customerAddress: string | undefined = undefined;
    if (service.customerId) {
      const customer = await db.customer.findUnique({
        where: {
          id: service.customerId,
          userId: effectiveUserId,
        },
        select: {
          address: true,
        },
      });
      customerAddress = customer?.address || undefined;
    }

    // Resolve user names for service history
    const serviceHistoryWithNames = await Promise.all(
      service.serviceHistory.map(async (history) => ({
        id: history.id,
        status: mapPrismaServiceStatus(history.status),
        notes: history.notes || undefined,
        changedAt: history.changedAt.toISOString(),
        changedBy: await getUserNameById(history.changedBy),
        serviceId: history.serviceId,
      }))
    );

    // Convert database model to frontend Service type
    return {
      id: service.id,
      serviceNumber: service.serviceNumber,
      customerName: service.customerName,
      customerPhone: service.customerPhone,
      customerEmail: service.customerEmail || undefined,
      customerAddress: customerAddress,
      deviceType: mapPrismaDeviceType(service.deviceType),
      deviceBrand: service.deviceBrand,
      deviceModel: service.deviceModel,
      deviceSerialNumber: service.deviceSerialNumber || undefined,
      problemDescription: service.problemDescription,
      diagnosisNotes: service.diagnosisNotes || undefined,
      repairNotes: service.repairNotes || undefined,
      estimatedCost: service.estimatedCost
        ? service.estimatedCost.toNumber()
        : undefined,
      finalCost: service.finalCost ? service.finalCost.toNumber() : undefined,
      warrantyPeriod: service.warrantyPeriod || 0,
      status: mapPrismaServiceStatus(service.status),
      receivedDate: service.receivedDate.toISOString(),
      estimatedCompletionDate: service.estimatedCompletionDate
        ? service.estimatedCompletionDate.toISOString()
        : undefined,
      completedDate: service.completedDate
        ? service.completedDate.toISOString()
        : undefined,
      deliveredDate: service.deliveredDate
        ? service.deliveredDate.toISOString()
        : undefined,
      createdAt: service.createdAt.toISOString(),
      updatedAt: service.updatedAt.toISOString(),
      userId: service.userId,
      customerId: service.customerId || undefined,
      isDraft: service.isDraft,
      lampiran: (service.lampiran as { url: string; filename: string }[]) || [],
      serviceHistory: serviceHistoryWithNames,
    };
  } catch (error) {
    console.error("Error fetching service by service number:", error);
    return null;
  }
}
