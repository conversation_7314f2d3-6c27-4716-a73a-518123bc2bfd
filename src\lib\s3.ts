import {
  S3Client,
  PutObjectCommand,
  DeleteObjectCommand,
  GetObjectCommand,
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";

// Initialize S3 client
const s3Client = new S3Client({
  region: process.env.AWS_REGION || "us-east-1",
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || "",
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || "",
  },
});

const BUCKET_NAME = process.env.AWS_S3_BUCKET_NAME || "";

// Upload a file to AWS S3
export async function uploadToS3(
  file: File,
  userId: string,
  folder: string = ""
) {
  console.log(
    `[S3] Starting upload - File: ${file.name}, Size: ${file.size} bytes, User: ${userId}, Folder: ${folder}`
  );

  try {
    // Validate file size (10MB limit)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      console.log(`[S3] File too large: ${file.size} bytes (max: ${maxSize})`);
      return { error: "File size exceeds 10MB limit", success: false };
    }

    // Create a unique filename with user ID prefix for organization
    const timestamp = Date.now();
    const sanitizedFileName = file.name.replace(/\s+/g, "-");
    const key = folder
      ? `${folder}/${userId}/${timestamp}-${sanitizedFileName}`
      : `${userId}/${timestamp}-${sanitizedFileName}`;

    console.log(`[S3] Generated key: ${key}`);
    console.log(
      `[S3] Bucket: ${BUCKET_NAME}, Region: ${process.env.AWS_REGION}`
    );

    // Convert File to ArrayBuffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    console.log(`[S3] Converted to buffer, size: ${buffer.length} bytes`);

    // Upload the file to S3 (removed ACL to avoid AccessControlListNotSupported error)
    const command = new PutObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key,
      Body: buffer,
      ContentType: file.type,
      // Removed ACL - bucket should be configured for public access via bucket policy
    });

    console.log(`[S3] Sending upload command...`);
    await s3Client.send(command);
    console.log(`[S3] Upload successful`);

    // Generate the public URL
    const url = `https://${BUCKET_NAME}.s3.${process.env.AWS_REGION || "us-east-1"}.amazonaws.com/${key}`;
    console.log(`[S3] Generated URL: ${url}`);

    return { url, success: true };
  } catch (error) {
    console.error("[S3] Error uploading to S3:", error);
    return {
      error: `Failed to upload file: ${error instanceof Error ? error.message : "Unknown error"}`,
      success: false,
    };
  }
}

// Delete a file from AWS S3
export async function deleteFromS3(url: string) {
  console.log(`[S3] Starting delete - URL: ${url}`);

  try {
    // Extract the key from the URL
    const urlParts = url.split("/");
    const bucketIndex = urlParts.findIndex((part) => part.includes(".s3."));
    if (bucketIndex === -1) {
      console.log(`[S3] Invalid S3 URL format: ${url}`);
      throw new Error("Invalid S3 URL format");
    }

    const key = urlParts.slice(bucketIndex + 1).join("/");
    console.log(`[S3] Extracted key: ${key}`);

    const command = new DeleteObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key,
    });

    console.log(`[S3] Sending delete command...`);
    await s3Client.send(command);
    console.log(`[S3] Delete successful`);
    return { success: true };
  } catch (error) {
    console.error("[S3] Error deleting from S3:", error);
    return {
      error: `Failed to delete file: ${error instanceof Error ? error.message : "Unknown error"}`,
      success: false,
    };
  }
}

// Generate a presigned URL for temporary access (optional, for private files)
export async function generatePresignedUrl(
  key: string,
  expiresIn: number = 3600
) {
  try {
    const command = new GetObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key,
    });

    const url = await getSignedUrl(s3Client, command, { expiresIn });
    return { url, success: true };
  } catch (error) {
    console.error("Error generating presigned URL:", error);
    return { error: "Failed to generate presigned URL", success: false };
  }
}

// Helper function to extract S3 key from URL
export function extractS3KeyFromUrl(url: string): string | null {
  try {
    const urlParts = url.split("/");
    const bucketIndex = urlParts.findIndex((part) => part.includes(".s3."));
    if (bucketIndex === -1) {
      return null;
    }
    return urlParts.slice(bucketIndex + 1).join("/");
  } catch (error) {
    console.error("Error extracting S3 key from URL:", error);
    return null;
  }
}
