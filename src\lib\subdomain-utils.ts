import { headers } from "next/headers";

/**
 * Utility functions for handling subdomain routing
 */

export async function getHostname(): Promise<string> {
  const headersList = await headers();
  return headersList.get("host") || "";
}

export async function isDashboardSubdomain(
  hostname?: string
): Promise<boolean> {
  const host = hostname || (await getHostname());
  return (
    host === "dashboard.kivapos.com" ||
    host === "dashboard.localhost:3000" ||
    host === "dashboard.localhost"
  );
}

export async function isMainDomain(hostname?: string): Promise<boolean> {
  const host = hostname || (await getHostname());
  return (
    host === "kivapos.com" || host === "localhost:3000" || host === "localhost"
  );
}

export async function getDashboardUrl(
  path: string = "/summaries"
): Promise<string> {
  const hostname = await getHostname();
  const isDevelopment = hostname.includes("localhost");

  const protocol = isDevelopment ? "http" : "https";
  const dashboardHost = isDevelopment
    ? "dashboard.localhost:3000"
    : "dashboard.kivapos.com";

  // Ensure path starts with /
  const cleanPath = path.startsWith("/") ? path : `/${path}`;

  return `${protocol}://${dashboardHost}${cleanPath}`;
}

export async function getMainDomainUrl(path: string = "/"): Promise<string> {
  const hostname = await getHostname();
  const isDevelopment = hostname.includes("localhost");

  const protocol = isDevelopment ? "http" : "https";
  const mainHost = isDevelopment ? "localhost:3000" : "kivapos.com";

  // Ensure path starts with /
  const cleanPath = path.startsWith("/") ? path : `/${path}`;

  return `${protocol}://${mainHost}${cleanPath}`;
}

/**
 * Parse dashboard route from slug array
 */
export function parseDashboardRoute(slug?: string[]): string {
  if (!slug || slug.length === 0) {
    return "summaries";
  }
  return slug.join("/");
}

/**
 * Check if a route is a valid dashboard route
 */
export function isValidDashboardRoute(route: string): boolean {
  const validRoutes = [
    "summaries",
    "settings",
    "settings/business",
    "settings/profile",
    "settings/security",
    "settings/notifications",
    "settings/billing",
    "settings/plans",
    "settings/appearance",
    "settings/redirections",
    "settings/support",
    "settings/employees",
    "products",
    "sales",
    "purchases",
    "customers",
    "suppliers",
    "reports",
    "notifications",
    "activity",
    "cashier",
    "employees",
    "contacts",
    "event-discounts",
    "welcome",
    "profile",
  ];

  return validRoutes.includes(route);
}

/**
 * Get page title for dashboard routes
 */
export function getDashboardPageTitle(route: string): string {
  const titles: Record<string, string> = {
    summaries: "Dashboard",
    settings: "Settings",
    "settings/business": "Business Settings",
    "settings/profile": "Profile Settings",
    "settings/security": "Security Settings",
    "settings/notifications": "Notification Settings",
    "settings/billing": "Billing Settings",
    "settings/plans": "Plans Settings",
    "settings/appearance": "Appearance Settings",
    "settings/redirections": "Redirections Settings",
    "settings/support": "Support Settings",
    "settings/employees": "Employee Settings",
    products: "Products",
    sales: "Sales",
    purchases: "Purchases",
    customers: "Customers",
    suppliers: "Suppliers",
    reports: "Reports",
    notifications: "Notifications",
    activity: "Activity",
    cashier: "Cashier",
    employees: "Employees",
    contacts: "Contacts",
    "event-discounts": "Event Discounts",
    welcome: "Welcome",
    profile: "Profile",
  };

  return titles[route] || "Dashboard";
}
