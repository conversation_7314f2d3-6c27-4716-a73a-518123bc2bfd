import { randomBytes } from "crypto";
import { db as database } from "./prisma";

// Security improvement: Custom error class
class TokenError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "TokenError";
  }
}

// function untuk generate token verifikasi email
export const generateVerificationToken = async (email: string) => {
  try {
    const token = randomBytes(64).toString("hex"); // Increased token size for better security
    const expires = new Date(Date.now() + 1800000); // 30 minutes - shorter expiry for security

    // Delete token lama jika ada
    await database.verificationToken.deleteMany({
      where: { email },
    });

    const lowerCaseEmail = email.toLowerCase();
    // Membuat token baru
    await database.verificationToken.create({
      data: {
        email: lowerCaseEmail,
        token,
        expires,
      },
    });

    return token; // Return raw token for email
  } catch (error) {
    throw new TokenError("Gagal membuat token verifikasi");
  }
};
