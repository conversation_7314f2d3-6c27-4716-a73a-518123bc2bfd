import { create } from "zustand";
import {
  getAllActivities,
  ActivityItem,
  ActivityType,
  ActivityFilters,
} from "@/actions/activity/activity";
import { UserActivity } from "@/actions/users/activity";

const ITEMS_PER_PAGE = 10;

interface ActivityState {
  activities: (ActivityItem | UserActivity)[];
  loading: boolean;
  error: string | null;
  totalCount: number;
  currentPage: number;
  activeTab: ActivityType | "all";
  dateRange: {
    startDate: string;
    endDate: string;
  };
  employeeOnly: boolean;
  fetchActivities: () => Promise<void>;
  setPage: (page: number) => void;
  setTab: (tab: ActivityType | "all") => void;
  setDateRange: (field: "startDate" | "endDate", value: string) => void;
  setEmployeeOnly: (employeeOnly: boolean) => void;
  clearFilters: () => void;
  refresh: () => void;
}

export const useActivityStore = create<ActivityState>((set, get) => ({
  activities: [],
  loading: true,
  error: null,
  totalCount: 0,
  currentPage: 1,
  activeTab: "all",
  dateRange: {
    startDate: "",
    endDate: "",
  },
  employeeOnly: false,

  fetchActivities: async () => {
    set({ loading: true, error: null });
    try {
      const { currentPage, activeTab, dateRange, employeeOnly } = get();
      const offset = (currentPage - 1) * ITEMS_PER_PAGE;

      const filters: ActivityFilters = {
        type: activeTab !== "all" ? activeTab : undefined,
        startDate: dateRange.startDate
          ? new Date(dateRange.startDate)
          : undefined,
        endDate: dateRange.endDate ? new Date(dateRange.endDate) : undefined,
        employeeOnly: employeeOnly || undefined,
      };

      const result = await getAllActivities({
        limit: ITEMS_PER_PAGE,
        offset,
        type: filters.type,
        startDate: filters.startDate,
        endDate: filters.endDate,
      });

      if (result.success && result.data) {
        set({
          activities: result.data,
          totalCount: result.totalCount || 0,
        });
      } else {
        set({ error: result.error || "Gagal mengambil aktivitas" });
      }
    } catch (err) {
      set({ error: "Terjadi kesalahan saat mengambil aktivitas" });
      console.error(err);
    } finally {
      set({ loading: false });
    }
  },

  setPage: (page: number) => {
    set({ currentPage: page });
    get().fetchActivities();
  },

  setTab: (tab: ActivityType | "all") => {
    set({ activeTab: tab, currentPage: 1 });
    get().fetchActivities();
  },

  setDateRange: (field: "startDate" | "endDate", value: string) => {
    set((state) => ({
      dateRange: { ...state.dateRange, [field]: value },
      currentPage: 1,
    }));
    get().fetchActivities();
  },

  setEmployeeOnly: (employeeOnly: boolean) => {
    set({ employeeOnly, currentPage: 1 });
    get().fetchActivities();
  },

  clearFilters: () => {
    set({
      activeTab: "all",
      dateRange: { startDate: "", endDate: "" },
      employeeOnly: false,
      currentPage: 1,
    });
    get().fetchActivities();
  },

  refresh: () => {
    get().fetchActivities();
  },
}));
