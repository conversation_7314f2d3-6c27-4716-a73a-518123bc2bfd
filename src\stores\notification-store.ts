import { create } from "zustand";
import {
  getFilteredNotifications,
  markAllNotificationsAsRead,
  markNotificationAsRead,
  NotificationItem,
  NotificationType,
  NotificationFilters,
} from "@/actions/notifications/notifications";
import { toast } from "sonner";

const ITEMS_PER_PAGE = 10;

interface NotificationState {
  notifications: NotificationItem[];
  loading: boolean;
  error: string | null;
  totalCount: number;
  currentPage: number;
  activeTab: NotificationType | "all";
  dateRange: {
    startDate: string;
    endDate: string;
  };
  readStatus: "all" | "read" | "unread";
  showUnreadOnly: boolean;
  fetchNotifications: () => Promise<void>;
  setPage: (page: number) => void;
  setTab: (tab: NotificationType | "all") => void;
  setDateRange: (field: "startDate" | "endDate", value: string) => void;
  setReadStatus: (status: "all" | "read" | "unread") => void;
  setShowUnreadOnly: (show: boolean) => void;
  clearFilters: () => void;
  markAsRead: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  refresh: () => void;
}

export const useNotificationStore = create<NotificationState>((set, get) => ({
  notifications: [],
  loading: true,
  error: null,
  totalCount: 0,
  currentPage: 1,
  activeTab: "all",
  dateRange: {
    startDate: "",
    endDate: "",
  },
  readStatus: "all",
  showUnreadOnly: false,

  fetchNotifications: async () => {
    set({ loading: true, error: null });
    try {
      const { currentPage, activeTab, dateRange, readStatus, showUnreadOnly } =
        get();
      const offset = (currentPage - 1) * ITEMS_PER_PAGE;

      const filters: NotificationFilters = {
        type: activeTab !== "all" ? activeTab : undefined,
        startDate: dateRange.startDate
          ? new Date(dateRange.startDate)
          : undefined,
        endDate: dateRange.endDate ? new Date(dateRange.endDate) : undefined,
        readStatus: showUnreadOnly ? "unread" : readStatus,
      };

      const result = await getFilteredNotifications({
        limit: ITEMS_PER_PAGE,
        offset,
        filters,
      });

      if (result.success && result.data) {
        set({
          notifications: result.data,
          totalCount: result.totalCount || 0,
        });
      } else {
        set({ error: result.error || "Gagal mengambil notifikasi" });
      }
    } catch (err) {
      set({ error: "Terjadi kesalahan saat mengambil notifikasi" });
      console.error(err);
    } finally {
      set({ loading: false });
    }
  },

  setPage: (page: number) => {
    set({ currentPage: page });
    get().fetchNotifications();
  },

  setTab: (tab: NotificationType | "all") => {
    set({ activeTab: tab, currentPage: 1 });
    get().fetchNotifications();
  },

  setDateRange: (field: "startDate" | "endDate", value: string) => {
    set((state) => ({
      dateRange: { ...state.dateRange, [field]: value },
      currentPage: 1,
    }));
    get().fetchNotifications();
  },

  setReadStatus: (status: "all" | "read" | "unread") => {
    set({ readStatus: status, currentPage: 1 });
    get().fetchNotifications();
  },

  setShowUnreadOnly: (show: boolean) => {
    set({ showUnreadOnly: show, currentPage: 1 });
    get().fetchNotifications();
  },

  clearFilters: () => {
    set({
      activeTab: "all",
      dateRange: { startDate: "", endDate: "" },
      readStatus: "all",
      showUnreadOnly: false,
      currentPage: 1,
    });
    get().fetchNotifications();
  },

  markAsRead: async (id: string) => {
    try {
      const result = await markNotificationAsRead(id);
      if (result.success) {
        set((state) => ({
          notifications: state.notifications.map((n) =>
            n.id === id ? { ...n, isRead: true } : n
          ),
        }));
        toast.success("Notifikasi ditandai sebagai telah dibaca");
      } else {
        toast.error(
          result.error || "Gagal menandai notifikasi sebagai telah dibaca"
        );
      }
    } catch (err) {
      toast.error("Terjadi kesalahan saat menandai notifikasi");
      console.error(err);
    }
  },

  markAllAsRead: async () => {
    try {
      const result = await markAllNotificationsAsRead();
      if (result.success) {
        set((state) => ({
          notifications: state.notifications.map((n) => ({
            ...n,
            isRead: true,
          })),
        }));
        toast.success("Semua notifikasi telah ditandai sebagai dibaca");
        const { readStatus, showUnreadOnly } = get();
        if (readStatus === "unread" || showUnreadOnly) {
          get().fetchNotifications();
        }
      } else {
        toast.error(
          result.error || "Gagal menandai semua notifikasi sebagai telah dibaca"
        );
      }
    } catch (err) {
      toast.error("Terjadi kesalahan saat menandai semua notifikasi");
      console.error(err);
    }
  },

  refresh: () => {
    get().fetchNotifications();
  },
}));
