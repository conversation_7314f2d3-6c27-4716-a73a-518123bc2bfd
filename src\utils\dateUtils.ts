/**
 * Utility functions for date handling in export components
 */

/**
 * Convert a Date object to local date string in YYYY-MM-DD format
 * This prevents timezone issues when using toISOString()
 */
export function formatDateForFilename(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

/**
 * Check if a date is in the future
 */
export function isFutureDate(date: Date): boolean {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const checkDate = new Date(date);
  checkDate.setHours(0, 0, 0, 0);
  return checkDate > today;
}

/**
 * Check if a month/year combination is in the future
 */
export function isFutureMonth(year: number, month: number): boolean {
  const today = new Date();
  const currentYear = today.getFullYear();
  const currentMonth = today.getMonth();
  
  if (year > currentYear) return true;
  if (year === currentYear && month > currentMonth) return true;
  return false;
}

/**
 * Check if a year is in the future
 */
export function isFutureYear(year: number): boolean {
  const currentYear = new Date().getFullYear();
  return year > currentYear;
}

/**
 * Generate date string for filename based on report type and config
 */
export function generateDateStringForFilename(
  reportType: "harian" | "bulanan" | "tahunan",
  config: {
    selectedDate: Date;
    selectedMonth: number;
    selectedYear: number;
  }
): string {
  switch (reportType) {
    case "harian":
      return formatDateForFilename(config.selectedDate);
    case "bulanan":
      const monthNames = [
        "01", "02", "03", "04", "05", "06",
        "07", "08", "09", "10", "11", "12"
      ];
      return `${config.selectedYear}-${monthNames[config.selectedMonth]}`;
    case "tahunan":
      return `${config.selectedYear}`;
    default:
      return formatDateForFilename(new Date());
  }
}

/**
 * Validate export date/period and return error message if invalid
 */
export function validateExportPeriod(
  reportType: "harian" | "bulanan" | "tahunan",
  config: {
    selectedDate: Date;
    selectedMonth: number;
    selectedYear: number;
  }
): string | null {
  switch (reportType) {
    case "harian":
      if (isFutureDate(config.selectedDate)) {
        return "Tidak dapat mengekspor data untuk tanggal yang akan datang";
      }
      break;
    case "bulanan":
      if (isFutureMonth(config.selectedYear, config.selectedMonth)) {
        return "Tidak dapat mengekspor data untuk bulan yang akan datang";
      }
      break;
    case "tahunan":
      if (isFutureYear(config.selectedYear)) {
        return "Tidak dapat mengekspor data untuk tahun yang akan datang";
      }
      break;
  }
  return null;
}
