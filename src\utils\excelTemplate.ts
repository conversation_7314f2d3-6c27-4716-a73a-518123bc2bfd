// Professional Excel template system for generating well-formatted reports

import * as XLSX from "xlsx-js-style";
import {
  CELL_STYLES,
  NUMBER_FORMATS,
  SHEET_HEADER_STYLES,
  ExcelCellStyle, // Added ExcelCellStyle import
  BRAND_COLORS,
} from "./excelStyles";

// Interface for the main report data structure
export interface ReportData {
  sales?: any[];
  purchases?: any[];
  products?: any[];
  customers?: any[];
  suppliers?: any[];
  incomeStatement?: any[];
  services?: any[];
  spareParts?: any[];
  summary?: {
    totalSales?: number;
    totalPurchases?: number;
    totalProducts?: number;
    totalCustomers?: number;
    totalSuppliers?: number;
    period?: string;
    generatedAt?: Date;
  };
  filters?: {
    dateRange?: string;
    startDate?: Date;
    endDate?: Date;
    category?: string;
    supplier?: string;
    customer?: string;
    status?: string;
  };
  reportType?: "harian" | "bulanan" | "tahunan";
}

// Interface for Excel generation options
export interface ExcelTemplateOptions {
  companyName?: string;
  reportTitle?: string;
  includeCharts?: boolean;
  includeSummary?: boolean;
  autoFitColumns?: boolean;
}

// --- UTILITY FUNCTIONS ---

const applyCellStyle = (ws: XLSX.WorkSheet, ref: string, style: any) => {
  if (!ws[ref]) ws[ref] = { t: "s", v: "" };
  ws[ref].s = style;
};

const mergeCells = (ws: XLSX.WorkSheet, range: string) => {
  if (!ws["!merges"]) ws["!merges"] = [];
  ws["!merges"].push(XLSX.utils.decode_range(range));
};

const setColumnWidths = (ws: XLSX.WorkSheet, widths: { wch: number }[]) => {
  ws["!cols"] = widths;
};

const setRowHeights = (
  ws: XLSX.WorkSheet,
  heights: { [row: number]: number }
) => {
  if (!ws["!rows"]) ws["!rows"] = [];
  Object.entries(heights).forEach(([row, hpt]) => {
    const rowIndex = parseInt(row) - 1;
    if (!ws["!rows"]![rowIndex]) ws["!rows"]![rowIndex] = {};
    ws["!rows"]![rowIndex].hpt = hpt;
  });
};

const addAutoFilter = (ws: XLSX.WorkSheet, range: string) => {
  ws["!autofilter"] = { ref: range };
};

const freezePanes = (ws: XLSX.WorkSheet, cell: string) => {
  ws["!view"] = {
    state: "frozen",
    xSplit: 0,
    ySplit: XLSX.utils.decode_cell(cell).r,
    topLeftCell: cell,
  };
};

const formatDate = (date: string | Date): string => {
  try {
    return new Date(date).toLocaleDateString("id-ID");
  } catch (error) {
    return String(date);
  }
};

const getSheetHeaderStyle = (sheetName: string) => {
  const type = sheetName.toLowerCase();
  if (type.includes("penjualan")) return SHEET_HEADER_STYLES.sales;
  if (type.includes("pembelian")) return SHEET_HEADER_STYLES.purchases;
  if (type.includes("produk")) return SHEET_HEADER_STYLES.products;
  if (type.includes("pelanggan")) return SHEET_HEADER_STYLES.customers;
  if (type.includes("supplier")) return SHEET_HEADER_STYLES.suppliers;
  return CELL_STYLES.tableHeader;
};

const getNestedValue = (obj: any, path: string): any => {
  return path.split(".").reduce((acc, part) => acc && acc[part], obj);
};

// --- SHEET CREATION FUNCTIONS ---

/**
 * Creates the main summary sheet for the report.
 */
const createSummarySheet = (
  data: ReportData,
  options: ExcelTemplateOptions
): XLSX.WorkSheet => {
  const companyName = options.companyName || "KivaPOS";
  const reportTitle = options.reportTitle || "Laporan Keuangan";
  const wsData = [
    [companyName],
    ["Sistem Manajemen Kasir Profesional"],
    [null],
    [reportTitle],
    [null],
    ["Informasi Laporan"],
    [
      "Jenis Laporan:",
      data.reportType
        ? data.reportType.charAt(0).toUpperCase() + data.reportType.slice(1)
        : "Kustom",
    ],
    ["Periode:", data.summary?.period || "Tidak ditentukan"],
    ["Tanggal Export:", new Date().toLocaleDateString("id-ID")],
    [null],
    ["Filter yang Diterapkan"],
    ["Rentang Tanggal:", data.filters?.dateRange || "Semua"],
    [
      "Tanggal Mulai:",
      data.filters?.startDate ? formatDate(data.filters.startDate) : "N/A",
    ],
    [
      "Tanggal Akhir:",
      data.filters?.endDate ? formatDate(data.filters.endDate) : "N/A",
    ],
    ["Kategori:", data.filters?.category || "Semua"],
    [null],
    ["Ringkasan Data"],
    [
      "Total Penjualan:",
      {
        t: "n",
        v: data.summary?.totalSales || 0,
        s: { ...CELL_STYLES.summary, numFmt: NUMBER_FORMATS.currency },
      },
    ],
    [
      "Total Pembelian:",
      {
        t: "n",
        v: data.summary?.totalPurchases || 0,
        s: { ...CELL_STYLES.summary, numFmt: NUMBER_FORMATS.currency },
      },
    ],
    [
      "Total Produk:",
      {
        t: "n",
        v: data.summary?.totalProducts || 0,
        s: { ...CELL_STYLES.summary, numFmt: NUMBER_FORMATS.integer },
      },
    ],
  ];
  const worksheet = XLSX.utils.aoa_to_sheet(wsData);

  // Styling and Merging
  applyCellStyle(worksheet, "A1", CELL_STYLES.reportTitle);
  mergeCells(worksheet, "A1:B1");
  applyCellStyle(worksheet, "A2", CELL_STYLES.info);
  mergeCells(worksheet, "A2:B2");
  applyCellStyle(worksheet, "A4", CELL_STYLES.sectionHeader);
  mergeCells(worksheet, "A4:B4");

  const sections = [6, 11, 17];
  sections.forEach((row) => {
    applyCellStyle(worksheet, `A${row}`, CELL_STYLES.sectionHeader);
    mergeCells(worksheet, `A${row}:B${row}`);
  });

  for (let r = 7; r <= 15; r++) {
    if (worksheet[`A${r}`])
      applyCellStyle(worksheet, `A${r}`, CELL_STYLES.info);
    if (worksheet[`B${r}`])
      applyCellStyle(worksheet, `B${r}`, CELL_STYLES.tableDataEven);
  }
  for (let r = 18; r <= 20; r++) {
    if (worksheet[`A${r}`])
      applyCellStyle(worksheet, `A${r}`, CELL_STYLES.info);
  }

  setColumnWidths(worksheet, [{ wch: 25 }, { wch: 30 }]);
  setRowHeights(worksheet, { 1: 30, 4: 25 });
  return worksheet;
};

/**
 * Creates a well-formatted data sheet with titles, headers, and data.
 */
const createDataSheet = (
  data: any[],
  sheetTitle: string,
  columns: {
    key: string;
    label: string;
    type?: "currency" | "date" | "number" | "text";
  }[],
  reportPeriod: string
): XLSX.WorkSheet => {
  const headerRowCount = 4; // Rows for Title, Subtitle, Spacer, and Headers
  const headers = columns.map((col) => col.label);

  const worksheet = XLSX.utils.aoa_to_sheet([[]]); // Start with an empty sheet

  // 1. Add Sheet Title & Subtitle
  XLSX.utils.sheet_add_aoa(
    worksheet,
    [[sheetTitle], [`Periode: ${reportPeriod}`]],
    { origin: "A1" }
  );
  mergeCells(worksheet, `A1:${XLSX.utils.encode_col(columns.length - 1)}1`);
  applyCellStyle(worksheet, "A1", CELL_STYLES.sheetTitle);
  mergeCells(worksheet, `A2:${XLSX.utils.encode_col(columns.length - 1)}2`);
  applyCellStyle(worksheet, "A2", CELL_STYLES.sheetSubtitle);

  // 2. Add Table Headers
  XLSX.utils.sheet_add_aoa(worksheet, [headers], {
    origin: { r: headerRowCount - 1, c: 0 },
  });
  const headerStyle = getSheetHeaderStyle(sheetTitle);
  columns.forEach((_, index) => {
    applyCellStyle(
      worksheet,
      XLSX.utils.encode_cell({ r: headerRowCount - 1, c: index }),
      headerStyle
    );
  });

  // 3. Process and Add Data Rows
  const rows = data.map((item) =>
    columns.map((col) => {
      let value = getNestedValue(item, col.key);
      if (col.key === "items.length" && Array.isArray(item.items))
        value = item.items.length;
      if (col.key === "customer.name") value = item.customer?.name || "Umum";
      if (value === null || value === undefined) return "";

      switch (col.type) {
        case "currency":
          return typeof value === "number" ? value : 0;
        case "date":
          let dateValue: Date | null = null;
          if (value instanceof Date) {
            dateValue = value;
          } else if (typeof value === "string") {
            try {
              dateValue = new Date(value);
              if (isNaN(dateValue.getTime())) {
                dateValue = null; // Invalid date string
              }
            } catch (e) {
              dateValue = null; // Parsing error
            }
          }

          return dateValue || ""; // Return Date object or empty string
        case "number":
          return typeof value === "number" ? value : 0;
        default:
          return String(value);
      }
    })
  );
  XLSX.utils.sheet_add_aoa(worksheet, rows, {
    origin: { r: headerRowCount, c: 0 },
  });

  // 4. Style Data Rows
  rows.forEach((_, rowIndex) => {
    const actualRow = rowIndex + headerRowCount;
    const isEven = rowIndex % 2 === 0;
    columns.forEach((col, colIndex) => {
      const cellRef = XLSX.utils.encode_cell({ r: actualRow, c: colIndex });
      let style: ExcelCellStyle = JSON.parse(
        JSON.stringify(
          isEven ? CELL_STYLES.tableDataEven : CELL_STYLES.tableDataOdd
        )
      );

      if (col.type === "currency") {
        style.numFmt = NUMBER_FORMATS.currency;
        if (style.alignment) style.alignment.horizontal = "right";
      } else if (col.type === "date") {
        style.numFmt = NUMBER_FORMATS.date;
        if (style.alignment) style.alignment.horizontal = "center";
      } else if (col.type === "number") {
        style.numFmt = NUMBER_FORMATS.integer;
        if (style.alignment) style.alignment.horizontal = "right";
      } else if (col.key === "description") {
        if (style.alignment) {
          style.alignment.vertical = "top";
          style.alignment.wrapText = true;
        }
      }
      applyCellStyle(worksheet, cellRef, style);
    });
  });

  // 5. Calculate Column Widths
  const colWidths = columns.map((col, index) => {
    const headerLength = col.label.length;
    const maxDataLength =
      data.length > 0
        ? Math.max(...rows.map((row) => String(row[index] || "").length))
        : 0;
    let width = Math.max(headerLength + 5, maxDataLength + 3, 15);
    if (col.key === "description") width = 50;
    else if (width > 60) width = 60;
    return { wch: width };
  });
  setColumnWidths(worksheet, colWidths);

  // 6. Set Row Heights
  const rowHeights: { [key: number]: number } = {
    1: 22,
    2: 18,
    [headerRowCount]: 30,
  };
  if (columns.some((c) => c.key === "description")) {
    rows.forEach((_, i) => {
      rowHeights[headerRowCount + i + 1] = 40;
    });
  }
  setRowHeights(worksheet, rowHeights);

  // 7. Add Interactive Features
  freezePanes(worksheet, `A${headerRowCount + 1}`);
  if (data.length > 0) {
    addAutoFilter(
      worksheet,
      `A${headerRowCount}:${XLSX.utils.encode_col(columns.length - 1)}${data.length + headerRowCount}`
    );
  }

  return worksheet;
};

/**
 * Adds a clean 'Total' row at the bottom of a data sheet.
 */
const addTotalRowToDataSheet = (
  worksheet: XLSX.WorkSheet,
  data: any[],
  columns: { key: string; label: string; type?: string }[]
) => {
  if (!data || data.length === 0) return;

  const headerRowCount = 4;
  const totalRowIndex = data.length + headerRowCount;

  const totalRow = columns.map((col) => {
    if (col.type === "currency" || col.type === "number") {
      return data.reduce(
        (sum, item) => sum + (getNestedValue(item, col.key) || 0),
        0
      );
    }
    return null;
  });
  totalRow[0] = "TOTAL"; // Label in the first column

  XLSX.utils.sheet_add_aoa(worksheet, [totalRow], {
    origin: { r: totalRowIndex, c: 0 },
  });

  // Style the total row
  columns.forEach((col, colIndex) => {
    const cellRef = XLSX.utils.encode_cell({ r: totalRowIndex, c: colIndex });
    let style: ExcelCellStyle = JSON.parse(
      JSON.stringify(CELL_STYLES.tableTotalRow)
    );

    if (col.type === "currency") {
      style.numFmt = NUMBER_FORMATS.currency;
      if (style.alignment) style.alignment.horizontal = "right";
    } else if (col.type === "number") {
      style.numFmt = NUMBER_FORMATS.integer;
      if (style.alignment) style.alignment.horizontal = "right";
    } else if (colIndex === 0) {
      if (style.alignment) style.alignment.horizontal = "left";
    }
    // Ensure vertical alignment is set for all total row cells
    if (style.alignment) style.alignment.vertical = "center";

    applyCellStyle(worksheet, cellRef, style);
  });
};

/**
 * Apply special formatting for income statement sheet
 */
const applyIncomeStatementFormatting = (
  worksheet: XLSX.WorkSheet,
  data: any[]
): void => {
  const headerRowCount = 4; // Title, subtitle, spacer, headers

  data.forEach((item, index) => {
    const rowIndex = headerRowCount + index + 1;

    // Apply special formatting for subtotals and totals
    if (item.isSubTotal || item.isTotal) {
      // Bold formatting for subtotals and totals
      const cellA = `A${rowIndex}`;
      const cellB = `B${rowIndex}`;
      const cellC = `C${rowIndex}`;

      applyCellStyle(worksheet, cellA, {
        ...CELL_STYLES.tableDataEven,
        font: { ...CELL_STYLES.tableDataEven.font, bold: true },
        fill: {
          patternType: "solid",
          fgColor: {
            rgb: item.isTotal ? BRAND_COLORS.primary : BRAND_COLORS.gray[100],
          },
        },
      });

      applyCellStyle(worksheet, cellB, {
        ...CELL_STYLES.tableDataEven,
        font: { ...CELL_STYLES.tableDataEven.font, bold: true },
        fill: {
          patternType: "solid",
          fgColor: {
            rgb: item.isTotal ? BRAND_COLORS.primary : BRAND_COLORS.gray[100],
          },
        },
      });

      applyCellStyle(worksheet, cellC, {
        ...CELL_STYLES.tableDataEven,
        font: {
          ...CELL_STYLES.tableDataEven.font,
          bold: true,
          color: item.isTotal ? { rgb: "FFFFFF" } : undefined,
        },
        fill: {
          patternType: "solid",
          fgColor: {
            rgb: item.isTotal ? BRAND_COLORS.primary : BRAND_COLORS.gray[100],
          },
        },
        numFmt: NUMBER_FORMATS.currency,
      });

      // Add borders for totals
      if (item.isTotal) {
        const borderStyle = {
          style: "thick",
          color: { rgb: BRAND_COLORS.primary },
        };
        [cellA, cellB, cellC].forEach((cell) => {
          const currentStyle = worksheet[cell]?.s || {};
          applyCellStyle(worksheet, cell, {
            ...currentStyle,
            border: {
              top: borderStyle,
              bottom: borderStyle,
              left: borderStyle,
              right: borderStyle,
            },
          });
        });
      }
    }
  });
};

// --- MAIN EXPORT FUNCTION ---

export const createProfessionalExcelReport = (
  data: ReportData,
  options: ExcelTemplateOptions = {}
): XLSX.WorkBook => {
  const workbook = XLSX.utils.book_new();
  const reportPeriod = data.summary?.period || "Tidak Ditentukan";

  // 1. Create Summary Sheet
  if (options.includeSummary !== false) {
    const summarySheet = createSummarySheet(data, options);
    XLSX.utils.book_append_sheet(workbook, summarySheet, "📊 Ringkasan");
  }

  // 2. Create Sales Sheet
  const salesData = data.sales || [];
  const salesColumns = [
    { key: "id", label: "No. Transaksi", type: "text" as const },
    { key: "saleDate", label: "Tanggal", type: "date" as const },
    { key: "customer.name", label: "Pelanggan", type: "text" as const },
    { key: "totalAmount", label: "Total", type: "currency" as const },
    { key: "items.length", label: "Jumlah Item", type: "number" as const },
    { key: "tags", label: "Tag", type: "text" as const },
  ];
  const salesSheet = createDataSheet(
    salesData,
    "Laporan Penjualan",
    salesColumns,
    reportPeriod
  );
  addTotalRowToDataSheet(salesSheet, salesData, salesColumns);
  XLSX.utils.book_append_sheet(workbook, salesSheet, "💰 Penjualan");

  // 3. Create Purchases Sheet
  const purchasesData = data.purchases || [];
  const purchaseColumns = [
    { key: "id", label: "No. Transaksi", type: "text" as const },
    { key: "purchaseDate", label: "Tanggal", type: "date" as const },
    { key: "supplier.name", label: "Supplier", type: "text" as const },
    { key: "totalAmount", label: "Total", type: "currency" as const },
    { key: "items.length", label: "Jumlah Item", type: "number" as const },
    { key: "tags", label: "Tag", type: "text" as const },
  ];
  const purchaseSheet = createDataSheet(
    purchasesData,
    "Laporan Pembelian",
    purchaseColumns,
    reportPeriod
  );
  addTotalRowToDataSheet(purchaseSheet, purchasesData, purchaseColumns);
  XLSX.utils.book_append_sheet(workbook, purchaseSheet, "🛒 Pembelian");

  // 4. Create Products Sheet
  const productsData = data.products || [];
  const productColumns = [
    { key: "name", label: "Nama Produk", type: "text" as const },
    { key: "description", label: "Deskripsi", type: "text" as const },
    { key: "sku", label: "Kode Produk", type: "text" as const },
    { key: "category.name", label: "Kategori", type: "text" as const },
    { key: "stock", label: "Stok", type: "number" as const },
    { key: "cost", label: "Harga Beli", type: "currency" as const },
    { key: "price", label: "Harga Jual", type: "currency" as const },
  ];
  const productSheet = createDataSheet(
    productsData,
    "Laporan Produk",
    productColumns,
    reportPeriod
  );
  addTotalRowToDataSheet(productSheet, productsData, productColumns);
  XLSX.utils.book_append_sheet(workbook, productSheet, "📦 Produk");

  // 5. Income Statement Sheet (if data exists)
  if (data.incomeStatement && data.incomeStatement.length > 0) {
    const incomeStatementColumns = [
      { key: "category", label: "Kategori", type: "text" as const },
      { key: "item", label: "Item", type: "text" as const },
      { key: "amount", label: "Jumlah", type: "currency" as const },
    ];

    const incomeStatementSheet = createDataSheet(
      data.incomeStatement,
      "Laporan Laba Rugi",
      incomeStatementColumns,
      reportPeriod
    );

    // Apply special formatting for income statement
    applyIncomeStatementFormatting(incomeStatementSheet, data.incomeStatement);

    XLSX.utils.book_append_sheet(
      workbook,
      incomeStatementSheet,
      "💰 Laba Rugi"
    );
  }

  // 6. Customers Sheet (if data exists)
  if (data.customers && data.customers.length > 0) {
    const customerColumns = [
      { key: "name", label: "Nama Pelanggan", type: "text" as const },
      { key: "phone", label: "Telepon", type: "text" as const },
      { key: "email", label: "Email", type: "text" as const },
      { key: "address", label: "Alamat", type: "text" as const },
      {
        key: "totalTransactions",
        label: "Total Transaksi",
        type: "number" as const,
      },
      {
        key: "totalSpent",
        label: "Total Pembelian",
        type: "currency" as const,
      },
    ];

    const customerSheet = createDataSheet(
      data.customers,
      "Data Pelanggan",
      customerColumns,
      reportPeriod
    );

    XLSX.utils.book_append_sheet(workbook, customerSheet, "👥 Pelanggan");
  }

  // 7. Suppliers Sheet (if data exists)
  if (data.suppliers && data.suppliers.length > 0) {
    const supplierColumns = [
      { key: "name", label: "Nama Supplier", type: "text" as const },
      { key: "phone", label: "Telepon", type: "text" as const },
      { key: "email", label: "Email", type: "text" as const },
      { key: "address", label: "Alamat", type: "text" as const },
      {
        key: "totalTransactions",
        label: "Total Transaksi",
        type: "number" as const,
      },
      {
        key: "totalPurchases",
        label: "Total Pembelian",
        type: "currency" as const,
      },
    ];

    const supplierSheet = createDataSheet(
      data.suppliers,
      "Data Supplier",
      supplierColumns,
      reportPeriod
    );

    XLSX.utils.book_append_sheet(workbook, supplierSheet, "🏢 Supplier");
  }

  // 8. Services Sheet (if data exists)
  if (data.services && data.services.length > 0) {
    const serviceColumns = [
      { key: "serviceNumber", label: "No. Servis", type: "text" as const },
      { key: "receivedDate", label: "Tanggal Masuk", type: "date" as const },
      { key: "customerName", label: "Nama Pelanggan", type: "text" as const },
      { key: "customerPhone", label: "Telepon", type: "text" as const },
      { key: "deviceType", label: "Jenis Perangkat", type: "text" as const },
      { key: "deviceBrand", label: "Merek", type: "text" as const },
      { key: "deviceModel", label: "Model", type: "text" as const },
      { key: "problemDescription", label: "Keluhan", type: "text" as const },
      { key: "status", label: "Status", type: "text" as const },
      {
        key: "estimatedCost",
        label: "Estimasi Biaya",
        type: "currency" as const,
      },
      { key: "finalCost", label: "Biaya Final", type: "currency" as const },
    ];

    const serviceSheet = createDataSheet(
      data.services,
      "Data Servis",
      serviceColumns,
      reportPeriod
    );

    XLSX.utils.book_append_sheet(workbook, serviceSheet, "🔧 Servis");
  }

  // 9. Spare Parts Sheet (if data exists)
  if (data.spareParts && data.spareParts.length > 0) {
    const sparePartColumns = [
      { key: "serviceNumber", label: "No. Servis", type: "text" as const },
      { key: "entryDate", label: "Tanggal Masuk", type: "date" as const },
      { key: "sparePartName", label: "Nama Sparepart", type: "text" as const },
      { key: "barcode", label: "Barcode", type: "text" as const },
      { key: "quantity", label: "Jumlah", type: "number" as const },
    ];

    const sparePartSheet = createDataSheet(
      data.spareParts,
      "Data Sparepart",
      sparePartColumns,
      reportPeriod
    );

    XLSX.utils.book_append_sheet(workbook, sparePartSheet, "⚙️ Sparepart");
  }

  return workbook;
};
