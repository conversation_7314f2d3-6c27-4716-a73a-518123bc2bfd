// Professional Excel export functionality for Products, with single-sheet support, transaction borders, and cell merging.

import * as XLSX from "xlsx-js-style";
import {
  CELL_STYLES,
  NUMBER_FORMATS,
  SHEET_HEADER_STYLES,
  ExcelCellStyle,
} from "../excelStyles";

// --- UTILITY FUNCTIONS ---

const applyCellStyle = (ws: XLSX.WorkSheet, ref: string, style: any) => {
  if (!ws[ref]) ws[ref] = { t: "s", v: "" };
  ws[ref].s = style;
};

const mergeCells = (ws: XLSX.WorkSheet, range: string) => {
  if (!ws["!merges"]) ws["!merges"] = [];
  ws["!merges"].push(XLSX.utils.decode_range(range));
};

const setColumnWidths = (ws: XLSX.WorkSheet, widths: { wch: number }[]) => {
  ws["!cols"] = widths;
};

const setRowHeights = (
  ws: XLSX.WorkSheet,
  heights: { [row: number]: number }
) => {
  if (!ws["!rows"]) ws["!rows"] = [];
  Object.entries(heights).forEach(([row, hpt]) => {
    const rowIndex = parseInt(row) - 1;
    if (!ws["!rows"]![rowIndex]) ws["!rows"]![rowIndex] = {};
    ws["!rows"]![rowIndex].hpt = hpt;
  });
};

const addAutoFilter = (ws: XLSX.WorkSheet, range: string) => {
  ws["!autofilter"] = { ref: range };
};

const freezePanes = (ws: XLSX.WorkSheet, cell: string) => {
  ws["!view"] = {
    state: "frozen",
    xSplit: 0,
    ySplit: XLSX.utils.decode_cell(cell).r,
    topLeftCell: cell,
  };
};

const getNestedValue = (obj: any, path: string): any => {
  return path.split(".").reduce((acc, part) => acc && acc[part], obj);
};

// --- MERGE LOGIC ---
const applyMergesAndVerticalAlign = (
  worksheet: XLSX.WorkSheet,
  data: any[],
  columns: any[],
  headerRowCount: number
) => {
  const mergeableColumns = columns
    .map((col, index) => ({ ...col, index }))
    .filter((col) => !col.key.startsWith("variant."));

  if (data.length === 0) return;

  data.forEach((_, rowIndex) => {
    mergeableColumns.forEach((col) => {
      const cellRef = XLSX.utils.encode_cell({
        r: rowIndex + headerRowCount,
        c: col.index,
      });
      if (worksheet[cellRef]) {
        if (!worksheet[cellRef].s) worksheet[cellRef].s = {};
        if (!worksheet[cellRef].s.alignment)
          worksheet[cellRef].s.alignment = {};
        worksheet[cellRef].s.alignment.vertical = "center";
      }
    });
  });

  let mergeStartRow = 0;
  for (let i = 1; i <= data.length; i++) {
    if (i === data.length || data[i].id !== data[i - 1].id) {
      if (i - mergeStartRow > 1) {
        mergeableColumns.forEach((col) => {
          const start = { r: mergeStartRow + headerRowCount, c: col.index };
          const end = { r: i - 1 + headerRowCount, c: col.index };
          if (!worksheet["!merges"]) worksheet["!merges"] = [];
          worksheet["!merges"].push({ s: start, e: end });
        });
      }
      mergeStartRow = i;
    }
  }
};

// --- PRODUCTS EXPORT ---

const createCombinedProductsSheet = (
  productsData: any[],
  reportPeriod: string
): XLSX.WorkSheet => {
  const sheetTitle = "Laporan Rinci Produk";
  const headerRowCount = 4;

  const columns = [
    // Product Details (Mergeable)
    { key: "name", label: "Nama Produk", type: "text" },
    { key: "description", label: "Deskripsi", type: "text" },
    { key: "sku", label: "SKU Induk", type: "text" },
    { key: "category.name", label: "Kategori", type: "text" },
    { key: "unit", label: "Unit", type: "text" },
    { key: "price", label: "Harga Jual Induk", type: "currency" },
    { key: "cost", label: "Harga Beli Induk", type: "currency" },
    { key: "stock", label: "Total Stok", type: "number" },
    {
      key: "isDraft",
      label: "Status",
      type: "text",
      formatter: (isDraft: boolean) => (isDraft ? "Draft" : "Selesai"),
    },
    // Variant Details (Not Mergeable)
    { key: "variant.colorName", label: "Nama Varian", type: "text" },
    { key: "variant.sku", label: "SKU Varian", type: "text" },
    { key: "variant.price", label: "Harga Varian", type: "currency" },
    { key: "variant.stock", label: "Stok Varian", type: "number" },
  ];

  const headers = columns.map((col) => col.label);
  const worksheet = XLSX.utils.aoa_to_sheet([[]]);

  const processedData = productsData.flatMap((product) => {
    if (product.variants && product.variants.length > 0) {
      return product.variants.map((variant: any) => ({
        ...product,
        variant: variant,
      }));
    }
    // If there are no variants, create a single row for the main product
    return [{ ...product, variant: {} }];
  });

  processedData.forEach((row, index, arr) => {
    row.isFirstInGroup = index === 0 || row.id !== arr[index - 1].id;
    row.isLastInGroup =
      index === arr.length - 1 || row.id !== arr[index + 1].id;
  });

  XLSX.utils.sheet_add_aoa(
    worksheet,
    [[sheetTitle], [`Periode: ${reportPeriod}`]],
    { origin: "A1" }
  );
  mergeCells(worksheet, `A1:${XLSX.utils.encode_col(columns.length - 1)}1`);
  applyCellStyle(worksheet, "A1", CELL_STYLES.sheetTitle);
  mergeCells(worksheet, `A2:${XLSX.utils.encode_col(columns.length - 1)}2`);
  applyCellStyle(worksheet, "A2", CELL_STYLES.sheetSubtitle);

  XLSX.utils.sheet_add_aoa(worksheet, [headers], {
    origin: { r: headerRowCount - 1, c: 0 },
  });
  columns.forEach((_, index) => {
    applyCellStyle(
      worksheet,
      XLSX.utils.encode_cell({ r: headerRowCount - 1, c: index }),
      SHEET_HEADER_STYLES.products
    );
  });

  const rows = processedData.map((item) =>
    columns.map((col) => {
      let value = getNestedValue(item, col.key);
      if (value === null || value === undefined) return "";
      if (col.formatter) value = col.formatter(value);
      switch (col.type) {
        case "currency":
        case "number":
          return typeof value === "number" ? value : 0;
        case "date":
          return value ? new Date(value) : "";
        default:
          return String(value);
      }
    })
  );
  XLSX.utils.sheet_add_aoa(worksheet, rows, {
    origin: { r: headerRowCount, c: 0 },
  });

  processedData.forEach((item, rowIndex) => {
    const actualRow = rowIndex + headerRowCount;
    columns.forEach((col, colIndex) => {
      const cellRef = XLSX.utils.encode_cell({ r: actualRow, c: colIndex });
      let style = JSON.parse(
        JSON.stringify(
          rowIndex % 2 === 0
            ? CELL_STYLES.tableDataEven
            : CELL_STYLES.tableDataOdd
        )
      );
      const border = { style: "thin", color: { rgb: "888888" } };
      if (item.isFirstInGroup) style.border = { ...style.border, top: border };
      if (item.isLastInGroup)
        style.border = { ...style.border, bottom: border };
      applyCellStyle(worksheet, cellRef, style);
    });
  });

  applyMergesAndVerticalAlign(
    worksheet,
    processedData,
    columns,
    headerRowCount
  );

  const colWidths = columns.map((col, index) => {
    const headerLength = col.label.length;
    const maxDataLength =
      processedData.length > 0
        ? Math.max(...rows.map((row) => String(row[index] || "").length))
        : 0;
    return {
      wch: Math.min(60, Math.max(headerLength + 5, maxDataLength + 3, 18)),
    };
  });
  setColumnWidths(worksheet, colWidths);
  setRowHeights(worksheet, { 1: 22, 2: 18, [headerRowCount]: 30 });
  freezePanes(worksheet, `A${headerRowCount + 1}`);
  if (processedData.length > 0) {
    addAutoFilter(
      worksheet,
      `A${headerRowCount}:${XLSX.utils.encode_col(columns.length - 1)}${processedData.length + headerRowCount}`
    );
  }

  return worksheet;
};

export const createProductsExcelReport = (
  productsData: any[],
  reportPeriod: string,
  options: { companyName?: string; reportTitle?: string } = {}
): XLSX.WorkBook => {
  const workbook = XLSX.utils.book_new();
  const combinedSheet = createCombinedProductsSheet(productsData, reportPeriod);
  XLSX.utils.book_append_sheet(workbook, combinedSheet, "Laporan Rinci Produk");
  return workbook;
};
