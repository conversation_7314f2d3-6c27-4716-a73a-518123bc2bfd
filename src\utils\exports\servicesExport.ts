// Professional Excel export functionality for Services, with single-sheet support, transaction borders, and cell merging.

import * as XLSX from "xlsx-js-style";
import {
  CELL_STYLES,
  NUMBER_FORMATS,
  SHEET_HEADER_STYLES,
  ExcelCellStyle,
} from "../excelStyles";

// --- UTILITY FUNCTIONS ---

const applyCellStyle = (ws: XLSX.WorkSheet, ref: string, style: any) => {
  if (!ws[ref]) ws[ref] = { t: "s", v: "" };
  ws[ref].s = style;
};

const mergeCells = (ws: XLSX.WorkSheet, range: string) => {
  if (!ws["!merges"]) ws["!merges"] = [];
  ws["!merges"].push(XLSX.utils.decode_range(range));
};

const setColumnWidths = (ws: XLSX.WorkSheet, widths: { wch: number }[]) => {
  ws["!cols"] = widths;
};

const setRowHeights = (
  ws: XLSX.WorkSheet,
  heights: { [row: number]: number }
) => {
  if (!ws["!rows"]) ws["!rows"] = [];
  Object.entries(heights).forEach(([row, hpt]) => {
    const rowIndex = parseInt(row) - 1;
    if (!ws["!rows"]![rowIndex]) ws["!rows"]![rowIndex] = {};
    ws["!rows"]![rowIndex].hpt = hpt;
  });
};

const addAutoFilter = (ws: XLSX.WorkSheet, range: string) => {
  ws["!autofilter"] = { ref: range };
};

const freezePanes = (ws: XLSX.WorkSheet, cell: string) => {
  ws["!view"] = {
    state: "frozen",
    xSplit: 0,
    ySplit: XLSX.utils.decode_cell(cell).r,
    topLeftCell: cell,
  };
};

const getNestedValue = (obj: any, path: string): any => {
  return path.split(".").reduce((acc, part) => acc && acc[part], obj);
};

// --- NEW MERGE LOGIC ---
const applyMergesAndVerticalAlign = (
  worksheet: XLSX.WorkSheet,
  data: any[],
  columns: any[],
  headerRowCount: number
) => {
  const mergeableColumns = columns
    .map((col, index) => ({ ...col, index }))
    .filter((col) => !col.key.startsWith("sparePart."));

  if (data.length === 0) return;

  // Apply vertical alignment to all mergeable columns
  data.forEach((_, rowIndex) => {
    mergeableColumns.forEach((col) => {
      const cellRef = XLSX.utils.encode_cell({
        r: rowIndex + headerRowCount,
        c: col.index,
      });
      if (worksheet[cellRef]) {
        if (!worksheet[cellRef].s) worksheet[cellRef].s = {};
        if (!worksheet[cellRef].s.alignment)
          worksheet[cellRef].s.alignment = {};
        worksheet[cellRef].s.alignment.vertical = "center";
      }
    });
  });

  // Apply merges
  let mergeStartRow = 0;
  for (let i = 1; i <= data.length; i++) {
    if (
      i === data.length ||
      data[i].serviceNumber !== data[i - 1].serviceNumber
    ) {
      if (i - mergeStartRow > 1) {
        mergeableColumns.forEach((col) => {
          const start = { r: mergeStartRow + headerRowCount, c: col.index };
          const end = { r: i - 1 + headerRowCount, c: col.index };
          if (!worksheet["!merges"]) worksheet["!merges"] = [];
          worksheet["!merges"].push({ s: start, e: end });
        });
      }
      mergeStartRow = i;
    }
  }
};

// --- SERVICES EXPORT ---

const createCombinedServicesSheet = (
  servicesData: any[],
  reportPeriod: string
): XLSX.WorkSheet => {
  const sheetTitle = "Laporan Rinci Servis";
  const headerRowCount = 4;

  const columns = [
    // Service Details (Mergeable)
    { key: "serviceNumber", label: "No. Servis", type: "text" },
    { key: "receivedDate", label: "Tanggal Masuk", type: "date" },
    { key: "customerName", label: "Nama Pelanggan", type: "text" },
    { key: "customerPhone", label: "Telepon", type: "text" },
    { key: "customerEmail", label: "Email Pelanggan", type: "text" },
    { key: "deviceType", label: "Jenis Perangkat", type: "text" },
    { key: "deviceBrand", label: "Merek", type: "text" },
    { key: "deviceModel", label: "Model", type: "text" },
    { key: "deviceSerialNumber", label: "No. Seri Perangkat", type: "text" },
    { key: "problemDescription", label: "Keluhan", type: "text" },
    { key: "diagnosisNotes", label: "Catatan Diagnosa", type: "text" },
    { key: "repairNotes", label: "Catatan Perbaikan", type: "text" },
    { key: "status", label: "Status", type: "text" },
    { key: "estimatedCost", label: "Estimasi Biaya", type: "currency" },
    { key: "finalCost", label: "Biaya Final", type: "currency" },
    { key: "warrantyPeriod", label: "Periode Garansi", type: "number" },
    { key: "estimatedCompletionDate", label: "Estimasi Selesai", type: "date" },
    { key: "completedDate", label: "Tanggal Selesai", type: "date" },
    { key: "deliveredDate", label: "Tanggal Diserahkan", type: "date" },
    {
      key: "isDraft",
      label: "Status Draft",
      type: "text",
      formatter: (isDraft: boolean) => (isDraft ? "Draft" : "Final"),
    },
    // Spare Part Details (Not Mergeable)
    { key: "sparePart.name", label: "Nama Sparepart", type: "text" },
    { key: "sparePart.barcode", label: "Barcode", type: "text" },
    { key: "sparePart.quantity", label: "Jumlah", type: "number" },
  ];

  const headers = columns.map((col) => col.label);
  const worksheet = XLSX.utils.aoa_to_sheet([[]]);

  // 1. Flatten data
  const processedData = servicesData.flatMap((service) =>
    (service.spareParts && service.spareParts.length > 0
      ? service.spareParts
      : [{}]
    ).map((sparePart: any) => ({
      ...service,
      sparePart: sparePart || {},
    }))
  );

  // Add grouping flags for borders
  processedData.forEach((row, index, arr) => {
    row.isFirstInGroup =
      index === 0 || row.serviceNumber !== arr[index - 1].serviceNumber;
    row.isLastInGroup =
      index === arr.length - 1 ||
      row.serviceNumber !== arr[index + 1].serviceNumber;
  });

  // 2. Add Sheet Title & Subtitle
  XLSX.utils.sheet_add_aoa(
    worksheet,
    [[sheetTitle], [`Periode: ${reportPeriod}`]],
    { origin: "A1" }
  );
  mergeCells(worksheet, `A1:${XLSX.utils.encode_col(columns.length - 1)}1`);
  applyCellStyle(worksheet, "A1", CELL_STYLES.sheetTitle);
  mergeCells(worksheet, `A2:${XLSX.utils.encode_col(columns.length - 1)}2`);
  applyCellStyle(worksheet, "A2", CELL_STYLES.sheetSubtitle);

  // 3. Add Table Headers
  XLSX.utils.sheet_add_aoa(worksheet, [headers], {
    origin: { r: headerRowCount - 1, c: 0 },
  });
  columns.forEach((_, index) => {
    applyCellStyle(
      worksheet,
      XLSX.utils.encode_cell({ r: headerRowCount - 1, c: index }),
      SHEET_HEADER_STYLES.sales // Using sales style for services as a placeholder
    );
  });

  // 4. Process and Add Data Rows
  const rows = processedData.map((item) =>
    columns.map((col) => {
      let value = getNestedValue(item, col.key);
      if (value === null || value === undefined) return "";
      if (col.formatter) value = col.formatter(value);
      switch (col.type) {
        case "currency":
        case "number":
          return typeof value === "number" ? value : 0;
        case "date":
          return value ? new Date(value) : "";
        default:
          return String(value);
      }
    })
  );
  XLSX.utils.sheet_add_aoa(worksheet, rows, {
    origin: { r: headerRowCount, c: 0 },
  });

  // 5. Style Data Rows with Borders
  processedData.forEach((item, rowIndex) => {
    const actualRow = rowIndex + headerRowCount;
    columns.forEach((col, colIndex) => {
      const cellRef = XLSX.utils.encode_cell({ r: actualRow, c: colIndex });
      let style = JSON.parse(
        JSON.stringify(
          rowIndex % 2 === 0
            ? CELL_STYLES.tableDataEven
            : CELL_STYLES.tableDataOdd
        )
      );
      const border = { style: "thin", color: { rgb: "888888" } };
      if (item.isFirstInGroup) style.border = { ...style.border, top: border };
      if (item.isLastInGroup)
        style.border = { ...style.border, bottom: border };
      applyCellStyle(worksheet, cellRef, style);
    });
  });

  // 6. Apply Merges and Final Styling
  applyMergesAndVerticalAlign(
    worksheet,
    processedData,
    columns,
    headerRowCount
  );

  // 7. Finalize sheet layout
  const colWidths = columns.map((col, index) => {
    const headerLength = col.label.length;
    const maxDataLength =
      processedData.length > 0
        ? Math.max(...rows.map((row) => String(row[index] || "").length))
        : 0;
    return {
      wch: Math.min(60, Math.max(headerLength + 5, maxDataLength + 3, 18)),
    };
  });
  setColumnWidths(worksheet, colWidths);
  setRowHeights(worksheet, { 1: 22, 2: 18, [headerRowCount]: 30 });
  freezePanes(worksheet, `A${headerRowCount + 1}`);
  if (processedData.length > 0) {
    addAutoFilter(
      worksheet,
      `A${headerRowCount}:${XLSX.utils.encode_col(columns.length - 1)}${
        processedData.length + headerRowCount
      }`
    );
  }

  return worksheet;
};

export const createServicesExcelReport = (
  servicesData: any[],
  reportPeriod: string,
  _options: { companyName?: string; reportTitle?: string } = {}
): XLSX.WorkBook => {
  const workbook = XLSX.utils.book_new();
  const combinedSheet = createCombinedServicesSheet(servicesData, reportPeriod);
  XLSX.utils.book_append_sheet(workbook, combinedSheet, "Laporan Rinci Servis");
  return workbook;
};
