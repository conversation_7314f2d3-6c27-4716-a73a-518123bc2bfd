// Professional Excel export functionality for Suppliers
// Extracted from consolidated excelTemplate.ts for better maintainability

import * as XLSX from "xlsx-js-style";
import {
  CELL_STYLES,
  NUMBER_FORMATS,
  SHEET_HEADER_STYLES,
  ExcelCellStyle,
} from "../excelStyles";

// --- UTILITY FUNCTIONS ---

const applyCellStyle = (ws: XLSX.WorkSheet, ref: string, style: any) => {
  if (!ws[ref]) ws[ref] = { t: "s", v: "" };
  ws[ref].s = style;
};

const mergeCells = (ws: XLSX.WorkSheet, range: string) => {
  if (!ws["!merges"]) ws["!merges"] = [];
  ws["!merges"].push(XLSX.utils.decode_range(range));
};

const setColumnWidths = (ws: XLSX.WorkSheet, widths: { wch: number }[]) => {
  ws["!cols"] = widths;
};

const setRowHeights = (
  ws: XLSX.WorkSheet,
  heights: { [row: number]: number }
) => {
  if (!ws["!rows"]) ws["!rows"] = [];
  Object.entries(heights).forEach(([row, hpt]) => {
    const rowIndex = parseInt(row) - 1;
    if (!ws["!rows"]![rowIndex]) ws["!rows"]![rowIndex] = {};
    ws["!rows"]![rowIndex].hpt = hpt;
  });
};

const addAutoFilter = (ws: XLSX.WorkSheet, range: string) => {
  ws["!autofilter"] = { ref: range };
};

const freezePanes = (ws: XLSX.WorkSheet, cell: string) => {
  ws["!view"] = {
    state: "frozen",
    xSplit: 0,
    ySplit: XLSX.utils.decode_cell(cell).r,
    topLeftCell: cell,
  };
};

const getNestedValue = (obj: any, path: string): any => {
  return path.split(".").reduce((acc, part) => acc && acc[part], obj);
};

// --- SUPPLIERS SPECIFIC FUNCTIONS ---

/**
 * Creates a well-formatted suppliers data sheet
 */
export const createSuppliersDataSheet = (
  data: any[],
  reportPeriod: string
): XLSX.WorkSheet => {
  const sheetTitle = "Data Supplier";
  const columns = [
    { key: "name", label: "Nama Supplier", type: "text" as const },
    { key: "firstName", label: "Nama Depan", type: "text" as const },
    { key: "middleName", label: "Nama Tengah", type: "text" as const },
    { key: "lastName", label: "Nama Belakang", type: "text" as const },
    { key: "contactName", label: "Nama Kontak", type: "text" as const },
    { key: "phone", label: "Telepon", type: "text" as const },
    { key: "telephone", label: "Telepon Alternatif", type: "text" as const },
    { key: "fax", label: "Fax", type: "text" as const },
    { key: "email", label: "Email", type: "text" as const },
    { key: "identityType", label: "Jenis Identitas", type: "text" as const },
    { key: "identityNumber", label: "Nomor Identitas", type: "text" as const },
    { key: "NIK", label: "NIK", type: "text" as const },
    { key: "NPWP", label: "NPWP", type: "text" as const },
    { key: "companyName", label: "Nama Perusahaan", type: "text" as const },
    { key: "otherInfo", label: "Info Lain", type: "text" as const },
    { key: "address", label: "Alamat", type: "text" as const },
    { key: "billingAddress", label: "Alamat Penagihan", type: "text" as const },
    {
      key: "shippingAddress",
      label: "Alamat Pengiriman",
      type: "text" as const,
    },
    {
      key: "sameAsShipping",
      label: "Sama Dengan Alamat Pengiriman",
      type: "boolean" as const,
    },
    { key: "bankName", label: "Nama Bank", type: "text" as const },
    { key: "bankBranch", label: "Cabang Bank", type: "text" as const },
    {
      key: "accountHolder",
      label: "Nama Pemegang Rekening",
      type: "text" as const,
    },
    { key: "accountNumber", label: "Nomor Rekening", type: "text" as const },
    { key: "notes", label: "Catatan", type: "text" as const },
    { key: "createdAt", label: "Dibuat Pada", type: "date" as const },
    { key: "updatedAt", label: "Diperbarui Pada", type: "date" as const },
    {
      key: "totalTransactions",
      label: "Total Transaksi",
      type: "number" as const,
    },
    {
      key: "totalPurchases",
      label: "Total Pembelian",
      type: "currency" as const,
    },
  ];

  const headerRowCount = 4;
  const headers = columns.map((col) => col.label);
  const worksheet = XLSX.utils.aoa_to_sheet([[]]);

  // 1. Add Sheet Title & Subtitle
  XLSX.utils.sheet_add_aoa(
    worksheet,
    [[sheetTitle], [`Periode: ${reportPeriod}`]],
    { origin: "A1" }
  );
  mergeCells(worksheet, `A1:${XLSX.utils.encode_col(columns.length - 1)}1`);
  applyCellStyle(worksheet, "A1", CELL_STYLES.sheetTitle);
  mergeCells(worksheet, `A2:${XLSX.utils.encode_col(columns.length - 1)}2`);
  applyCellStyle(worksheet, "A2", CELL_STYLES.sheetSubtitle);

  // 2. Add Table Headers
  XLSX.utils.sheet_add_aoa(worksheet, [headers], {
    origin: { r: headerRowCount - 1, c: 0 },
  });
  const headerStyle = SHEET_HEADER_STYLES.suppliers;
  columns.forEach((_, index) => {
    applyCellStyle(
      worksheet,
      XLSX.utils.encode_cell({ r: headerRowCount - 1, c: index }),
      headerStyle
    );
  });

  // 3. Process and Add Data Rows
  const rows = data.map((item) =>
    columns.map((col) => {
      let value = getNestedValue(item, col.key);
      if (value === null || value === undefined) return "";

      switch (col.type) {
        case "currency":
          return typeof value === "number" ? value : 0;
        case "number":
          return typeof value === "number" ? value : 0;
        case "boolean":
          return value ? "Ya" : "Tidak";
        case "date":
          return value ? new Date(value) : "";
        default:
          return String(value);
      }
    })
  );

  XLSX.utils.sheet_add_aoa(worksheet, rows, {
    origin: { r: headerRowCount, c: 0 },
  });

  // 4. Style Data Rows
  rows.forEach((_, rowIndex) => {
    const actualRow = rowIndex + headerRowCount;
    const isEven = rowIndex % 2 === 0;
    columns.forEach((col, colIndex) => {
      const cellRef = XLSX.utils.encode_cell({ r: actualRow, c: colIndex });
      let style: ExcelCellStyle = JSON.parse(
        JSON.stringify(
          isEven ? CELL_STYLES.tableDataEven : CELL_STYLES.tableDataOdd
        )
      );

      // Add borders to each row to group them visually
      const border = { style: "thin" as const, color: { rgb: "888888" } };
      style.border = {
        ...style.border,
        top: border,
        bottom: border,
      };

      if (col.type === "currency") {
        style.numFmt = NUMBER_FORMATS.currency;
        if (style.alignment) style.alignment.horizontal = "right";
      } else if (col.type === "number") {
        style.numFmt = NUMBER_FORMATS.integer;
        if (style.alignment) style.alignment.horizontal = "right";
      } else if (
        col.key === "address" ||
        col.key === "billingAddress" ||
        col.key === "shippingAddress" ||
        col.key === "notes"
      ) {
        if (style.alignment) {
          style.alignment.vertical = "top";
          style.alignment.wrapText = true;
        }
      }
      applyCellStyle(worksheet, cellRef, style);
    });
  });

  // 5. Calculate Column Widths
  const colWidths = columns.map((col, index) => {
    const headerLength = col.label.length;
    const maxDataLength =
      data.length > 0
        ? Math.max(...rows.map((row) => String(row[index] || "").length))
        : 0;
    let width = Math.max(headerLength + 5, maxDataLength + 3, 15);
    if (
      col.key === "address" ||
      col.key === "billingAddress" ||
      col.key === "shippingAddress" ||
      col.key === "notes"
    )
      width = 40;
    else if (width > 60) width = 60;
    return { wch: width };
  });
  setColumnWidths(worksheet, colWidths);

  // 6. Set Row Heights
  const rowHeights: { [key: number]: number } = {
    1: 22,
    2: 18,
    [headerRowCount]: 30,
  };
  if (columns.some((c) => c.key === "address")) {
    rows.forEach((_, i) => {
      rowHeights[headerRowCount + i + 1] = 35;
    });
  }
  setRowHeights(worksheet, rowHeights);

  // 7. Add Interactive Features
  freezePanes(worksheet, `A${headerRowCount + 1}`);
  if (data.length > 0) {
    addAutoFilter(
      worksheet,
      `A${headerRowCount}:${XLSX.utils.encode_col(columns.length - 1)}${data.length + headerRowCount}`
    );
  }

  return worksheet;
};

/**
 * Creates a complete suppliers Excel workbook
 */
export const createSuppliersExcelReport = (
  suppliersData: any[],
  reportPeriod: string,
  _options: { companyName?: string; reportTitle?: string } = {}
): XLSX.WorkBook => {
  const workbook = XLSX.utils.book_new();

  // Create suppliers sheet
  const suppliersSheet = createSuppliersDataSheet(suppliersData, reportPeriod);
  XLSX.utils.book_append_sheet(workbook, suppliersSheet, "🏢 Supplier");

  return workbook;
};
