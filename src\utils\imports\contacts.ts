// Contact import template generation utilities
// Creates standardized Excel import templates for customers and suppliers

import * as XLSX from "xlsx-js-style";
import {
  applyCellStyle,
  mergeCells,
  setColumnWidths,
  setRowHeights,
  applyHeaderStyling,
  applyColumnHeaderStyling,
  setStandardRowHeights,
  createInstructionsSheet,
  getCommonInstructions,
  getContactFormatInstructions,
  CELL_STYLES,
  BRAND_COLORS,
} from "./shared";

/**
 * Creates a professional import template for customers
 */
export const createCustomerImportTemplate = (): XLSX.WorkBook => {
  const workbook = XLSX.utils.book_new();

  // Create main template sheet
  const templateData = [
    // Header rows
    [
      "TEMPLATE IMPORT PELANGGAN",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
    ],
    [
      "KivaPOS - Sistem Manajemen Pelanggan",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
    ],
    ["", "", "", "", "", "", "", "", "", "", "", "", "", ""],
    // Column headers
    [
      "Nama Pelanggan*",
      "Nama Depan",
      "Nama Tengah",
      "Nama Belakang",
      "Nama Kontak",
      "Telepon",
      "Email",
      "Jenis Identitas",
      "Nomor Identitas",
      "NIK",
      "NPWP",
      "Nama Perusahaan",
      "Alamat",
      "Catatan",
    ],
    // Sample data rows
    [
      "John Doe",
      "John",
      "",
      "Doe",
      "John Doe",
      "081234567890",
      "<EMAIL>",
      "KTP",
      "1234567890123456",
      "1234567890123456",
      "12.345.678.9-012.000",
      "PT Example Corp",
      "Jl. Contoh No. 123, Jakarta",
      "Pelanggan VIP",
    ],
    [
      "Jane Smith",
      "Jane",
      "",
      "Smith",
      "Jane Smith",
      "081234567891",
      "<EMAIL>",
      "KTP",
      "1234567890123457",
      "",
      "",
      "",
      "Jl. Sample No. 456, Bandung",
      "",
    ],
  ];

  const templateSheet = XLSX.utils.aoa_to_sheet(templateData);

  // Apply header styling
  applyHeaderStyling(
    templateSheet,
    "TEMPLATE IMPORT PELANGGAN",
    "KivaPOS - Sistem Manajemen Pelanggan",
    14
  );

  // Style column headers
  applyColumnHeaderStyling(templateSheet, 14);

  // Set column widths
  setColumnWidths(templateSheet, [
    { wch: 20 }, // Nama Pelanggan
    { wch: 15 }, // Nama Depan
    { wch: 15 }, // Nama Tengah
    { wch: 15 }, // Nama Belakang
    { wch: 20 }, // Nama Kontak
    { wch: 15 }, // Telepon
    { wch: 25 }, // Email
    { wch: 15 }, // Jenis Identitas
    { wch: 20 }, // Nomor Identitas
    { wch: 20 }, // NIK
    { wch: 20 }, // NPWP
    { wch: 25 }, // Nama Perusahaan
    { wch: 35 }, // Alamat
    { wch: 20 }, // Catatan
  ]);

  // Set row heights
  setStandardRowHeights(templateSheet);

  XLSX.utils.book_append_sheet(workbook, templateSheet, "Template Pelanggan");

  // Create instructions sheet
  const instructions = [
    "1. KOLOM WAJIB (Harus diisi):",
    "   • Nama Pelanggan: Nama lengkap pelanggan",
    "",
    "2. KOLOM OPSIONAL:",
    "   • Nama Depan/Tengah/Belakang: Pemisahan nama untuk keperluan formal",
    "   • Nama Kontak: Nama untuk komunikasi sehari-hari",
    "   • Telepon: Nomor telepon utama",
    "   • Email: Alamat email pelanggan",
    "   • Jenis Identitas: KTP, SIM, Paspor, dll",
    "   • Nomor Identitas: Nomor dokumen identitas",
    "   • NIK: Nomor Induk Kependudukan",
    "   • NPWP: Nomor Pokok Wajib Pajak",
    "   • Nama Perusahaan: Nama perusahaan (jika ada)",
    "   • Alamat: Alamat lengkap pelanggan",
    "   • Catatan: Informasi tambahan",
    "",
    "3. FORMAT DATA:",
    ...getContactFormatInstructions(),
    "   • NIK: 16 digit angka",
    "   • NPWP: Format XX.XXX.XXX.X-XXX.XXX",
    "",
    ...getCommonInstructions(),
  ];

  createInstructionsSheet(
    workbook,
    "PETUNJUK PENGGUNAAN TEMPLATE IMPORT PELANGGAN",
    instructions
  );

  return workbook;
};

/**
 * Creates a professional import template for suppliers
 */
export const createSupplierImportTemplate = (): XLSX.WorkBook => {
  const workbook = XLSX.utils.book_new();

  // Create main template sheet
  const templateData = [
    // Header rows
    [
      "TEMPLATE IMPORT SUPPLIER",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
    ],
    [
      "KivaPOS - Sistem Manajemen Supplier",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
    ],
    ["", "", "", "", "", "", "", "", "", "", "", "", "", ""],
    // Column headers
    [
      "Nama Supplier*",
      "Nama Depan",
      "Nama Tengah",
      "Nama Belakang",
      "Nama Kontak",
      "Telepon",
      "Email",
      "Jenis Identitas",
      "Nomor Identitas",
      "NIK",
      "NPWP",
      "Nama Perusahaan",
      "Alamat",
      "Catatan",
    ],
    // Sample data rows
    [
      "PT Supplier ABC",
      "Ahmad",
      "",
      "Wijaya",
      "Ahmad Wijaya",
      "021-12345678",
      "<EMAIL>",
      "KTP",
      "1234567890123456",
      "1234567890123456",
      "12.345.678.9-012.000",
      "PT Supplier ABC",
      "Jl. Industri No. 789, Jakarta",
      "Supplier utama elektronik",
    ],
    [
      "CV Supplier XYZ",
      "Siti",
      "",
      "Rahayu",
      "Siti Rahayu",
      "021-87654321",
      "<EMAIL>",
      "KTP",
      "1234567890123457",
      "",
      "",
      "CV Supplier XYZ",
      "Jl. Perdagangan No. 321, Surabaya",
      "",
    ],
  ];

  const templateSheet = XLSX.utils.aoa_to_sheet(templateData);

  // Apply header styling
  applyHeaderStyling(
    templateSheet,
    "TEMPLATE IMPORT SUPPLIER",
    "KivaPOS - Sistem Manajemen Supplier",
    14
  );

  // Style column headers
  applyColumnHeaderStyling(templateSheet, 14);

  // Set column widths (same as customer template)
  setColumnWidths(templateSheet, [
    { wch: 20 }, // Nama Supplier
    { wch: 15 }, // Nama Depan
    { wch: 15 }, // Nama Tengah
    { wch: 15 }, // Nama Belakang
    { wch: 20 }, // Nama Kontak
    { wch: 15 }, // Telepon
    { wch: 25 }, // Email
    { wch: 15 }, // Jenis Identitas
    { wch: 20 }, // Nomor Identitas
    { wch: 20 }, // NIK
    { wch: 20 }, // NPWP
    { wch: 25 }, // Nama Perusahaan
    { wch: 35 }, // Alamat
    { wch: 20 }, // Catatan
  ]);

  // Set row heights
  setStandardRowHeights(templateSheet);

  XLSX.utils.book_append_sheet(workbook, templateSheet, "Template Supplier");

  // Create instructions sheet
  const instructions = [
    "1. KOLOM WAJIB (Harus diisi):",
    "   • Nama Supplier: Nama lengkap supplier/pemasok",
    "",
    "2. KOLOM OPSIONAL:",
    "   • Nama Depan/Tengah/Belakang: Pemisahan nama untuk keperluan formal",
    "   • Nama Kontak: Nama person in charge untuk komunikasi",
    "   • Telepon: Nomor telepon utama supplier",
    "   • Email: Alamat email supplier",
    "   • Jenis Identitas: KTP, SIM, Paspor, dll",
    "   • Nomor Identitas: Nomor dokumen identitas",
    "   • NIK: Nomor Induk Kependudukan",
    "   • NPWP: Nomor Pokok Wajib Pajak",
    "   • Nama Perusahaan: Nama resmi perusahaan supplier",
    "   • Alamat: Alamat lengkap supplier",
    "   • Catatan: Informasi tambahan tentang supplier",
    "",
    "3. FORMAT DATA:",
    ...getContactFormatInstructions(),
    "   • NIK: 16 digit angka",
    "   • NPWP: Format XX.XXX.XXX.X-XXX.XXX",
    "",
    ...getCommonInstructions(),
  ];

  createInstructionsSheet(
    workbook,
    "PETUNJUK PENGGUNAAN TEMPLATE IMPORT SUPPLIER",
    instructions
  );

  return workbook;
};
