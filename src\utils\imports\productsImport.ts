// Product import template generation utilities
// Creates standardized Excel import templates for products

import * as XLSX from "xlsx-js-style";
import {
  applyCellStyle,
  mergeCells,
  setColumnWidths,
  setRowHeights,
  applyHeaderStyling,
  applyColumnHeaderStyling,
  setStandardRowHeights,
  createInstructionsSheet,
  getCommonInstructions,
  CELL_STYLES,
  BRAND_COLORS,
} from "./shared";

/**
 * Creates a professional import template for products
 */
export const createProductImportTemplate = (): XLSX.WorkBook => {
  const workbook = XLSX.utils.book_new();

  // Create main template sheet
  const templateData = [
    // Header rows
    ["TEMPLATE IMPORT PRODUK", "", "", "", "", "", "", "", "", "", "", ""],
    [
      "KivaPOS - Sistem Manajemen Produk",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
    ],
    ["", "", "", "", "", "", "", "", "", "", "", ""],
    // Column headers
    [
      "Nama Produk*",
      "Kategori",
      "Satuan",
      "Harga Beli",
      "Harga Jual*",
      "Stok Awal",
      "Stok Minimum",
      "Kode Produk",
      "Barcode",
      "Deskripsi",
      "Warna/Varian",
      "Catatan",
    ],
    // Sample data rows
    [
      "Contoh Produk 1",
      "Elektronik",
      "Buah",
      "50000",
      "75000",
      "100",
      "10",
      "PROD001",
      "1234567890123",
      "Deskripsi produk contoh",
      "Merah",
      "Catatan tambahan",
    ],
    [
      "Contoh Produk 2",
      "Makanan",
      "Kg",
      "25000",
      "35000",
      "50",
      "5",
      "PROD002",
      "",
      "Produk makanan segar",
      "",
      "",
    ],
  ];

  const templateSheet = XLSX.utils.aoa_to_sheet(templateData);

  // Apply header styling
  applyHeaderStyling(
    templateSheet,
    "TEMPLATE IMPORT PRODUK",
    "KivaPOS - Sistem Manajemen Produk",
    12
  );

  // Style column headers
  applyColumnHeaderStyling(templateSheet, 12);

  // Set column widths
  setColumnWidths(templateSheet, [
    { wch: 25 }, // Nama Produk
    { wch: 15 }, // Kategori
    { wch: 10 }, // Satuan
    { wch: 12 }, // Harga Beli
    { wch: 12 }, // Harga Jual
    { wch: 10 }, // Stok Awal
    { wch: 12 }, // Stok Minimum
    { wch: 12 }, // Kode Produk
    { wch: 15 }, // Barcode
    { wch: 30 }, // Deskripsi
    { wch: 15 }, // Warna/Varian
    { wch: 20 }, // Catatan
  ]);

  // Set row heights
  setStandardRowHeights(templateSheet);

  XLSX.utils.book_append_sheet(workbook, templateSheet, "Template Produk");

  // Create instructions sheet
  const instructions = [
    "1. KOLOM WAJIB (Harus diisi):",
    "   • Nama Produk: Nama produk yang akan ditambahkan",
    "   • Harga Jual: Harga jual produk (dalam Rupiah, tanpa titik/koma)",
    "",
    "2. KOLOM OPSIONAL:",
    "   • Kategori: Kategori produk (akan dibuat otomatis jika belum ada)",
    "   • Satuan: Satuan produk (akan dibuat otomatis jika belum ada)",
    "   • Harga Beli: Harga beli produk",
    "   • Stok Awal: Jumlah stok awal produk",
    "   • Stok Minimum: Batas minimum stok untuk peringatan",
    "   • Kode Produk: Kode unik produk",
    "   • Barcode: Barcode produk",
    "   • Deskripsi: Deskripsi detail produk",
    "   • Warna/Varian: Varian warna produk",
    "   • Catatan: Catatan tambahan",
    "",
    "3. FORMAT DATA:",
    "   • Harga: Masukkan angka tanpa titik/koma (contoh: 50000)",
    "   • Stok: Masukkan angka bulat (contoh: 100)",
    "   • Teks: Maksimal 255 karakter per kolom",
    "",
    ...getCommonInstructions(),
  ];

  createInstructionsSheet(
    workbook,
    "PETUNJUK PENGGUNAAN TEMPLATE IMPORT PRODUK",
    instructions
  );

  return workbook;
};
