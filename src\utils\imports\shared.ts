// Shared utilities for import template generation
// Common functions used across all import template types

import * as XLSX from "xlsx-js-style";
import {
  CELL_STYLES,
  NUMBER_FORMATS,
  SHEET_HEADER_STYLES,
  ExcelCellStyle,
  BRAND_COLORS,
} from "../excelStyles";

// --- UTILITY FUNCTIONS ---

export const applyCellStyle = (ws: XLSX.WorkSheet, ref: string, style: any) => {
  if (!ws[ref]) ws[ref] = { t: "s", v: "" };
  ws[ref].s = style;
};

export const mergeCells = (ws: XLSX.WorkSheet, range: string) => {
  if (!ws["!merges"]) ws["!merges"] = [];
  ws["!merges"].push(XLSX.utils.decode_range(range));
};

export const setColumnWidths = (
  ws: XLSX.WorkSheet,
  widths: { wch: number }[]
) => {
  ws["!cols"] = widths;
};

export const setRowHeights = (
  ws: XLSX.WorkSheet,
  heights: { [row: number]: number }
) => {
  if (!ws["!rows"]) ws["!rows"] = [];
  Object.entries(heights).forEach(([row, hpt]) => {
    const rowIndex = parseInt(row) - 1;
    if (!ws["!rows"]![rowIndex]) ws["!rows"]![rowIndex] = {};
    ws["!rows"]![rowIndex].hpt = hpt;
  });
};

// --- COMMON TEMPLATE STYLING ---

export const applyHeaderStyling = (
  ws: XLSX.WorkSheet,
  title: string,
  subtitle: string,
  colCount: number
) => {
  // Apply main header styling
  applyCellStyle(ws, "A1", {
    ...CELL_STYLES.sheetTitle,
    font: { ...CELL_STYLES.sheetTitle.font, sz: 16, bold: true },
    fill: { fgColor: { rgb: BRAND_COLORS.primary } },
  });

  // Apply subtitle styling
  applyCellStyle(ws, "A2", {
    ...CELL_STYLES.sheetSubtitle,
    font: { ...CELL_STYLES.sheetSubtitle.font, sz: 12 },
  });

  // Merge header cells
  const lastCol = XLSX.utils.encode_col(colCount - 1);
  mergeCells(ws, `A1:${lastCol}1`);
  mergeCells(ws, `A2:${lastCol}2`);
};

export const applyColumnHeaderStyling = (
  ws: XLSX.WorkSheet,
  colCount: number,
  headerRow: number = 3
) => {
  // Style column headers
  for (let col = 0; col < colCount; col++) {
    const cellRef = XLSX.utils.encode_cell({ r: headerRow, c: col });
    applyCellStyle(ws, cellRef, {
      ...CELL_STYLES.tableHeader,
      fill: { fgColor: { rgb: BRAND_COLORS.secondary } },
    });
  }
};

export const setStandardRowHeights = (ws: XLSX.WorkSheet) => {
  setRowHeights(ws, {
    1: 25,
    2: 20,
    4: 20,
  });
};

// --- COMMON INSTRUCTION TEMPLATES ---

export const createInstructionsSheet = (
  workbook: XLSX.WorkBook,
  title: string,
  instructions: string[]
): void => {
  const instructionsData = [
    [title],
    [""],
    ...instructions.map((instruction) => [instruction]),
  ];

  const instructionsSheet = XLSX.utils.aoa_to_sheet(instructionsData);

  // Style instructions header
  applyCellStyle(instructionsSheet, "A1", {
    ...CELL_STYLES.reportTitle,
    font: { ...CELL_STYLES.reportTitle.font, sz: 14, bold: true },
    fill: { fgColor: { rgb: BRAND_COLORS.primary } },
  });

  setColumnWidths(instructionsSheet, [{ wch: 80 }]);
  setRowHeights(instructionsSheet, { 1: 25 });

  XLSX.utils.book_append_sheet(
    workbook,
    instructionsSheet,
    "Petunjuk Penggunaan"
  );
};

// --- COMMON INSTRUCTION SETS ---

export const getCommonInstructions = () => [
  "4. TIPS IMPORT:",
  "   • Hapus baris contoh sebelum import",
  "   • Pastikan tidak ada baris kosong di tengah data",
  "   • Simpan file dalam format .xlsx atau .xls",
  "   • Maksimal 1000 item per file import",
  "",
  "5. TROUBLESHOOTING:",
  "   • Jika import gagal, periksa format data",
  "   • Pastikan kolom wajib terisi",
  "   • Periksa ukuran file (maksimal 10MB)",
];

export const getDateFormatInstructions = () => [
  "   • Tanggal: YYYY-MM-DD (contoh: 2024-01-15)",
  "   • Harga: Masukkan angka tanpa titik/koma (contoh: 50000)",
  "   • Quantity: Masukkan angka bulat (contoh: 2)",
];

export const getContactFormatInstructions = () => [
  "   • Nama: Maksimal 255 karakter",
  "   • Email: Format email yang valid (contoh: <EMAIL>)",
  "   • Telepon: Format bebas (contoh: +62 xxx-xxxx-xxxx)",
  "   • Alamat: Maksimal 500 karakter",
];

// --- EXPORT STYLES AND CONSTANTS ---
export { CELL_STYLES, BRAND_COLORS, NUMBER_FORMATS, SHEET_HEADER_STYLES };
