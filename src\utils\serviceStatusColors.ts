import { ServiceStatus } from "@/components/pages/dashboard/services/types";

/**
 * Service Status Color Configuration
 * Provides consistent color schemes across the application for service statuses
 */

export interface ServiceStatusColorConfig {
  background: string;
  backgroundDark: string;
  text: string;
  textDark: string;
  border: string;
  icon: string;
  iconDark: string;
  badge: string;
  badgeDark: string;
}

export const SERVICE_STATUS_COLORS: Record<ServiceStatus, ServiceStatusColorConfig> = {
  [ServiceStatus.DITERIMA]: {
    background: "bg-blue-50",
    backgroundDark: "dark:bg-blue-900/20",
    text: "text-blue-700",
    textDark: "dark:text-blue-300",
    border: "border-blue-200",
    icon: "text-blue-500",
    iconDark: "dark:text-blue-400",
    badge: "bg-blue-100 text-blue-800",
    badgeDark: "dark:bg-blue-900/50 dark:text-blue-200",
  },
  [ServiceStatus.PROSES_MENUNGGU_SPAREPART]: {
    background: "bg-amber-50",
    backgroundDark: "dark:bg-amber-900/20",
    text: "text-amber-700",
    textDark: "dark:text-amber-300",
    border: "border-amber-200",
    icon: "text-amber-500",
    iconDark: "dark:text-amber-400",
    badge: "bg-amber-100 text-amber-800",
    badgeDark: "dark:bg-amber-900/50 dark:text-amber-200",
  },
  [ServiceStatus.SELESAI_BELUM_DIAMBIL]: {
    background: "bg-purple-50",
    backgroundDark: "dark:bg-purple-900/20",
    text: "text-purple-700",
    textDark: "dark:text-purple-300",
    border: "border-purple-200",
    icon: "text-purple-500",
    iconDark: "dark:text-purple-400",
    badge: "bg-purple-100 text-purple-800",
    badgeDark: "dark:bg-purple-900/50 dark:text-purple-200",
  },
  [ServiceStatus.SELESAI_SUDAH_DIAMBIL]: {
    background: "bg-green-50",
    backgroundDark: "dark:bg-green-900/20",
    text: "text-green-700",
    textDark: "dark:text-green-300",
    border: "border-green-200",
    icon: "text-green-500",
    iconDark: "dark:text-green-400",
    badge: "bg-green-100 text-green-800",
    badgeDark: "dark:bg-green-900/50 dark:text-green-200",
  },
};

/**
 * Get the complete color configuration for a service status
 */
export const getServiceStatusColors = (status: ServiceStatus): ServiceStatusColorConfig => {
  return SERVICE_STATUS_COLORS[status];
};

/**
 * Get the background classes for a service status
 */
export const getServiceStatusBackground = (status: ServiceStatus): string => {
  const colors = getServiceStatusColors(status);
  return `${colors.background} ${colors.backgroundDark}`;
};

/**
 * Get the text classes for a service status
 */
export const getServiceStatusText = (status: ServiceStatus): string => {
  const colors = getServiceStatusColors(status);
  return `${colors.text} ${colors.textDark}`;
};

/**
 * Get the icon classes for a service status
 */
export const getServiceStatusIcon = (status: ServiceStatus): string => {
  const colors = getServiceStatusColors(status);
  return `${colors.icon} ${colors.iconDark}`;
};

/**
 * Get the badge classes for a service status
 */
export const getServiceStatusBadge = (status: ServiceStatus): string => {
  const colors = getServiceStatusColors(status);
  return `${colors.badge} ${colors.badgeDark}`;
};

/**
 * Get the border classes for a service status
 */
export const getServiceStatusBorder = (status: ServiceStatus): string => {
  const colors = getServiceStatusColors(status);
  return colors.border;
};

/**
 * Get Tailwind CSS classes for select item styling
 */
export const getServiceStatusSelectItemClasses = (status: ServiceStatus): string => {
  const colors = getServiceStatusColors(status);
  return `hover:${colors.background} hover:${colors.backgroundDark} focus:${colors.background} focus:${colors.backgroundDark} ${colors.text} ${colors.textDark}`;
};
